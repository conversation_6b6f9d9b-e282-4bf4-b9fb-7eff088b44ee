# Pipecat Chatbot System

A complete voice chatbot system using Pipecat, Daily rooms, Deepgram STT, OpenAI GPT-4o-mini, and Azure TTS with extensive logging for debugging.

## 🏗️ Architecture

### Backend (Python)
- **Framework**: FastAPI server with Pipecat pipeline
- **Speech-to-Text**: Deepgram API
- **Language Model**: OpenAI GPT-4o-mini
- **Text-to-Speech**: Azure Speech Services
- **Video/Audio**: Daily rooms for WebRTC communication
- **Logging**: Extensive debug logging at each pipeline stage

### Frontend (React)
- **Framework**: React with Vite
- **UI**: Minimalistic interface with extensive event logging
- **Communication**: RTVI client for real-time voice interaction
- **Transport**: Daily transport for WebRTC

## 🚀 Quick Start

### 1. Start the Python Server
```bash
python server.py
```
Server will start on http://localhost:7860

### 2. Start the React Client
```bash
cd client
npm run dev
```
Client will start on http://localhost:5173

### 3. Test the System
Open http://localhost:5173 in your browser and click "Connect to Bot"

## 🧪 Testing

### Run All Tests
```bash
# Test Python server
python test_server.py

# Test React client
cd client && node test_client.js

# Test complete system
python test_complete_system.py
```

## 📁 Project Structure

```
├── server.py              # FastAPI server
├── bot.py                 # Pipecat bot implementation
├── requirements.txt       # Python dependencies
├── test_server.py         # Server tests
├── test_complete_system.py # End-to-end tests
├── .env                   # Environment variables
└── client/
    ├── package.json       # React dependencies
    ├── src/
    │   ├── App.jsx        # Main React component
    │   ├── App.css        # Styles
    │   └── main.jsx       # Entry point
    ├── index.html         # HTML template
    ├── vite.config.js     # Vite configuration
    └── test_client.js     # Client tests
```

## 🔧 Configuration

### Environment Variables (.env)
- `DAILY_API_KEY`: Daily.co API key
- `DAILY_SAMPLE_ROOM_URL`: Fixed room URL for development
- `OPENAI_API_KEY`: OpenAI API key
- `DEEPGRAM_API_KEY`: Deepgram API key
- `AZURE_SPEECH_API_KEY`: Azure Speech Services key
- `AZURE_SPEECH_REGION`: Azure region

## 🎯 Features

### Extensive Logging
- **Server**: Debug logs for each pipeline stage
- **Client**: Real-time event logging with categorization
- **Bot**: Frame-by-frame processing logs

### Minimalistic UI
- Clean, modern interface
- Real-time connection status
- Event log viewer with filtering
- Responsive design

### Robust Error Handling
- Comprehensive API testing
- Connection retry logic
- Graceful error recovery

## 🔍 Debugging

### Server Logs
Check the console output for detailed pipeline processing:
```
[After Transport Input] Processing frame: AudioRawFrame
[After STT] Processing frame: TranscriptionFrame
[After LLM] Processing frame: TextFrame
[After TTS] Processing frame: TTSAudioRawFrame
```

### Client Logs
Open browser developer tools to see RTVI events:
```
[RTVI] connected: {...}
[RTVI] botReady: {...}
[RTVI] userStartedSpeaking: {...}
```

## 🚨 Troubleshooting

### Common Issues

1. **Server won't start**
   - Check environment variables: `python test_server.py`
   - Verify API keys are valid

2. **Client can't connect**
   - Ensure server is running on port 7860
   - Check CORS settings

3. **No audio**
   - Check microphone permissions
   - Verify Daily room configuration

4. **Bot doesn't respond**
   - Check OpenAI API quota
   - Verify Azure TTS configuration

## 📊 System Status

All tests passing ✅:
- Environment Variables ✅
- Daily API ✅
- OpenAI API ✅
- Deepgram API ✅
- Azure Speech API ✅
- Server Startup ✅
- Client Build ✅
- Server Connectivity ✅
- Bot Creation ✅

## 🎮 Usage

1. Open http://localhost:5173
2. Click "Connect to Bot"
3. Allow microphone access when prompted
4. Start speaking to the bot
5. Monitor logs for debugging information

The bot will respond with voice using Azure TTS and the conversation will be logged in real-time for debugging purposes.
