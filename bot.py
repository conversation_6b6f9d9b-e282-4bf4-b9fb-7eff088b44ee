#
# Simple Pipecat Chatbot Bot
#

import argparse
import asyncio
import os
import sys
from typing import Tuple

import aiohttp
from dotenv import load_dotenv
from loguru import logger

from pipecat.audio.vad.silero import SileroVADAnalyzer
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineParams, PipelineTask
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.processors.frameworks.rtvi import RTVIConfig, RTVIObserver, RTVIProcessor
from pipecat.services.azure.tts import AzureTTSService
from pipecat.services.deepgram.stt import DeepgramSTTService
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.transports.services.daily import DailyParams, DailyTransport

# Configure logging
logger.remove()
logger.add(sys.stderr, level="DEBUG", format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
logger.add("bot.log", rotation="10 MB", level="DEBUG")

# Load environment variables
load_dotenv(override=True)

class LoggingProcessor:
    """A processor that logs all frames passing through it."""
    
    def __init__(self, name):
        self.name = name
    
    async def process(self, frame):
        logger.debug(f"[{self.name}] Processing frame: {frame.__class__.__name__}")
        return frame

async def configure(session: aiohttp.ClientSession) -> Tuple[str, str]:
    """Parse command line arguments to get room URL and token."""
    parser = argparse.ArgumentParser(description="Run the RTVI bot")
    parser.add_argument("-u", "--url", type=str, required=True, help="Daily room URL")
    parser.add_argument("-t", "--token", type=str, required=True, help="Daily room token")
    args = parser.parse_args()
    
    return args.url, args.token

async def main():
    """Main function to set up and run the bot pipeline."""
    async with aiohttp.ClientSession() as session:
        (room_url, token) = await configure(session)
        
        logger.info(f"Starting bot with room URL: {room_url}")
        
        # Set up Daily transport with audio parameters
        transport = DailyTransport(
            room_url,
            token,
            "AI Assistant",
            DailyParams(
                audio_in_enabled=True,
                audio_out_enabled=True,
                vad_analyzer=SileroVADAnalyzer(),
            ),
        )
        
        # Set up speech-to-text service
        stt = DeepgramSTTService(api_key=os.getenv("DEEPGRAM_API_KEY"))
        logger.info("Initialized Deepgram STT service")
        
        # Set up text-to-speech service
        tts = AzureTTSService(
            api_key=os.getenv("AZURE_API_KEY"),
            region=os.getenv("AZURE_REGION"),
            voice_name="en-US-JennyNeural",
        )
        logger.info("Initialized Azure TTS service")
        
        # Set up language model service
        llm = OpenAILLMService(
            api_key=os.getenv("OPENAI_API_KEY"),
            model="gpt-4o-mini",
            system_instruction=(
                "You are a helpful AI assistant. Respond concisely and clearly to the user's questions. "
                "Your responses will be spoken aloud, so keep them brief and conversational."
            ),
        )
        logger.info("Initialized OpenAI LLM service")
        
        # Set up conversation context
        messages = [
            {
                "role": "system",
                "content": (
                    "You are a helpful AI assistant. Respond concisely and clearly to the user's questions. "
                    "Your responses will be spoken aloud, so keep them brief and conversational."
                ),
            },
        ]
        
        context = OpenAILLMContext(messages)
        context_aggregator = llm.create_context_aggregator(context)
        
        # RTVI events for Pipecat client UI
        rtvi = RTVIProcessor(config=RTVIConfig(config=[]))
        
        # Build the pipeline with logging at each stage
        pipeline = Pipeline(
            [
                transport.input(),
                LoggingProcessor("After Transport Input"),
                stt,
                LoggingProcessor("After STT"),
                rtvi,
                LoggingProcessor("After RTVI"),
                context_aggregator.user(),
                LoggingProcessor("After Context Aggregator (User)"),
                llm,
                LoggingProcessor("After LLM"),
                tts,
                LoggingProcessor("After TTS"),
                transport.output(),
                LoggingProcessor("After Transport Output"),
                context_aggregator.assistant(),
            ]
        )
        
        task = PipelineTask(
            pipeline,
            params=PipelineParams(
                allow_interruptions=True,
                enable_metrics=True,
                enable_usage_metrics=True,
            ),
            observers=[RTVIObserver(rtvi)],
        )
        
        @transport.event_handler("on_first_participant_joined")
        async def on_first_participant_joined(transport, participant):
            logger.info(f"First participant joined: {participant['id']}")
            await transport.capture_participant_transcription(participant["id"])
            # Send a welcome message
            welcome_frame = context_aggregator.user().create_frame("Hello, welcome to the chat!")
            await task.queue_frames([welcome_frame])
        
        @transport.event_handler("on_participant_joined")
        async def on_participant_joined(transport, participant):
            logger.info(f"Participant joined: {participant['id']}")
        
        @transport.event_handler("on_participant_left")
        async def on_participant_left(transport, participant):
            logger.info(f"Participant left: {participant['id']}")
        
        @rtvi.event_handler("on_client_ready")
        async def on_client_ready(rtvi):
            logger.info("Client is ready")
            await rtvi.set_bot_ready()
        
        # Run the pipeline
        logger.info("Starting pipeline")
        runner = PipelineRunner()
        await runner.run(task)

if __name__ == "__main__":
    asyncio.run(main())