<html>
  <head>
    <title>daily custom tracks</title>
  </head>
  <script crossorigin src="https://unpkg.com/@daily-co/daily-js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fomantic-ui/2.8.6/semantic.min.js"></script>
  <link
    rel="stylesheet"
    type="text/css"
    href="https://cdnjs.cloudflare.com/ajax/libs/fomantic-ui/2.8.6/semantic.min.css"
    />
  <script>
    function enableButton(buttonId, enable) {
        const button = document.getElementById(buttonId);
        button.disabled = !enable;
    }

    function enableJoinButton(enable) {
        enableButton("join-button", enable);
    }

    function enableLeaveButton(enable) {
        enableButton("leave-button", enable);
    }

    function destroyPlayers(query) {
        const items = document.querySelectorAll(query);
        if (items) {
            for (const item of items) {
                item.remove();
            }
        }
    }

    function destroyParticipantPlayers(participantId) {
        destroyPlayers(`audio[data-participant-id="${participantId}"]`);
        destroyPlayers(`button[data-participant-id="${participantId}"]`);
    }

    async function startPlayer(player, track) {
        player.muted = false;
        player.autoplay = true;
        if (track != null) {
            player.srcObject = new MediaStream([track]);
        }
    }

    async function buildAudioPlayer(track, participantId) {
        const audioContainer = document.getElementById("audio-container");
        const player = document.createElement("audio");
        player.dataset.participantId = participantId;

        // Create a new button for controlling audio
        const audioControlButton = document.createElement("button");
        audioControlButton.className = "ui primary green button"
        audioControlButton.innerText = track._mediaTag == "cam-audio" ? "english" : track._mediaTag;
        audioControlButton.dataset.participantId = participantId;
        audioControlButton.onclick = () => {
            if (player.paused) {

                player.play();
                audioControlButton.className = "ui primary red button"
            } else {
                player.pause();
                audioControlButton.className = "ui primary green button"
            }
        };

        audioContainer.appendChild(player);
        audioContainer.appendChild(audioControlButton);

        await startPlayer(player, track);
        player.pause()

        return player;
    }

    function subscribeToTracks(participantId) {
        console.log(`subscribing to track`);

        if (participantId === "local") {
            return;
        }

        callObject.updateParticipant(participantId, {
            setSubscribedTracks: {
                audio: true,
                video: false,
                custom: true,
            },
        });
    }

    function startDaily() {
        enableJoinButton(true);
        enableLeaveButton(false);

        window.callObject = window.DailyIframe.createCallObject({});

        callObject.on("participant-joined", (e) => {
            if (!e.participant.local) {
                console.log("participant-joined", e.participant);
               subscribeToTracks(e.participant.session_id);
            }
        });

        callObject.on("participant-left", (e) => {
            console.log("participant-left", e.participant.session_id);
            destroyParticipantPlayers(e.participant.session_id);
        });

        callObject.on("track-started", async (e) => {
            console.log("track-started", e.track);
            if (e.track.kind === "audio") {
                await buildAudioPlayer(e.track, e.participant.session_id);
            }
        });
    }

    async function joinRoom() {
        enableJoinButton(false);
        enableLeaveButton(true);

        const meetingUrl = document.getElementById("meeting-url").value;

        callObject.join({
            url: meetingUrl,
            startVideoOff: true,
            startAudioOff: true,
            subscribeToTracksAutomatically: false,
            receiveSettings: {
                base: { video: { layer: 0 } },
            },
        });
    }

    async function leaveRoom() {
        enableJoinButton(true);
        enableLeaveButton(false);

        callObject.leave();

        const audioContainer = document.getElementById("audio-container");
        audioContainer.replaceChildren();
    }
  </script>

  <body onload="startDaily()">
    <div class="ui centered page grid" style="margin-top: 30px">
      <div class="ten wide column">
        <div class="ui form" style="margin-top: 30px">
          <div class="field">
            <label>Meeting URL</label>
            <input id="meeting-url" value="" />
          </div>
        </div>
      </div>
    </div>
    <div class="ui centered aligned header" style="margin-top: 30px">
      <button id="join-button" class="ui primary button" onclick="joinRoom()">
        Join
      </button>
      <button id="leave-button" class="ui button" onclick="leaveRoom()">
        Leave
      </button>
    </div>
    <div id="tile" class="ui container" style="margin-top: 30px">
      <div id="tile" class="ui center aligned grid">
        <div id="audio-container"></div><br/>
      </div>
    </div>
  </body>
</html>
