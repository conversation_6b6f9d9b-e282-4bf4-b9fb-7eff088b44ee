<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Chatbot</title>
  </head>

  <body>
    <div class="container">
      <div class="status-bar">
        <div class="status">
          Status: <span id="connection-status">Disconnected</span>
        </div>
        <div class="controls">
          <select id="bot-selector">
            <option value="openai">OpenAI</option>
            <option value="gemini">Gemini</option>
            <option value="vllm">Llama</option>
          </select>
          <button id="connect-btn">Connect</button>
          <button id="disconnect-btn" disabled>Disconnect</button>
        </div>
      </div>

      <div class="main-content">
        <div class="bot-container">
          <div id="bot-video-container"></div>
          <audio id="bot-audio" autoplay></audio>
        </div>
      </div>

      <div class="device-bar">
        <div class="device-controls">
          <select id="device-selector"></select>
          <button id="mic-toggle-btn">Mute Mic</button>
        </div>
      </div>

      <div class="debug-panel">
        <h3>Debug Info</h3>
        <div id="debug-log"></div>
      </div>
    </div>

    <script type="module" src="/src/app.js"></script>
    <link rel="stylesheet" href="/src/style.css" />
  </body>
</html>
