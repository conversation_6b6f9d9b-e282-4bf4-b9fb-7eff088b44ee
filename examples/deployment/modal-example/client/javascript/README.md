# JavaScript Implementation

Basic implementation using the [Pipecat JavaScript SDK](https://docs.pipecat.ai/client/js/introduction).

## Setup

1. Deploy the Modal server. See the main [README](../../README).

2. Navigate to the `client/javascript` directory:

```bash
cd client/javascript
```

3. Modify the baseUrl in src/app.js to point to your deployed Modal endpoint

4. Install dependencies:

```bash
npm install
```

5. Run the client app:

```
npm run dev
```

6. Visit http://localhost:5173 in your browser.
