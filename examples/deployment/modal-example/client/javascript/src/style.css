body {
  margin: 0;
  padding: 20px;
  font-family: Arial, sans-serif;
  background-color: #f0f0f0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.status-bar,
.device-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 20px;
}

.controls,
.device-controls {
  display: flex;
  align-items: center;
  gap: 10px; /* Adds spacing between elements */
}

.device-controls {
  margin-left: auto;
}

.controls button,
.device-controls button {
  padding: 8px 16px;
  margin-left: 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

#bot-selector,
#device-selector {
  padding: 8px 16px;
  padding-right: 40px;
  border: none;
  border-radius: 4px;
  background-color: #6c757d; /* Gray background */
  color: white; /* White text */
  cursor: pointer;
  appearance: none; /* Removes default browser styling for dropdowns */
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E"); /* Custom arrow */
  background-repeat: no-repeat;
  background-position: right 8px center; /* Position the arrow */
}

#bot-selector:focus,
#device-selector:focus {
  outline: none;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3); /* Add a subtle focus effect */
}

#connect-btn {
  background-color: #4caf50;
  color: white;
}

#disconnect-btn {
  background-color: #f44336;
  color: white;
}

#mic-toggle-btn {
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.main-content {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.bot-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

#bot-video-container {
  width: 640px;
  height: 360px;
  background-color: #e0e0e0;
  border-radius: 8px;
  margin: 20px auto;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

#bot-video-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.debug-panel {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
}

.debug-panel h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: bold;
}

#debug-log {
  height: 200px;
  overflow-y: auto;
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
}
