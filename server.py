#
# Simple Pipecat Chatbot Server
#

import argparse
import os
import subprocess
import sys
from contextlib import asynccontextmanager
from typing import Any, Dict, List, <PERSON>ple

import aiohttp
import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse
from loguru import logger

# Load environment variables
load_dotenv(override=True)

# Track bot processes
bot_procs = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: nothing to do
    yield
    # Shutdown: terminate all bot processes
    for pid, (proc, _) in bot_procs.items():
        logger.info(f"Terminating bot process {pid}")
        proc.terminate()

app = FastAPI(lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

async def create_room_and_token() -> <PERSON><PERSON>[str, str]:
    """Create a Daily room and token for the bot.
    
    Returns:
        Tuple[str, str]: Room URL and token
    
    Raises:
        HTTPException: If room creation or token generation fails
    """
    # Check if we have a fixed room URL for development
    sample_room_url = os.getenv("DAILY_SAMPLE_ROOM_URL")
    if sample_room_url:
        logger.info(f"Using sample room URL: {sample_room_url}")
        
        # Create a token for the sample room
        room_name = sample_room_url.split("/")[-1]
        
        async with aiohttp.ClientSession() as session:
            token_url = f"{os.getenv('DAILY_API_URL', 'https://api.daily.co/v1')}/meeting-tokens"
            headers = {"Authorization": f"Bearer {os.getenv('DAILY_API_KEY')}"}
            data = {
                "properties": {
                    "room_name": room_name,
                    "is_owner": True,
                }
            }
            
            async with session.post(token_url, json=data, headers=headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Failed to create token: {error_text}")
                    raise HTTPException(status_code=500, detail="Failed to create token")
                
                token_data = await response.json()
                return sample_room_url, token_data["token"]
    
    # Create a new room
    async with aiohttp.ClientSession() as session:
        room_url = f"{os.getenv('DAILY_API_URL', 'https://api.daily.co/v1')}/rooms"
        headers = {"Authorization": f"Bearer {os.getenv('DAILY_API_KEY')}"}
        data = {
            "properties": {
                "enable_chat": True,
                "enable_knocking": False,
                "enable_prejoin_ui": False,
                "enable_screenshare": True,
                "enable_video_processing_ui": True,
                "start_video_off": False,
                "start_audio_off": False,
            }
        }
        
        async with session.post(room_url, json=data, headers=headers) as response:
            if response.status != 200:
                error_text = await response.text()
                logger.error(f"Failed to create room: {error_text}")
                raise HTTPException(status_code=500, detail="Failed to create room")
            
            room_data = await response.json()
            room_url = room_data["url"]
            
            # Create a token for the room
            token_url = f"{os.getenv('DAILY_API_URL', 'https://api.daily.co/v1')}/meeting-tokens"
            data = {
                "properties": {
                    "room_name": room_data["name"],
                    "is_owner": True,
                }
            }
            
            async with session.post(token_url, json=data, headers=headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Failed to create token: {error_text}")
                    raise HTTPException(status_code=500, detail="Failed to create token")
                
                token_data = await response.json()
                return room_url, token_data["token"]

@app.get("/")
async def read_root():
    """Direct browser access, redirects to a Daily Prebuilt room."""
    logger.info("Creating room for direct browser access")
    room_url, token = await create_room_and_token()
    logger.info(f"Room URL: {room_url}")
    
    # Start the bot process
    try:
        proc = subprocess.Popen(
            [f"python3 -m bot -u {room_url} -t {token}"],
            shell=True,
            bufsize=1,
            cwd=os.path.dirname(os.path.abspath(__file__)),
        )
        bot_procs[proc.pid] = (proc, room_url)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start subprocess: {e}")
    
    return RedirectResponse(room_url)

@app.post("/connect")
async def rtvi_connect(request: Request) -> Dict[Any, Any]:
    """RTVI connect endpoint that creates a room and returns connection credentials.
    
    This endpoint is called by RTVI clients to establish a connection.
    
    Returns:
        Dict[Any, Any]: Authentication bundle containing room_url and token
    
    Raises:
        HTTPException: If room creation, token generation, or bot startup fails
    """
    logger.info("Creating room for RTVI connection")
    room_url, token = await create_room_and_token()
    logger.info(f"Room URL: {room_url}")
    
    # Start the bot process
    try:
        proc = subprocess.Popen(
            [f"python3 -m bot -u {room_url} -t {token}"],
            shell=True,
            bufsize=1,
            cwd=os.path.dirname(os.path.abspath(__file__)),
        )
        bot_procs[proc.pid] = (proc, room_url)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start subprocess: {e}")
    
    # Return the authentication bundle in format expected by DailyTransport
    return {"room_url": room_url, "token": token}

@app.get("/status/{pid}")
async def get_status(pid: int):
    """Get status of a specific bot process.
    
    Args:
        pid: Process ID of the bot
    
    Returns:
        Dict: Status information
    
    Raises:
        HTTPException: If the process is not found
    """
    if pid not in bot_procs:
        raise HTTPException(status_code=404, detail="Process not found")
    
    proc, room_url = bot_procs[pid]
    return {
        "pid": pid,
        "running": proc.poll() is None,
        "exit_code": proc.poll(),
        "room_url": room_url,
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run the RTVI bot server")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=7860, help="Port to bind to")
    args = parser.parse_args()
    
    logger.info(f"Starting server on {args.host}:{args.port}")
    uvicorn.run(app, host=args.host, port=args.port)