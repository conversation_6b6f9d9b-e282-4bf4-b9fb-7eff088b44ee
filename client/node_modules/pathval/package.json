{"name": "pathval", "description": "Object value retrieval given a string path", "homepage": "https://github.com/chaijs/pathval", "version": "1.1.1", "keywords": ["pathval", "value retrieval", "chai util"], "license": "MIT", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "files": ["index.js", "pathval.js"], "main": "./index.js", "repository": {"type": "git", "url": "git+ssh://**************/chaijs/pathval.git"}, "scripts": {"build": "browserify --standalone pathval -o pathval.js", "lint": "eslint --ignore-path .gitignore .", "lint:fix": "npm run lint -- --fix", "prepublish": "npm run build", "semantic-release": "semantic-release pre && npm publish && semantic-release post", "pretest": "npm run lint", "test": "npm run test:node && npm run test:browser && npm run upload-coverage", "test:browser": "karma start --singleRun=true", "test:node": "nyc mocha", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0"}, "config": {"ghooks": {"commit-msg": "validate-commit-msg"}}, "eslintConfig": {"extends": ["strict/es5"], "env": {"es6": true}, "globals": {"HTMLElement": false}, "rules": {"complexity": 0, "max-statements": 0}}, "devDependencies": {"browserify": "^17.0.0", "browserify-istanbul": "^3.0.1", "coveralls": "^3.1.0", "eslint": "^7.13.0", "eslint-config-strict": "^14.0.1", "eslint-plugin-filenames": "^1.3.2", "ghooks": "^2.0.4", "karma": "^5.2.3", "karma-browserify": "^7.0.0", "karma-coverage": "^2.0.3", "karma-mocha": "^2.0.1", "karma-phantomjs-launcher": "^1.0.4", "karma-sauce-launcher": "^4.3.3", "lcov-result-merger": "^3.1.0", "mocha": "^8.2.1", "nyc": "^15.1.0", "phantomjs-prebuilt": "^2.1.16", "semantic-release": "^17.2.2", "simple-assert": "^1.0.0", "travis-after-all": "^1.4.5", "validate-commit-msg": "^2.14.0"}, "engines": {"node": "*"}}