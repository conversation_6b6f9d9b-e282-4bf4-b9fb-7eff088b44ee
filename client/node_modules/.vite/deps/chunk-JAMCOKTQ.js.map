{"version": 3, "sources": ["../../kind-of/index.js", "../../shallow-clone/index.js", "../../isobject/index.js", "../../is-plain-object/index.js", "../../clone-deep/index.js", "../../events/events.js", "../../uuid/dist/esm-browser/regex.js", "../../uuid/dist/esm-browser/validate.js", "../../uuid/dist/esm-browser/parse.js", "../../uuid/dist/esm-browser/stringify.js", "../../uuid/dist/esm-browser/rng.js", "../../uuid/dist/esm-browser/v35.js", "../../uuid/dist/esm-browser/md5.js", "../../uuid/dist/esm-browser/v3.js", "../../uuid/dist/esm-browser/native.js", "../../uuid/dist/esm-browser/v4.js", "../../uuid/dist/esm-browser/sha1.js", "../../uuid/dist/esm-browser/v5.js", "../../@pipecat-ai/client-js/dist/client-js/src/index.ts", "../../@pipecat-ai/client-js/dist/client-js/src/actions.ts", "../../@pipecat-ai/client-js/dist/client-js/src/client.ts", "../../@pipecat-ai/client-js/dist/client-js/package.json", "../../@pipecat-ai/client-js/dist/client-js/src/decorators.ts", "../../@pipecat-ai/client-js/dist/client-js/src/errors.ts", "../../@pipecat-ai/client-js/dist/client-js/src/events.ts", "../../@pipecat-ai/client-js/dist/client-js/src/helpers/index.ts", "../../@pipecat-ai/client-js/dist/client-js/src/logger.ts", "../../@pipecat-ai/client-js/dist/client-js/src/messages.ts", "../../@pipecat-ai/client-js/dist/client-js/src/helpers/llm.ts", "../../@pipecat-ai/client-js/dist/client-js/src/transport.ts"], "sourcesContent": ["var toString = Object.prototype.toString;\n\nmodule.exports = function kindOf(val) {\n  if (val === void 0) return 'undefined';\n  if (val === null) return 'null';\n\n  var type = typeof val;\n  if (type === 'boolean') return 'boolean';\n  if (type === 'string') return 'string';\n  if (type === 'number') return 'number';\n  if (type === 'symbol') return 'symbol';\n  if (type === 'function') {\n    return isGeneratorFn(val) ? 'generatorfunction' : 'function';\n  }\n\n  if (isArray(val)) return 'array';\n  if (isBuffer(val)) return 'buffer';\n  if (isArguments(val)) return 'arguments';\n  if (isDate(val)) return 'date';\n  if (isError(val)) return 'error';\n  if (isRegexp(val)) return 'regexp';\n\n  switch (ctorName(val)) {\n    case 'Symbol': return 'symbol';\n    case 'Promise': return 'promise';\n\n    // Set, Map, WeakSet, WeakMap\n    case 'WeakMap': return 'weakmap';\n    case 'WeakSet': return 'weakset';\n    case 'Map': return 'map';\n    case 'Set': return 'set';\n\n    // 8-bit typed arrays\n    case 'Int8Array': return 'int8array';\n    case 'Uint8Array': return 'uint8array';\n    case 'Uint8ClampedArray': return 'uint8clampedarray';\n\n    // 16-bit typed arrays\n    case 'Int16Array': return 'int16array';\n    case 'Uint16Array': return 'uint16array';\n\n    // 32-bit typed arrays\n    case 'Int32Array': return 'int32array';\n    case 'Uint32Array': return 'uint32array';\n    case 'Float32Array': return 'float32array';\n    case 'Float64Array': return 'float64array';\n  }\n\n  if (isGeneratorObj(val)) {\n    return 'generator';\n  }\n\n  // Non-plain objects\n  type = toString.call(val);\n  switch (type) {\n    case '[object Object]': return 'object';\n    // iterators\n    case '[object Map Iterator]': return 'mapiterator';\n    case '[object Set Iterator]': return 'setiterator';\n    case '[object String Iterator]': return 'stringiterator';\n    case '[object Array Iterator]': return 'arrayiterator';\n  }\n\n  // other\n  return type.slice(8, -1).toLowerCase().replace(/\\s/g, '');\n};\n\nfunction ctorName(val) {\n  return typeof val.constructor === 'function' ? val.constructor.name : null;\n}\n\nfunction isArray(val) {\n  if (Array.isArray) return Array.isArray(val);\n  return val instanceof Array;\n}\n\nfunction isError(val) {\n  return val instanceof Error || (typeof val.message === 'string' && val.constructor && typeof val.constructor.stackTraceLimit === 'number');\n}\n\nfunction isDate(val) {\n  if (val instanceof Date) return true;\n  return typeof val.toDateString === 'function'\n    && typeof val.getDate === 'function'\n    && typeof val.setDate === 'function';\n}\n\nfunction isRegexp(val) {\n  if (val instanceof RegExp) return true;\n  return typeof val.flags === 'string'\n    && typeof val.ignoreCase === 'boolean'\n    && typeof val.multiline === 'boolean'\n    && typeof val.global === 'boolean';\n}\n\nfunction isGeneratorFn(name, val) {\n  return ctorName(name) === 'GeneratorFunction';\n}\n\nfunction isGeneratorObj(val) {\n  return typeof val.throw === 'function'\n    && typeof val.return === 'function'\n    && typeof val.next === 'function';\n}\n\nfunction isArguments(val) {\n  try {\n    if (typeof val.length === 'number' && typeof val.callee === 'function') {\n      return true;\n    }\n  } catch (err) {\n    if (err.message.indexOf('callee') !== -1) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * If you need to support Safari 5-7 (8-10 yr-old browser),\n * take a look at https://github.com/feross/is-buffer\n */\n\nfunction isBuffer(val) {\n  if (val.constructor && typeof val.constructor.isBuffer === 'function') {\n    return val.constructor.isBuffer(val);\n  }\n  return false;\n}\n", "/*!\n * shallow-clone <https://github.com/jonschlinkert/shallow-clone>\n *\n * Copyright (c) 2015-present, <PERSON>.\n * Released under the MIT License.\n */\n\n'use strict';\n\nconst valueOf = Symbol.prototype.valueOf;\nconst typeOf = require('kind-of');\n\nfunction clone(val, deep) {\n  switch (typeOf(val)) {\n    case 'array':\n      return val.slice();\n    case 'object':\n      return Object.assign({}, val);\n    case 'date':\n      return new val.constructor(Number(val));\n    case 'map':\n      return new Map(val);\n    case 'set':\n      return new Set(val);\n    case 'buffer':\n      return cloneBuffer(val);\n    case 'symbol':\n      return cloneSymbol(val);\n    case 'arraybuffer':\n      return cloneArrayBuffer(val);\n    case 'float32array':\n    case 'float64array':\n    case 'int16array':\n    case 'int32array':\n    case 'int8array':\n    case 'uint16array':\n    case 'uint32array':\n    case 'uint8clampedarray':\n    case 'uint8array':\n      return cloneTypedArray(val);\n    case 'regexp':\n      return cloneRegExp(val);\n    case 'error':\n      return Object.create(val);\n    default: {\n      return val;\n    }\n  }\n}\n\nfunction cloneRegExp(val) {\n  const flags = val.flags !== void 0 ? val.flags : (/\\w+$/.exec(val) || void 0);\n  const re = new val.constructor(val.source, flags);\n  re.lastIndex = val.lastIndex;\n  return re;\n}\n\nfunction cloneArrayBuffer(val) {\n  const res = new val.constructor(val.byteLength);\n  new Uint8Array(res).set(new Uint8Array(val));\n  return res;\n}\n\nfunction cloneTypedArray(val, deep) {\n  return new val.constructor(val.buffer, val.byteOffset, val.length);\n}\n\nfunction cloneBuffer(val) {\n  const len = val.length;\n  const buf = Buffer.allocUnsafe ? Buffer.allocUnsafe(len) : Buffer.from(len);\n  val.copy(buf);\n  return buf;\n}\n\nfunction cloneSymbol(val) {\n  return valueOf ? Object(valueOf.call(val)) : {};\n}\n\n/**\n * Expose `clone`\n */\n\nmodule.exports = clone;\n", "/*!\n * isobject <https://github.com/jonschlinkert/isobject>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\n'use strict';\n\nmodule.exports = function isObject(val) {\n  return val != null && typeof val === 'object' && Array.isArray(val) === false;\n};\n", "/*!\n * is-plain-object <https://github.com/jonschlinkert/is-plain-object>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\n'use strict';\n\nvar isObject = require('isobject');\n\nfunction isObjectObject(o) {\n  return isObject(o) === true\n    && Object.prototype.toString.call(o) === '[object Object]';\n}\n\nmodule.exports = function isPlainObject(o) {\n  var ctor,prot;\n\n  if (isObjectObject(o) === false) return false;\n\n  // If has modified constructor\n  ctor = o.constructor;\n  if (typeof ctor !== 'function') return false;\n\n  // If has modified prototype\n  prot = ctor.prototype;\n  if (isObjectObject(prot) === false) return false;\n\n  // If constructor does not have an Object-specific method\n  if (prot.hasOwnProperty('isPrototypeOf') === false) {\n    return false;\n  }\n\n  // Most likely a plain Object\n  return true;\n};\n", "'use strict';\n\n/**\n * Module dependenices\n */\n\nconst clone = require('shallow-clone');\nconst typeOf = require('kind-of');\nconst isPlainObject = require('is-plain-object');\n\nfunction cloneDeep(val, instanceClone) {\n  switch (typeOf(val)) {\n    case 'object':\n      return cloneObjectDeep(val, instanceClone);\n    case 'array':\n      return cloneArrayDeep(val, instanceClone);\n    default: {\n      return clone(val);\n    }\n  }\n}\n\nfunction cloneObjectDeep(val, instanceClone) {\n  if (typeof instanceClone === 'function') {\n    return instanceClone(val);\n  }\n  if (instanceClone || isPlainObject(val)) {\n    const res = new val.constructor();\n    for (let key in val) {\n      res[key] = cloneDeep(val[key], instanceClone);\n    }\n    return res;\n  }\n  return val;\n}\n\nfunction cloneArrayDeep(val, instanceClone) {\n  const res = new val.constructor(val.length);\n  for (let i = 0; i < val.length; i++) {\n    res[i] = cloneDeep(val[i], instanceClone);\n  }\n  return res;\n}\n\n/**\n * Expose `cloneDeep`\n */\n\nmodule.exports = cloneDeep;\n", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\nmodule.exports.once = once;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n      resolve([].slice.call(arguments));\n    };\n\n    eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });\n    }\n  });\n}\n\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\n\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}\n", "export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;", "import REGEX from './regex.js';\nfunction validate(uuid) {\n  return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;", "import validate from './validate.js';\nfunction parse(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n  var v;\n  var arr = new Uint8Array(16);\n\n  // Parse ########-....-....-....-............\n  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;\n  arr[1] = v >>> 16 & 0xff;\n  arr[2] = v >>> 8 & 0xff;\n  arr[3] = v & 0xff;\n\n  // Parse ........-####-....-....-............\n  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;\n  arr[5] = v & 0xff;\n\n  // Parse ........-....-####-....-............\n  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;\n  arr[7] = v & 0xff;\n\n  // Parse ........-....-....-####-............\n  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;\n  arr[9] = v & 0xff;\n\n  // Parse ........-....-....-....-############\n  // (Use \"/\" to avoid 32-bit truncation when bit-shifting high-order bytes)\n  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000 & 0xff;\n  arr[11] = v / 0x100000000 & 0xff;\n  arr[12] = v >>> 24 & 0xff;\n  arr[13] = v >>> 16 & 0xff;\n  arr[14] = v >>> 8 & 0xff;\n  arr[15] = v & 0xff;\n  return arr;\n}\nexport default parse;", "import validate from './validate.js';\n\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\nvar byteToHex = [];\nfor (var i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  //\n  // Note to future-self: No, you can't remove the `toLowerCase()` call.\n  // REF: https://github.com/uuidjs/uuid/pull/677#issuecomment-1757351351\n  return (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n  var uuid = unsafeStringify(arr, offset);\n  // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n  return uuid;\n}\nexport default stringify;", "// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\n\nvar getRandomValues;\nvar rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n  return getRandomValues(rnds8);\n}", "import { unsafeStringify } from './stringify.js';\nimport parse from './parse.js';\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n\n  var bytes = [];\n  for (var i = 0; i < str.length; ++i) {\n    bytes.push(str.charCodeAt(i));\n  }\n  return bytes;\n}\nexport var DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexport var URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexport default function v35(name, version, hashfunc) {\n  function generateUUID(value, namespace, buf, offset) {\n    var _namespace;\n    if (typeof value === 'string') {\n      value = stringToBytes(value);\n    }\n    if (typeof namespace === 'string') {\n      namespace = parse(namespace);\n    }\n    if (((_namespace = namespace) === null || _namespace === void 0 ? void 0 : _namespace.length) !== 16) {\n      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    }\n\n    // Compute hash of namespace and value, Per 4.3\n    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =\n    // hashfunc([...namespace, ... value])`\n    var bytes = new Uint8Array(16 + value.length);\n    bytes.set(namespace);\n    bytes.set(value, namespace.length);\n    bytes = hashfunc(bytes);\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n    if (buf) {\n      offset = offset || 0;\n      for (var i = 0; i < 16; ++i) {\n        buf[offset + i] = bytes[i];\n      }\n      return buf;\n    }\n    return unsafeStringify(bytes);\n  }\n\n  // Function#name is not settable on some platforms (#270)\n  try {\n    generateUUID.name = name;\n  } catch (err) {}\n\n  // For CommonJS default export support\n  generateUUID.DNS = DNS;\n  generateUUID.URL = URL;\n  return generateUUID;\n}", "/*\n * Browser-compatible JavaScript MD5\n *\n * Modification of JavaScript MD5\n * https://github.com/blueimp/JavaScript-MD5\n *\n * Copyright 2011, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n *\n * Based on\n * A JavaScript implementation of the RSA Data Security, Inc. MD5 Message\n * Digest Algorithm, as defined in RFC 1321.\n * Version 2.2 Copyright (C) <PERSON> 1999 - 2009\n * Other contributors: <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for more info.\n */\nfunction md5(bytes) {\n  if (typeof bytes === 'string') {\n    var msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = new Uint8Array(msg.length);\n    for (var i = 0; i < msg.length; ++i) {\n      bytes[i] = msg.charCodeAt(i);\n    }\n  }\n  return md5ToHexEncodedArray(wordsToMd5(bytesToWords(bytes), bytes.length * 8));\n}\n\n/*\n * Convert an array of little-endian words to an array of bytes\n */\nfunction md5ToHexEncodedArray(input) {\n  var output = [];\n  var length32 = input.length * 32;\n  var hexTab = '0123456789abcdef';\n  for (var i = 0; i < length32; i += 8) {\n    var x = input[i >> 5] >>> i % 32 & 0xff;\n    var hex = parseInt(hexTab.charAt(x >>> 4 & 0x0f) + hexTab.charAt(x & 0x0f), 16);\n    output.push(hex);\n  }\n  return output;\n}\n\n/**\n * Calculate output length with padding and bit length\n */\nfunction getOutputLength(inputLength8) {\n  return (inputLength8 + 64 >>> 9 << 4) + 14 + 1;\n}\n\n/*\n * Calculate the MD5 of an array of little-endian words, and a bit length.\n */\nfunction wordsToMd5(x, len) {\n  /* append padding */\n  x[len >> 5] |= 0x80 << len % 32;\n  x[getOutputLength(len) - 1] = len;\n  var a = 1732584193;\n  var b = -271733879;\n  var c = -1732584194;\n  var d = 271733878;\n  for (var i = 0; i < x.length; i += 16) {\n    var olda = a;\n    var oldb = b;\n    var oldc = c;\n    var oldd = d;\n    a = md5ff(a, b, c, d, x[i], 7, -680876936);\n    d = md5ff(d, a, b, c, x[i + 1], 12, -389564586);\n    c = md5ff(c, d, a, b, x[i + 2], 17, 606105819);\n    b = md5ff(b, c, d, a, x[i + 3], 22, -1044525330);\n    a = md5ff(a, b, c, d, x[i + 4], 7, -176418897);\n    d = md5ff(d, a, b, c, x[i + 5], 12, 1200080426);\n    c = md5ff(c, d, a, b, x[i + 6], 17, -1473231341);\n    b = md5ff(b, c, d, a, x[i + 7], 22, -45705983);\n    a = md5ff(a, b, c, d, x[i + 8], 7, 1770035416);\n    d = md5ff(d, a, b, c, x[i + 9], 12, -1958414417);\n    c = md5ff(c, d, a, b, x[i + 10], 17, -42063);\n    b = md5ff(b, c, d, a, x[i + 11], 22, -1990404162);\n    a = md5ff(a, b, c, d, x[i + 12], 7, 1804603682);\n    d = md5ff(d, a, b, c, x[i + 13], 12, -40341101);\n    c = md5ff(c, d, a, b, x[i + 14], 17, -1502002290);\n    b = md5ff(b, c, d, a, x[i + 15], 22, 1236535329);\n    a = md5gg(a, b, c, d, x[i + 1], 5, -165796510);\n    d = md5gg(d, a, b, c, x[i + 6], 9, -1069501632);\n    c = md5gg(c, d, a, b, x[i + 11], 14, 643717713);\n    b = md5gg(b, c, d, a, x[i], 20, -373897302);\n    a = md5gg(a, b, c, d, x[i + 5], 5, -701558691);\n    d = md5gg(d, a, b, c, x[i + 10], 9, 38016083);\n    c = md5gg(c, d, a, b, x[i + 15], 14, -660478335);\n    b = md5gg(b, c, d, a, x[i + 4], 20, -405537848);\n    a = md5gg(a, b, c, d, x[i + 9], 5, 568446438);\n    d = md5gg(d, a, b, c, x[i + 14], 9, -1019803690);\n    c = md5gg(c, d, a, b, x[i + 3], 14, -187363961);\n    b = md5gg(b, c, d, a, x[i + 8], 20, 1163531501);\n    a = md5gg(a, b, c, d, x[i + 13], 5, -1444681467);\n    d = md5gg(d, a, b, c, x[i + 2], 9, -51403784);\n    c = md5gg(c, d, a, b, x[i + 7], 14, 1735328473);\n    b = md5gg(b, c, d, a, x[i + 12], 20, -1926607734);\n    a = md5hh(a, b, c, d, x[i + 5], 4, -378558);\n    d = md5hh(d, a, b, c, x[i + 8], 11, -2022574463);\n    c = md5hh(c, d, a, b, x[i + 11], 16, 1839030562);\n    b = md5hh(b, c, d, a, x[i + 14], 23, -35309556);\n    a = md5hh(a, b, c, d, x[i + 1], 4, -1530992060);\n    d = md5hh(d, a, b, c, x[i + 4], 11, 1272893353);\n    c = md5hh(c, d, a, b, x[i + 7], 16, -155497632);\n    b = md5hh(b, c, d, a, x[i + 10], 23, -1094730640);\n    a = md5hh(a, b, c, d, x[i + 13], 4, 681279174);\n    d = md5hh(d, a, b, c, x[i], 11, -358537222);\n    c = md5hh(c, d, a, b, x[i + 3], 16, -722521979);\n    b = md5hh(b, c, d, a, x[i + 6], 23, 76029189);\n    a = md5hh(a, b, c, d, x[i + 9], 4, -640364487);\n    d = md5hh(d, a, b, c, x[i + 12], 11, -421815835);\n    c = md5hh(c, d, a, b, x[i + 15], 16, 530742520);\n    b = md5hh(b, c, d, a, x[i + 2], 23, -995338651);\n    a = md5ii(a, b, c, d, x[i], 6, -198630844);\n    d = md5ii(d, a, b, c, x[i + 7], 10, 1126891415);\n    c = md5ii(c, d, a, b, x[i + 14], 15, -1416354905);\n    b = md5ii(b, c, d, a, x[i + 5], 21, -57434055);\n    a = md5ii(a, b, c, d, x[i + 12], 6, 1700485571);\n    d = md5ii(d, a, b, c, x[i + 3], 10, -1894986606);\n    c = md5ii(c, d, a, b, x[i + 10], 15, -1051523);\n    b = md5ii(b, c, d, a, x[i + 1], 21, -2054922799);\n    a = md5ii(a, b, c, d, x[i + 8], 6, 1873313359);\n    d = md5ii(d, a, b, c, x[i + 15], 10, -30611744);\n    c = md5ii(c, d, a, b, x[i + 6], 15, -1560198380);\n    b = md5ii(b, c, d, a, x[i + 13], 21, 1309151649);\n    a = md5ii(a, b, c, d, x[i + 4], 6, -145523070);\n    d = md5ii(d, a, b, c, x[i + 11], 10, -1120210379);\n    c = md5ii(c, d, a, b, x[i + 2], 15, 718787259);\n    b = md5ii(b, c, d, a, x[i + 9], 21, -343485551);\n    a = safeAdd(a, olda);\n    b = safeAdd(b, oldb);\n    c = safeAdd(c, oldc);\n    d = safeAdd(d, oldd);\n  }\n  return [a, b, c, d];\n}\n\n/*\n * Convert an array bytes to an array of little-endian words\n * Characters >255 have their high-byte silently ignored.\n */\nfunction bytesToWords(input) {\n  if (input.length === 0) {\n    return [];\n  }\n  var length8 = input.length * 8;\n  var output = new Uint32Array(getOutputLength(length8));\n  for (var i = 0; i < length8; i += 8) {\n    output[i >> 5] |= (input[i / 8] & 0xff) << i % 32;\n  }\n  return output;\n}\n\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */\nfunction safeAdd(x, y) {\n  var lsw = (x & 0xffff) + (y & 0xffff);\n  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return msw << 16 | lsw & 0xffff;\n}\n\n/*\n * Bitwise rotate a 32-bit number to the left.\n */\nfunction bitRotateLeft(num, cnt) {\n  return num << cnt | num >>> 32 - cnt;\n}\n\n/*\n * These functions implement the four basic operations the algorithm uses.\n */\nfunction md5cmn(q, a, b, x, s, t) {\n  return safeAdd(bitRotateLeft(safeAdd(safeAdd(a, q), safeAdd(x, t)), s), b);\n}\nfunction md5ff(a, b, c, d, x, s, t) {\n  return md5cmn(b & c | ~b & d, a, b, x, s, t);\n}\nfunction md5gg(a, b, c, d, x, s, t) {\n  return md5cmn(b & d | c & ~d, a, b, x, s, t);\n}\nfunction md5hh(a, b, c, d, x, s, t) {\n  return md5cmn(b ^ c ^ d, a, b, x, s, t);\n}\nfunction md5ii(a, b, c, d, x, s, t) {\n  return md5cmn(c ^ (b | ~d), a, b, x, s, t);\n}\nexport default md5;", "import v35 from './v35.js';\nimport md5 from './md5.js';\nvar v3 = v35('v3', 0x30, md5);\nexport default v3;", "var randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default {\n  randomUUID\n};", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n  if (native.randomUUID && !buf && !options) {\n    return native.randomUUID();\n  }\n  options = options || {};\n  var rnds = options.random || (options.rng || rng)();\n\n  // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80;\n\n  // Copy bytes to buffer, if provided\n  if (buf) {\n    offset = offset || 0;\n    for (var i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n    return buf;\n  }\n  return unsafeStringify(rnds);\n}\nexport default v4;", "// Adapted from <PERSON>' SHA1 code at\n// http://www.movable-type.co.uk/scripts/sha1.html\nfunction f(s, x, y, z) {\n  switch (s) {\n    case 0:\n      return x & y ^ ~x & z;\n    case 1:\n      return x ^ y ^ z;\n    case 2:\n      return x & y ^ x & z ^ y & z;\n    case 3:\n      return x ^ y ^ z;\n  }\n}\nfunction ROTL(x, n) {\n  return x << n | x >>> 32 - n;\n}\nfunction sha1(bytes) {\n  var K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  var H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n  if (typeof bytes === 'string') {\n    var msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = [];\n    for (var i = 0; i < msg.length; ++i) {\n      bytes.push(msg.charCodeAt(i));\n    }\n  } else if (!Array.isArray(bytes)) {\n    // Convert Array-like to Array\n    bytes = Array.prototype.slice.call(bytes);\n  }\n  bytes.push(0x80);\n  var l = bytes.length / 4 + 2;\n  var N = Math.ceil(l / 16);\n  var M = new Array(N);\n  for (var _i = 0; _i < N; ++_i) {\n    var arr = new Uint32Array(16);\n    for (var j = 0; j < 16; ++j) {\n      arr[j] = bytes[_i * 64 + j * 4] << 24 | bytes[_i * 64 + j * 4 + 1] << 16 | bytes[_i * 64 + j * 4 + 2] << 8 | bytes[_i * 64 + j * 4 + 3];\n    }\n    M[_i] = arr;\n  }\n  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n  for (var _i2 = 0; _i2 < N; ++_i2) {\n    var W = new Uint32Array(80);\n    for (var t = 0; t < 16; ++t) {\n      W[t] = M[_i2][t];\n    }\n    for (var _t = 16; _t < 80; ++_t) {\n      W[_t] = ROTL(W[_t - 3] ^ W[_t - 8] ^ W[_t - 14] ^ W[_t - 16], 1);\n    }\n    var a = H[0];\n    var b = H[1];\n    var c = H[2];\n    var d = H[3];\n    var e = H[4];\n    for (var _t2 = 0; _t2 < 80; ++_t2) {\n      var s = Math.floor(_t2 / 20);\n      var T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[_t2] >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n    H[0] = H[0] + a >>> 0;\n    H[1] = H[1] + b >>> 0;\n    H[2] = H[2] + c >>> 0;\n    H[3] = H[3] + d >>> 0;\n    H[4] = H[4] + e >>> 0;\n  }\n  return [H[0] >> 24 & 0xff, H[0] >> 16 & 0xff, H[0] >> 8 & 0xff, H[0] & 0xff, H[1] >> 24 & 0xff, H[1] >> 16 & 0xff, H[1] >> 8 & 0xff, H[1] & 0xff, H[2] >> 24 & 0xff, H[2] >> 16 & 0xff, H[2] >> 8 & 0xff, H[2] & 0xff, H[3] >> 24 & 0xff, H[3] >> 16 & 0xff, H[3] >> 8 & 0xff, H[3] & 0xff, H[4] >> 24 & 0xff, H[4] >> 16 & 0xff, H[4] >> 8 & 0xff, H[4] & 0xff];\n}\nexport default sha1;", "import v35 from './v35.js';\nimport sha1 from './sha1.js';\nvar v5 = v35('v5', 0x50, sha1);\nexport default v5;", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nexport * from \"./actions\";\nexport * from \"./client\";\nexport * from \"./errors\";\nexport * from \"./events\";\nexport * from \"./helpers\";\nexport * from \"./helpers/llm\";\nexport * from \"./logger\";\nexport * from \"./messages\";\nexport * from \"./transport\";\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport { logger, RTVIClientParams, RTVIError } from \".\";\nimport { RTVIActionRequest, RTVIActionResponse } from \"./messages\";\n\nexport async function httpActionGenerator(\n  actionUrl: string,\n  action: RTVIActionRequest,\n  params: RTVIClientParams,\n  handleResponse: (response: RTVIActionResponse) => void\n): Promise<void> {\n  try {\n    logger.debug(\"[RTVI] Fetch action\", actionUrl, action);\n\n    const headers = new Headers({\n      ...Object.fromEntries((params.headers ?? new Headers()).entries()),\n    });\n\n    if (!headers.has(\"Content-Type\")) {\n      headers.set(\"Content-Type\", \"application/json\");\n    }\n    headers.set(\"Cache-Control\", \"no-cache\");\n    headers.set(\"Connection\", \"keep-alive\");\n\n    // Perform the fetch request\n    const response = await fetch(actionUrl, {\n      method: \"POST\",\n      headers,\n      body: JSON.stringify({ ...params.requestData, actions: [action] }),\n    });\n\n    // Check the response content type\n    const contentType = response.headers.get(\"content-type\");\n\n    // Handle non-ok response status\n    if (!response.ok) {\n      const errorMessage = await response.text();\n      throw new RTVIError(\n        `Failed to resolve action: ${errorMessage}`,\n        response.status\n      );\n    }\n\n    if (response.body && contentType?.includes(\"text/event-stream\")) {\n      // Parse streamed responses\n      const reader = response.body\n        .pipeThrough(new TextDecoderStream())\n        .getReader();\n\n      let buffer = \"\";\n\n      while (true) {\n        const { value, done } = await reader.read();\n        if (done) break;\n\n        buffer += value;\n\n        let boundary = buffer.indexOf(\"\\n\\n\");\n        while (boundary !== -1) {\n          const message = buffer.slice(0, boundary);\n          buffer = buffer.slice(boundary + 2);\n\n          // Split on the first \":\" to extract the JSON part\n          const lines = message.split(\"\\n\");\n          let encodedData = \"\";\n          for (const line of lines) {\n            const colonIndex = line.indexOf(\":\");\n            if (colonIndex !== -1) {\n              encodedData += line.slice(colonIndex + 1).trim();\n            }\n          }\n\n          try {\n            const jsonData = atob(encodedData);\n            const parsedData = JSON.parse(jsonData);\n            handleResponse(parsedData);\n          } catch (error) {\n            logger.error(\"[RTVI] Failed to parse JSON:\", error);\n            throw error;\n          }\n\n          boundary = buffer.indexOf(\"\\n\\n\");\n        }\n      }\n    } else {\n      // For regular non-streamed responses, parse and handle the data as JSON\n      const data = await response.json();\n      handleResponse(data);\n    }\n  } catch (error) {\n    logger.error(\"[RTVI] Error during fetch:\", error);\n    throw error;\n  }\n}\n/*\n//@TODO: implement abortController when mode changes / bad things happen\nexport async function dispatchAction(\n  this: RTVIClient,\n  action: RTVIActionRequest\n): Promise<RTVIActionResponse> {\n  const promise = new Promise((resolve, reject) => {\n    (async () => {\n      if (this.connected) {\n        return this._messageDispatcher.dispatch(action);\n      } else {\n        const actionUrl = this.constructUrl(\"action\");\n        try {\n          const result = await httpActionGenerator(\n            actionUrl,\n            action,\n            this.params,\n            (response) => {\n              this.handleMessage(response);\n            }\n          );\n          resolve(result);\n        } catch (error) {\n          reject(error);\n        }\n      }\n    })();\n  });\n\n  return promise as Promise<RTVIActionResponse>;\n}\n*/\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport cloneDeep from \"clone-deep\";\nimport EventEmitter from \"events\";\nimport TypedEmitter from \"typed-emitter\";\n\nimport packageJson from \"../package.json\";\nimport { getIfTransportInState, transportReady } from \"./decorators\";\nimport * as RTVIErrors from \"./errors\";\nimport { RTVIEvent, RTVIEvents } from \"./events\";\nimport { RTVIClientHelper, RTVIClientHelpers } from \"./helpers\";\nimport { logger, LogLevel } from \"./logger\";\nimport {\n  BotLLMSearchResponseData,\n  BotLLMTextData,\n  BotReadyData,\n  BotTTSTextData,\n  ConfigData,\n  ErrorData,\n  MessageDispatcher,\n  PipecatMetricsData,\n  RTVIActionRequest,\n  RTVIActionRequestData,\n  RTVIActionResponse,\n  RTVIMessage,\n  RTVIMessageType,\n  StorageItemStoredData,\n  TranscriptData,\n} from \"./messages\";\nimport { Participant, Tracks, Transport, TransportState } from \"./transport\";\n\nexport type ConfigOption = {\n  name: string;\n  value: unknown;\n};\n\nexport type RTVIClientConfigOption = {\n  service: string;\n  options: ConfigOption[];\n};\n\nexport type RTVIURLEndpoints = \"connect\" | \"action\";\n\nconst defaultEndpoints: Record<RTVIURLEndpoints, string | null> = {\n  connect: \"/connect\",\n  action: \"/action\",\n};\n\nclass TransportWrapper {\n  private _transport: Transport;\n  private _proxy: Transport;\n\n  constructor(transport: Transport) {\n    this._transport = transport;\n    this._proxy = new Proxy(this._transport, {\n      get: (target, prop, receiver) => {\n        if (typeof target[prop as keyof Transport] === \"function\") {\n          let errMsg;\n          switch (String(prop)) {\n            // Disable methods that modify the lifecycle of the call. These operations\n            // should be performed via the RTVI client in order to keep state in sync.\n            case \"initialize\":\n              errMsg = `Calls to initialize() are disabled and used internally by the RTVIClient`;\n              break;\n            case \"sendReadyMessage\":\n              errMsg = `Calls to sendReadyMessage() are disabled and used internally by the RTVIClient`;\n              break;\n            case \"connect\":\n              errMsg = `Calls to connect() are disabled. Please use RTVIClient.connect()`;\n              break;\n            case \"disconnect\":\n              errMsg = `Calls to disconnect() are disabled. Please use RTVIClient.disconnect()`;\n              break;\n          }\n          if (errMsg) {\n            return () => {\n              throw new Error(errMsg);\n            };\n          }\n          // Forward other method calls\n          return (...args: any[]) => {\n            return (target[prop as keyof Transport] as Function)(...args);\n          };\n        }\n        // Forward property access\n        return Reflect.get(target, prop, receiver);\n      },\n    });\n  }\n\n  get proxy(): Transport {\n    return this._proxy;\n  }\n}\n\nexport type RTVIClientParams = {\n  baseUrl?: string | null;\n} & Partial<{\n  headers?: Headers;\n  endpoints: Partial<Record<RTVIURLEndpoints, string | null>>;\n  requestData?: object;\n  config?: RTVIClientConfigOption[];\n}> & {\n    [key: string]: unknown;\n  };\n\nexport interface RTVIClientOptions {\n  /**\n   * Parameters passed as JSON stringified body params to all endpoints (e.g. connect)\n   */\n  params: RTVIClientParams;\n\n  /**\n   * Transport class for media streaming\n   */\n  transport: Transport;\n\n  /**\n   * Optional callback methods for RTVI events\n   */\n  callbacks?: RTVIEventCallbacks;\n\n  /**\n   * Handshake timeout\n   *\n   * How long should the client wait for the bot ready event (when authenticating / requesting an agent)\n   * Defaults to no timeout (undefined)\n   */\n  timeout?: number;\n\n  /**\n   * Enable user mic input\n   *\n   * Default to true\n   */\n  enableMic?: boolean;\n\n  /**\n   * Enable user cam input\n   *\n   * Default to false\n   */\n  enableCam?: boolean;\n\n  /**\n   * Custom start method handler for retrieving auth bundle for transport\n   * @param baseUrl\n   * @param params\n   * @param timeout\n   * @param abortController\n   * @returns Promise<void>\n   */\n  customConnectHandler?: (\n    params: RTVIClientParams,\n    timeout: ReturnType<typeof setTimeout> | undefined,\n    abortController: AbortController\n  ) => Promise<void>;\n}\n\nexport type RTVIEventCallbacks = Partial<{\n  onGenericMessage: (data: unknown) => void;\n  onMessageError: (message: RTVIMessage) => void;\n  onError: (message: RTVIMessage) => void;\n  onConnected: () => void;\n  onDisconnected: () => void;\n  onTransportStateChanged: (state: TransportState) => void;\n  onConfig: (config: RTVIClientConfigOption[]) => void;\n  onConfigDescribe: (configDescription: unknown) => void;\n  onActionsAvailable: (actions: unknown) => void;\n  onBotConnected: (participant: Participant) => void;\n  onBotReady: (botReadyData: BotReadyData) => void;\n  onBotDisconnected: (participant: Participant) => void;\n  onParticipantJoined: (participant: Participant) => void;\n  onParticipantLeft: (participant: Participant) => void;\n  onMetrics: (data: PipecatMetricsData) => void;\n\n  onAvailableCamsUpdated: (cams: MediaDeviceInfo[]) => void;\n  onAvailableMicsUpdated: (mics: MediaDeviceInfo[]) => void;\n  onAvailableSpeakersUpdated: (speakers: MediaDeviceInfo[]) => void;\n  onCamUpdated: (cam: MediaDeviceInfo) => void;\n  onMicUpdated: (mic: MediaDeviceInfo) => void;\n  onSpeakerUpdated: (speaker: MediaDeviceInfo) => void;\n  onTrackStarted: (track: MediaStreamTrack, participant?: Participant) => void;\n  onTrackStopped: (track: MediaStreamTrack, participant?: Participant) => void;\n  onScreenTrackStarted: (\n    track: MediaStreamTrack,\n    participant?: Participant\n  ) => void;\n  onScreenTrackStopped: (\n    track: MediaStreamTrack,\n    participant?: Participant\n  ) => void;\n  onScreenShareError: (errorMessage: string) => void;\n  onLocalAudioLevel: (level: number) => void;\n  onRemoteAudioLevel: (level: number, participant: Participant) => void;\n\n  onBotStartedSpeaking: () => void;\n  onBotStoppedSpeaking: () => void;\n  onUserStartedSpeaking: () => void;\n  onUserStoppedSpeaking: () => void;\n  onUserTranscript: (data: TranscriptData) => void;\n  onBotTranscript: (data: BotLLMTextData) => void;\n\n  onBotLlmText: (data: BotLLMTextData) => void;\n  onBotLlmStarted: () => void;\n  onBotLlmStopped: () => void;\n  onBotTtsText: (data: BotTTSTextData) => void;\n  onBotTtsStarted: () => void;\n  onBotTtsStopped: () => void;\n  onBotLlmSearchResponse: (data: BotLLMSearchResponseData) => void;\n\n  onStorageItemStored: (data: StorageItemStoredData) => void;\n\n  onServerMessage: (data: any) => void;\n}>;\n\nabstract class RTVIEventEmitter extends (EventEmitter as unknown as new () => TypedEmitter<RTVIEvents>) {}\n\nexport class RTVIClient extends RTVIEventEmitter {\n  public params: RTVIClientParams;\n  protected _options: RTVIClientOptions;\n  private _abortController: AbortController | undefined;\n  private _handshakeTimeout: ReturnType<typeof setTimeout> | undefined;\n  private _helpers: RTVIClientHelpers;\n  private _startResolve: ((value: unknown) => void) | undefined;\n  protected _transport: Transport;\n  protected _transportWrapper: TransportWrapper;\n  protected declare _messageDispatcher: MessageDispatcher;\n\n  constructor(options: RTVIClientOptions) {\n    super();\n\n    this.params = {\n      ...options.params,\n      endpoints: {\n        ...defaultEndpoints,\n        ...(options.params.endpoints ?? {}),\n      },\n    };\n\n    this._helpers = {};\n    this._transport = options.transport;\n    this._transportWrapper = new TransportWrapper(this._transport);\n\n    // Wrap transport callbacks with event triggers\n    // This allows for either functional callbacks or .on / .off event listeners\n    const wrappedCallbacks: RTVIEventCallbacks = {\n      ...options.callbacks,\n      onMessageError: (message: RTVIMessage) => {\n        options?.callbacks?.onMessageError?.(message);\n        this.emit(RTVIEvent.MessageError, message);\n      },\n      onError: (message: RTVIMessage) => {\n        options?.callbacks?.onError?.(message);\n        try {\n          this.emit(RTVIEvent.Error, message);\n        } catch (e) {\n          logger.debug(\"Could not emit error\", message);\n        }\n        const data = message.data as ErrorData;\n        if (data?.fatal) {\n          logger.error(\"Fatal error reported. Disconnecting...\");\n          this.disconnect();\n        }\n      },\n      onConnected: () => {\n        options?.callbacks?.onConnected?.();\n        this.emit(RTVIEvent.Connected);\n      },\n      onDisconnected: () => {\n        options?.callbacks?.onDisconnected?.();\n        this.emit(RTVIEvent.Disconnected);\n      },\n      onTransportStateChanged: (state: TransportState) => {\n        options?.callbacks?.onTransportStateChanged?.(state);\n        this.emit(RTVIEvent.TransportStateChanged, state);\n      },\n      onConfig: (config: RTVIClientConfigOption[]) => {\n        options?.callbacks?.onConfig?.(config);\n        this.emit(RTVIEvent.Config, config);\n      },\n      onConfigDescribe: (configDescription: unknown) => {\n        options?.callbacks?.onConfigDescribe?.(configDescription);\n        this.emit(RTVIEvent.ConfigDescribe, configDescription);\n      },\n      onActionsAvailable: (actionsAvailable: unknown) => {\n        options?.callbacks?.onActionsAvailable?.(actionsAvailable);\n        this.emit(RTVIEvent.ActionsAvailable, actionsAvailable);\n      },\n      onParticipantJoined: (p) => {\n        options?.callbacks?.onParticipantJoined?.(p);\n        this.emit(RTVIEvent.ParticipantConnected, p);\n      },\n      onParticipantLeft: (p) => {\n        options?.callbacks?.onParticipantLeft?.(p);\n        this.emit(RTVIEvent.ParticipantLeft, p);\n      },\n      onTrackStarted: (track, p) => {\n        options?.callbacks?.onTrackStarted?.(track, p);\n        this.emit(RTVIEvent.TrackStarted, track, p);\n      },\n      onTrackStopped: (track, p) => {\n        options?.callbacks?.onTrackStopped?.(track, p);\n        this.emit(RTVIEvent.TrackStopped, track, p);\n      },\n      onScreenTrackStarted: (track, p) => {\n        options?.callbacks?.onScreenTrackStarted?.(track, p);\n        this.emit(RTVIEvent.ScreenTrackStarted, track, p);\n      },\n      onScreenTrackStopped: (track, p) => {\n        options?.callbacks?.onScreenTrackStopped?.(track, p);\n        this.emit(RTVIEvent.ScreenTrackStopped, track, p);\n      },\n      onScreenShareError: (errorMessage) => {\n        options?.callbacks?.onScreenShareError?.(errorMessage);\n        this.emit(RTVIEvent.ScreenShareError, errorMessage);\n      },\n      onAvailableCamsUpdated: (cams) => {\n        options?.callbacks?.onAvailableCamsUpdated?.(cams);\n        this.emit(RTVIEvent.AvailableCamsUpdated, cams);\n      },\n      onAvailableMicsUpdated: (mics) => {\n        options?.callbacks?.onAvailableMicsUpdated?.(mics);\n        this.emit(RTVIEvent.AvailableMicsUpdated, mics);\n      },\n      onAvailableSpeakersUpdated: (speakers) => {\n        options?.callbacks?.onAvailableSpeakersUpdated?.(speakers);\n        this.emit(RTVIEvent.AvailableSpeakersUpdated, speakers);\n      },\n      onCamUpdated: (cam) => {\n        options?.callbacks?.onCamUpdated?.(cam);\n        this.emit(RTVIEvent.CamUpdated, cam);\n      },\n      onMicUpdated: (mic) => {\n        options?.callbacks?.onMicUpdated?.(mic);\n        this.emit(RTVIEvent.MicUpdated, mic);\n      },\n      onSpeakerUpdated: (speaker) => {\n        options?.callbacks?.onSpeakerUpdated?.(speaker);\n        this.emit(RTVIEvent.SpeakerUpdated, speaker);\n      },\n      onBotConnected: (p) => {\n        options?.callbacks?.onBotConnected?.(p);\n        this.emit(RTVIEvent.BotConnected, p);\n      },\n      onBotReady: (botReadyData: BotReadyData) => {\n        options?.callbacks?.onBotReady?.(botReadyData);\n        this.emit(RTVIEvent.BotReady, botReadyData);\n      },\n      onBotDisconnected: (p) => {\n        options?.callbacks?.onBotDisconnected?.(p);\n        this.emit(RTVIEvent.BotDisconnected, p);\n      },\n      onBotStartedSpeaking: () => {\n        options?.callbacks?.onBotStartedSpeaking?.();\n        this.emit(RTVIEvent.BotStartedSpeaking);\n      },\n      onBotStoppedSpeaking: () => {\n        options?.callbacks?.onBotStoppedSpeaking?.();\n        this.emit(RTVIEvent.BotStoppedSpeaking);\n      },\n      onRemoteAudioLevel: (level, p) => {\n        options?.callbacks?.onRemoteAudioLevel?.(level, p);\n        this.emit(RTVIEvent.RemoteAudioLevel, level, p);\n      },\n      onUserStartedSpeaking: () => {\n        options?.callbacks?.onUserStartedSpeaking?.();\n        this.emit(RTVIEvent.UserStartedSpeaking);\n      },\n      onUserStoppedSpeaking: () => {\n        options?.callbacks?.onUserStoppedSpeaking?.();\n        this.emit(RTVIEvent.UserStoppedSpeaking);\n      },\n      onLocalAudioLevel: (level) => {\n        options?.callbacks?.onLocalAudioLevel?.(level);\n        this.emit(RTVIEvent.LocalAudioLevel, level);\n      },\n      onUserTranscript: (data) => {\n        options?.callbacks?.onUserTranscript?.(data);\n        this.emit(RTVIEvent.UserTranscript, data);\n      },\n      onBotTranscript: (text) => {\n        options?.callbacks?.onBotTranscript?.(text);\n        this.emit(RTVIEvent.BotTranscript, text);\n      },\n      onBotLlmText: (text) => {\n        options?.callbacks?.onBotLlmText?.(text);\n        this.emit(RTVIEvent.BotLlmText, text);\n      },\n      onBotLlmStarted: () => {\n        options?.callbacks?.onBotLlmStarted?.();\n        this.emit(RTVIEvent.BotLlmStarted);\n      },\n      onBotLlmStopped: () => {\n        options?.callbacks?.onBotLlmStopped?.();\n        this.emit(RTVIEvent.BotLlmStopped);\n      },\n      onBotTtsText: (text) => {\n        options?.callbacks?.onBotTtsText?.(text);\n        this.emit(RTVIEvent.BotTtsText, text);\n      },\n      onBotTtsStarted: () => {\n        options?.callbacks?.onBotTtsStarted?.();\n        this.emit(RTVIEvent.BotTtsStarted);\n      },\n      onBotTtsStopped: () => {\n        options?.callbacks?.onBotTtsStopped?.();\n        this.emit(RTVIEvent.BotTtsStopped);\n      },\n      onStorageItemStored: (data) => {\n        options?.callbacks?.onStorageItemStored?.(data);\n        this.emit(RTVIEvent.StorageItemStored, data);\n      },\n    };\n\n    // Update options to reference wrapped callbacks and config defaults\n    this._options = {\n      ...options,\n      callbacks: wrappedCallbacks,\n      enableMic: options.enableMic ?? true,\n      enableCam: options.enableCam ?? false,\n    };\n\n    // Instantiate the transport class and bind message handler\n    this._initialize();\n\n    // Get package version number\n    logger.debug(\"[RTVI Client] Initialized\", this.version);\n  }\n\n  public constructUrl(endpoint: RTVIURLEndpoints): string {\n    if (!this.params.baseUrl) {\n      throw new RTVIErrors.RTVIError(\n        \"Base URL not set. Please set rtviClient.params.baseUrl\"\n      );\n    }\n    const baseUrl = this.params.baseUrl.replace(/\\/+$/, \"\");\n    return baseUrl + (this.params.endpoints?.[endpoint] ?? \"\");\n  }\n\n  public setLogLevel(level: LogLevel) {\n    logger.setLevel(level);\n  }\n\n  // ------ Transport methods\n\n  /**\n   * Initialize local media devices\n   */\n  public async initDevices() {\n    logger.debug(\"[RTVI Client] Initializing devices...\");\n    await this._transport.initDevices();\n  }\n\n  /**\n   * Connect the voice client session with chosen transport\n   * Call async (await) to handle errors\n   */\n  public async connect(): Promise<unknown> {\n    if (\n      [\"authenticating\", \"connecting\", \"connected\", \"ready\"].includes(\n        this._transport.state\n      )\n    ) {\n      throw new RTVIErrors.RTVIError(\n        \"Voice client has already been started. Please call disconnect() before starting again.\"\n      );\n    }\n\n    this._abortController = new AbortController();\n\n    // Establish transport session and await bot ready signal\n    return new Promise((resolve, reject) => {\n      (async () => {\n        this._startResolve = resolve;\n\n        if (this._transport.state === \"disconnected\") {\n          await this._transport.initDevices();\n        }\n\n        this._transport.state = \"authenticating\";\n\n        // Set a timer for the bot to enter a ready state, otherwise abort the attempt\n        if (this._options.timeout) {\n          this._handshakeTimeout = setTimeout(async () => {\n            this._abortController?.abort();\n            await this.disconnect();\n            this._transport.state = \"error\";\n            reject(new RTVIErrors.ConnectionTimeoutError());\n          }, this._options.timeout);\n        }\n\n        let authBundle: unknown;\n        const customConnectHandler = this._options.customConnectHandler;\n\n        logger.debug(\"[RTVI Client] Start params\", this.params);\n\n        this.params = {\n          ...this.params,\n          requestData: {\n            ...this.params.requestData,\n            rtvi_client_version: this.version,\n          },\n        };\n\n        if (!this.params.baseUrl && !this.params.endpoints?.connect) {\n          // If baseUrl and endpoints.connect are not set, bypass the handshake and connect directly\n          // This is useful with transports that do not require service side auth, especially in local development\n          // Note: this is not recommended for production use, see [docs link]\n          logger.debug(\n            \"[RTVI Client] Connecting directly (skipping handshake / auth)...\"\n          );\n          clearTimeout(this._handshakeTimeout);\n        } else {\n          const connectUrl = this.constructUrl(\"connect\");\n\n          logger.debug(\"[RTVI Client] Connecting...\", connectUrl);\n          logger.debug(\"[RTVI Client] Start params\", this.params);\n\n          try {\n            if (customConnectHandler) {\n              authBundle = await customConnectHandler(\n                this.params,\n                this._handshakeTimeout,\n                this._abortController!\n              );\n            } else {\n              authBundle = await fetch(connectUrl, {\n                method: \"POST\",\n                mode: \"cors\",\n                headers: new Headers({\n                  \"Content-Type\": \"application/json\",\n                  ...Object.fromEntries(\n                    (this.params.headers ?? new Headers()).entries()\n                  ),\n                }),\n                body: JSON.stringify({\n                  config: this.params.config,\n                  ...(this.params.services\n                    ? { services: this.params.services }\n                    : {}), // @deprecated - pass services through request data\n                  ...this.params.requestData,\n                }),\n                signal: this._abortController?.signal,\n              }).then((res) => {\n                clearTimeout(this._handshakeTimeout);\n\n                if (res.ok) {\n                  return res.json();\n                }\n\n                return Promise.reject(res);\n              });\n            }\n          } catch (e) {\n            clearTimeout(this._handshakeTimeout);\n            // Handle errors if the request was not aborted\n            if (this._abortController?.signal.aborted) {\n              return;\n            }\n            this._transport.state = \"error\";\n            if (e instanceof Response) {\n              const errorResp = await e.json();\n              reject(\n                new RTVIErrors.StartBotError(\n                  errorResp.info ?? errorResp.detail ?? e.statusText,\n                  e.status\n                )\n              );\n            } else {\n              reject(new RTVIErrors.StartBotError());\n            }\n            return;\n          }\n\n          logger.debug(\"[RTVI Client] Auth bundle received\", authBundle);\n        }\n        try {\n          await this._transport.connect(\n            authBundle,\n            this._abortController as AbortController\n          );\n          await this._transport.sendReadyMessage();\n        } catch (e) {\n          clearTimeout(this._handshakeTimeout);\n          this.disconnect();\n          reject(e);\n          return;\n        }\n      })();\n    });\n  }\n\n  /**\n   * Disconnect the voice client from the transport\n   * Reset / reinitialize transport and abort any pending requests\n   */\n  public async disconnect(): Promise<void> {\n    if (this._abortController) {\n      this._abortController.abort();\n    }\n\n    clearTimeout(this._handshakeTimeout);\n\n    await this._transport.disconnect();\n\n    this._messageDispatcher = new MessageDispatcher(this);\n  }\n\n  private _initialize() {\n    this._transport.initialize(this._options, this.handleMessage.bind(this));\n\n    // Create a new message dispatch queue for async message handling\n    this._messageDispatcher = new MessageDispatcher(this);\n  }\n\n  /**\n   * Get the current state of the transport\n   */\n  public get connected(): boolean {\n    return [\"connected\", \"ready\"].includes(this._transport.state);\n  }\n\n  public get transport(): Transport {\n    return this._transportWrapper.proxy;\n  }\n\n  public get state(): TransportState {\n    return this._transport.state;\n  }\n\n  public get version(): string {\n    return packageJson.version;\n  }\n\n  // ------ Device methods\n\n  public async getAllMics(): Promise<MediaDeviceInfo[]> {\n    return await this._transport.getAllMics();\n  }\n\n  public async getAllCams(): Promise<MediaDeviceInfo[]> {\n    return await this._transport.getAllCams();\n  }\n\n  public async getAllSpeakers(): Promise<MediaDeviceInfo[]> {\n    return await this._transport.getAllSpeakers();\n  }\n\n  public get selectedMic() {\n    return this._transport.selectedMic;\n  }\n\n  public get selectedCam() {\n    return this._transport.selectedCam;\n  }\n\n  public get selectedSpeaker() {\n    return this._transport.selectedSpeaker;\n  }\n\n  public updateMic(micId: string) {\n    this._transport.updateMic(micId);\n  }\n\n  public updateCam(camId: string) {\n    this._transport.updateCam(camId);\n  }\n\n  public updateSpeaker(speakerId: string) {\n    this._transport.updateSpeaker(speakerId);\n  }\n\n  public enableMic(enable: boolean) {\n    this._transport.enableMic(enable);\n  }\n\n  public get isMicEnabled(): boolean {\n    return this._transport.isMicEnabled;\n  }\n\n  public enableCam(enable: boolean) {\n    this._transport.enableCam(enable);\n  }\n\n  public get isCamEnabled(): boolean {\n    return this._transport.isCamEnabled;\n  }\n\n  public tracks(): Tracks {\n    return this._transport.tracks();\n  }\n\n  public enableScreenShare(enable: boolean) {\n    return this._transport.enableScreenShare(enable);\n  }\n\n  public get isSharingScreen(): boolean {\n    return this._transport.isSharingScreen;\n  }\n\n  // ------ Config methods\n\n  /**\n   * Request the bot to send the current configuration\n   * @returns Promise<RTVIClientConfigOption[]> - Promise that resolves with the bot's configuration\n   */\n  @transportReady\n  public async getConfig(): Promise<RTVIClientConfigOption[]> {\n    const configMsg = await this._messageDispatcher.dispatch(\n      RTVIMessage.getBotConfig()\n    );\n    return (configMsg.data as ConfigData).config as RTVIClientConfigOption[];\n  }\n\n  /**\n   * Update pipeline and services\n   * @param config - RTVIClientConfigOption[] partial object with the new configuration\n   * @param interrupt - boolean flag to interrupt the current pipeline, or wait until the next turn\n   * @returns Promise<RTVIMessage> - Promise that resolves with the updated configuration\n   */\n  @transportReady\n  public async updateConfig(\n    config: RTVIClientConfigOption[],\n    interrupt: boolean = false\n  ): Promise<RTVIMessage> {\n    logger.debug(\"[RTVI Client] Updating config\", config);\n    // Only send the partial config if the bot is ready to prevent\n    // potential racing conditions whilst pipeline is instantiating\n    return this._messageDispatcher.dispatch(\n      RTVIMessage.updateConfig(config, interrupt)\n    );\n  }\n\n  /**\n   * Request bot describe the current configuration options\n   * @returns Promise<unknown> - Promise that resolves with the bot's configuration description\n   */\n  @transportReady\n  public async describeConfig(): Promise<unknown> {\n    return this._messageDispatcher.dispatch(RTVIMessage.describeConfig());\n  }\n\n  /**\n   * Returns configuration options for specified service key\n   * @param serviceKey - Service name to get options for (e.g. \"llm\")\n   * @param config? - Optional RTVIClientConfigOption[] to query (vs. using remote config)\n   * @returns RTVIClientConfigOption | undefined - Configuration options array for the service with specified key or undefined\n   */\n  public async getServiceOptionsFromConfig(\n    serviceKey: string,\n    config?: RTVIClientConfigOption[]\n  ): Promise<RTVIClientConfigOption | undefined> {\n    if (!config && this.state !== \"ready\") {\n      throw new RTVIErrors.BotNotReadyError(\n        \"getServiceOptionsFromConfig called without config array before bot is ready\"\n      );\n    }\n\n    return Promise.resolve().then(async () => {\n      // Check if we have registered service with name service\n      if (!serviceKey) {\n        logger.debug(\"Target service name is required\");\n        return undefined;\n      }\n\n      const passedConfig: RTVIClientConfigOption[] =\n        config ?? (await this.getConfig());\n\n      // Find matching service name in the config and update the messages\n      const configServiceKey = passedConfig.find(\n        (config: RTVIClientConfigOption) => config.service === serviceKey\n      );\n\n      if (!configServiceKey) {\n        logger.debug(\n          \"No service with name \" + serviceKey + \" not found in config\"\n        );\n        return undefined;\n      }\n\n      // Return a new object, as to not mutate existing state\n      return configServiceKey;\n    });\n  }\n\n  /**\n   * Returns configuration option value (unknown) for specified service key and option name\n   * @param serviceKey - Service name to get options for (e.g. \"llm\")\n   * @optional option Name of option return from the config (e.g. \"model\")\n   * @returns Promise<unknown | undefined> - Service configuration option value or undefined\n   */\n  public async getServiceOptionValueFromConfig(\n    serviceKey: string,\n    option: string,\n    config?: RTVIClientConfigOption[]\n  ): Promise<unknown | undefined> {\n    const configServiceKey: RTVIClientConfigOption | undefined =\n      await this.getServiceOptionsFromConfig(serviceKey, config);\n\n    if (!configServiceKey) {\n      logger.debug(\"Service with name \" + serviceKey + \" not found in config\");\n      return undefined;\n    }\n\n    // Find matching option key in the service config\n    const optionValue: ConfigOption | undefined = configServiceKey.options.find(\n      (o: ConfigOption) => o.name === option\n    );\n\n    return optionValue ? (optionValue as ConfigOption).value : undefined;\n  }\n\n  private _updateOrAddOption(\n    existingOptions: ConfigOption[],\n    newOption: ConfigOption\n  ): ConfigOption[] {\n    const existingOptionIndex = existingOptions.findIndex(\n      (item) => item.name === newOption.name\n    );\n    if (existingOptionIndex !== -1) {\n      // Update existing option\n      return existingOptions.map((item, index) =>\n        index === existingOptionIndex\n          ? { ...item, value: newOption.value }\n          : item\n      );\n    } else {\n      // Add new option\n      return [\n        ...existingOptions,\n        { name: newOption.name, value: newOption.value },\n      ];\n    }\n  }\n\n  /**\n   * Returns config with updated option(s) for specified service key and option name\n   * Note: does not update current config, only returns a new object (call updateConfig to apply changes)\n   * @param serviceKey - Service name to get options for (e.g. \"llm\")\n   * @param option - Service name to get options for (e.g. \"model\")\n   * @param config - Optional RTVIClientConfigOption[] to update (vs. using current config)\n   * @returns Promise<RTVIClientConfigOption[] | undefined> - Configuration options array with updated option(s) or undefined\n   */\n  public async setServiceOptionInConfig(\n    serviceKey: string,\n    option: ConfigOption | ConfigOption[],\n    config?: RTVIClientConfigOption[]\n  ): Promise<RTVIClientConfigOption[] | undefined> {\n    const newConfig: RTVIClientConfigOption[] = cloneDeep(\n      config ?? (await this.getConfig())\n    );\n\n    const serviceOptions = await this.getServiceOptionsFromConfig(\n      serviceKey,\n      newConfig\n    );\n\n    if (!serviceOptions) {\n      logger.debug(\n        \"Service with name '\" + serviceKey + \"' not found in config\"\n      );\n      return newConfig;\n    }\n\n    const optionsArray = Array.isArray(option) ? option : [option];\n\n    for (const opt of optionsArray) {\n      const existingItem = newConfig.find(\n        (item) => item.service === serviceKey\n      );\n      const updatedOptions = existingItem\n        ? this._updateOrAddOption(existingItem.options, opt)\n        : [{ name: opt.name, value: opt.value }];\n\n      if (existingItem) {\n        existingItem.options = updatedOptions;\n      } else {\n        newConfig.push({ service: serviceKey, options: updatedOptions });\n      }\n    }\n\n    return newConfig;\n  }\n\n  /**\n   * Returns config object with updated properties from passed array.\n   * @param configOptions - Array of RTVIClientConfigOption[] to update\n   * @param config? - Optional RTVIClientConfigOption[] to update (vs. using current config)\n   * @returns Promise<RTVIClientConfigOption[]> - Configuration options\n   */\n  public async setConfigOptions(\n    configOptions: RTVIClientConfigOption[],\n    config?: RTVIClientConfigOption[]\n  ): Promise<RTVIClientConfigOption[]> {\n    let accumulator: RTVIClientConfigOption[] = cloneDeep(\n      config ?? (await this.getConfig())\n    );\n\n    for (const configOption of configOptions) {\n      accumulator =\n        (await this.setServiceOptionInConfig(\n          configOption.service,\n          configOption.options,\n          accumulator\n        )) || accumulator;\n    }\n    return accumulator;\n  }\n\n  // ------ Actions\n\n  /**\n   * Dispatch an action message to the bot or http single-turn endpoint\n   */\n  public async action(\n    action: RTVIActionRequestData\n  ): Promise<RTVIActionResponse> {\n    return this._messageDispatcher.dispatchAction(\n      new RTVIActionRequest(action),\n      this.handleMessage.bind(this)\n    );\n  }\n\n  /**\n   * Describe available / registered actions the bot has\n   * @returns Promise<unknown> - Promise that resolves with the bot's actions\n   */\n  @transportReady\n  public async describeActions(): Promise<unknown> {\n    return this._messageDispatcher.dispatch(RTVIMessage.describeActions());\n  }\n\n  // ------ Transport methods\n\n  /**\n   * Get the session expiry time for the transport session (if applicable)\n   * @returns number - Expiry time in milliseconds\n   */\n  @getIfTransportInState(\"connected\", \"ready\")\n  public get transportExpiry(): number | undefined {\n    return this._transport.expiry;\n  }\n\n  // ------ Messages\n\n  /**\n   * Directly send a message to the bot via the transport\n   * @param message - RTVIMessage object to send\n   */\n  @transportReady\n  public sendMessage(message: RTVIMessage): void {\n    this._transport.sendMessage(message);\n  }\n\n  /**\n   * Disconnects the bot, but keeps the session alive\n   */\n  @transportReady\n  public disconnectBot(): void {\n    this._transport.sendMessage(\n      new RTVIMessage(RTVIMessageType.DISCONNECT_BOT, {})\n    );\n  }\n\n  protected handleMessage(ev: RTVIMessage): void {\n    logger.debug(\"[RTVI Message]\", ev);\n\n    switch (ev.type) {\n      case RTVIMessageType.BOT_READY:\n        clearTimeout(this._handshakeTimeout);\n        this._startResolve?.(ev.data as BotReadyData);\n        this._options.callbacks?.onBotReady?.(ev.data as BotReadyData);\n        break;\n      case RTVIMessageType.CONFIG_AVAILABLE: {\n        this._messageDispatcher.resolve(ev);\n        this._options.callbacks?.onConfigDescribe?.(ev.data);\n        break;\n      }\n      case RTVIMessageType.CONFIG: {\n        const resp = this._messageDispatcher.resolve(ev);\n        this._options.callbacks?.onConfig?.((resp.data as ConfigData).config);\n        break;\n      }\n      case RTVIMessageType.ACTIONS_AVAILABLE: {\n        this._messageDispatcher.resolve(ev);\n        this._options.callbacks?.onActionsAvailable?.(ev.data);\n        break;\n      }\n      case RTVIMessageType.ACTION_RESPONSE: {\n        this._messageDispatcher.resolve(ev);\n        break;\n      }\n      case RTVIMessageType.ERROR_RESPONSE: {\n        const resp = this._messageDispatcher.reject(ev);\n        this._options.callbacks?.onMessageError?.(resp as RTVIMessage);\n        break;\n      }\n      case RTVIMessageType.ERROR:\n        this._options.callbacks?.onError?.(ev);\n        break;\n      case RTVIMessageType.USER_STARTED_SPEAKING:\n        this._options.callbacks?.onUserStartedSpeaking?.();\n        break;\n      case RTVIMessageType.USER_STOPPED_SPEAKING:\n        this._options.callbacks?.onUserStoppedSpeaking?.();\n        break;\n      case RTVIMessageType.BOT_STARTED_SPEAKING:\n        this._options.callbacks?.onBotStartedSpeaking?.();\n        break;\n      case RTVIMessageType.BOT_STOPPED_SPEAKING:\n        this._options.callbacks?.onBotStoppedSpeaking?.();\n        break;\n      case RTVIMessageType.USER_TRANSCRIPTION: {\n        const TranscriptData = ev.data as TranscriptData;\n        this._options.callbacks?.onUserTranscript?.(TranscriptData);\n        break;\n      }\n      case RTVIMessageType.BOT_TRANSCRIPTION: {\n        this._options.callbacks?.onBotTranscript?.(ev.data as BotLLMTextData);\n        break;\n      }\n      case RTVIMessageType.BOT_LLM_TEXT:\n        this._options.callbacks?.onBotLlmText?.(ev.data as BotLLMTextData);\n        break;\n      case RTVIMessageType.BOT_LLM_STARTED:\n        this._options.callbacks?.onBotLlmStarted?.();\n        break;\n      case RTVIMessageType.BOT_LLM_STOPPED:\n        this._options.callbacks?.onBotLlmStopped?.();\n        break;\n      case RTVIMessageType.BOT_TTS_TEXT:\n        this._options.callbacks?.onBotTtsText?.(ev.data as BotTTSTextData);\n        break;\n      case RTVIMessageType.BOT_TTS_STARTED:\n        this._options.callbacks?.onBotTtsStarted?.();\n        break;\n      case RTVIMessageType.BOT_TTS_STOPPED:\n        this._options.callbacks?.onBotTtsStopped?.();\n        break;\n      case RTVIMessageType.BOT_LLM_SEARCH_RESPONSE:\n        this._options.callbacks?.onBotLlmSearchResponse?.(\n          ev.data as BotLLMSearchResponseData\n        );\n        this.emit(\n          RTVIEvent.BotLlmSearchResponse,\n          ev.data as BotLLMSearchResponseData\n        );\n        break;\n      case RTVIMessageType.METRICS:\n        this.emit(RTVIEvent.Metrics, ev.data as PipecatMetricsData);\n        this._options.callbacks?.onMetrics?.(ev.data as PipecatMetricsData);\n        break;\n      case RTVIMessageType.STORAGE_ITEM_STORED:\n        this._options.callbacks?.onStorageItemStored?.(\n          ev.data as StorageItemStoredData\n        );\n        break;\n      case RTVIMessageType.SERVER_MESSAGE: {\n        this._options.callbacks?.onServerMessage?.(ev.data);\n        this.emit(RTVIEvent.ServerMessage, ev.data);\n        break;\n      }\n      default: {\n        let match: boolean = false;\n        // Pass message to registered helpers\n        for (const helper of Object.values(\n          this._helpers\n        ) as RTVIClientHelper[]) {\n          if (helper.getMessageTypes().includes(ev.type)) {\n            match = true;\n            helper.handleMessage(ev);\n          }\n        }\n        if (!match) {\n          this._options.callbacks?.onGenericMessage?.(ev.data);\n        }\n      }\n    }\n  }\n\n  // ------ Helpers\n\n  /**\n   * Register a new helper to the client\n   * This (optionally) provides a way to reference helpers directly\n   * from the client and use the event dispatcher\n   * @param service - Target service for this helper\n   * @param helper - Helper instance\n   * @returns RTVIClientHelper - Registered helper instance\n   */\n  public registerHelper(\n    service: string,\n    helper: RTVIClientHelper\n  ): RTVIClientHelper {\n    if (this._helpers[service]) {\n      throw new Error(`Helper with name '${service}' already registered`);\n    }\n\n    // Check helper is instance of RTVIClientHelper\n    if (!(helper instanceof RTVIClientHelper)) {\n      throw new Error(`Helper must be an instance of RTVIClientHelper`);\n    }\n\n    helper.service = service;\n    helper.client = this;\n\n    this._helpers[service] = helper;\n\n    return this._helpers[service];\n  }\n\n  public getHelper<T extends RTVIClientHelper>(service: string): T | undefined {\n    const helper = this._helpers[service];\n    if (!helper) {\n      logger.debug(`Helper targeting service '${service}' not found`);\n      return undefined;\n    }\n    return helper as T;\n  }\n\n  public unregisterHelper(service: string) {\n    if (!this._helpers[service]) {\n      return;\n    }\n    delete this._helpers[service];\n  }\n}\n", "{\n  \"name\": \"@pipecat-ai/client-js\",\n  \"version\": \"0.3.4\",\n  \"license\": \"BSD-2-Clause\",\n  \"main\": \"dist/index.js\",\n  \"module\": \"dist/index.module.js\",\n  \"types\": \"dist/index.d.ts\",\n  \"source\": \"src/index.ts\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"git+https://github.com/pipecat-ai/pipecat-client-web.git\"\n  },\n  \"files\": [\n    \"dist\",\n    \"package.json\",\n    \"README.md\"\n  ],\n  \"scripts\": {\n    \"build\": \"jest --silent && parcel build --no-cache\",\n    \"dev\": \"parcel watch\",\n    \"lint\": \"eslint src/ --report-unused-disable-directives --max-warnings 0\",\n    \"test\": \"jest\"\n  },\n  \"jest\": {\n    \"preset\": \"ts-jest\",\n    \"testEnvironment\": \"node\"\n  },\n  \"devDependencies\": {\n    \"@jest/globals\": \"^29.7.0\",\n    \"@types/clone-deep\": \"^4.0.4\",\n    \"@types/jest\": \"^29.5.12\",\n    \"@types/uuid\": \"^10.0.0\",\n    \"eslint\": \"^9.11.1\",\n    \"eslint-config-prettier\": \"^9.1.0\",\n    \"eslint-plugin-simple-import-sort\": \"^12.1.1\",\n    \"jest\": \"^29.7.0\",\n    \"ts-jest\": \"^29.2.5\"\n  },\n  \"dependencies\": {\n    \"@types/events\": \"^3.0.3\",\n    \"clone-deep\": \"^4.0.1\",\n    \"events\": \"^3.3.0\",\n    \"typed-emitter\": \"^2.1.0\",\n    \"uuid\": \"^10.0.0\"\n  }\n}\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport { RTVIClient } from \".\";\nimport { BotNotReadyError } from \"./errors\";\n\nexport function transportReady<T extends RTVIClient>(\n  _target: T,\n  propertyKey: string | symbol,\n  descriptor: PropertyDescriptor\n): PropertyDescriptor | void {\n  const originalMethod = descriptor.value;\n\n  descriptor.value = function (this: T, ...args: unknown[]) {\n    if (this.state === \"ready\") {\n      return originalMethod.apply(this, args);\n    } else {\n      throw new BotNotReadyError(\n        `Attempt to call ${propertyKey.toString()} when transport not in ready state. Await connect() first.`\n      );\n    }\n  };\n\n  return descriptor;\n}\nexport function transportInState<T extends RTVIClient>(states: string[]) {\n  return function (\n    _target: T,\n    propertyKey: string | symbol,\n    descriptor: PropertyDescriptor\n  ): PropertyDescriptor | void {\n    const originalMethod = descriptor.value;\n\n    descriptor.get = function (this: T, ...args: unknown[]) {\n      if (states.includes(this.state)) {\n        return originalMethod.apply(this, args);\n      } else {\n        throw new BotNotReadyError(\n          `Attempt to call ${propertyKey.toString()} when transport not in ${states}.`\n        );\n      }\n    };\n\n    return descriptor;\n  };\n}\n\nexport function getIfTransportInState<T extends RTVIClient>(\n  ...states: string[]\n) {\n  states = [\"ready\", ...states];\n\n  return function (\n    _target: T,\n    propertyKey: string | symbol,\n    descriptor: PropertyDescriptor\n  ): PropertyDescriptor | void {\n    const originalGetter = descriptor.get;\n\n    descriptor.get = function (this: T) {\n      if (states.includes(this.state)) {\n        return originalGetter?.apply(this);\n      } else {\n        throw new BotNotReadyError(\n          `Attempt to call ${propertyKey.toString()} when transport not in ${states}. Await connect() first.`\n        );\n      }\n    };\n\n    return descriptor;\n  };\n}\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nexport class RTVIError extends Error {\n  readonly status: number | undefined;\n\n  constructor(message?: string, status?: number | undefined) {\n    super(message);\n    this.status = status;\n  }\n}\n\nexport class ConnectionTimeoutError extends RTVIError {\n  constructor(message?: string | undefined) {\n    super(\n      message ??\n        \"<PERSON><PERSON> did not enter ready state within the specified timeout period.\"\n    );\n  }\n}\n\nexport class StartBotError extends RTVIError {\n  readonly error: string = \"invalid-request-error\";\n  constructor(message?: string | undefined, status?: number) {\n    super(\n      message ?? `Failed to connect / invalid auth bundle from base url`,\n      status ?? 500\n    );\n  }\n}\n\nexport class TransportStartError extends RTVIError {\n  constructor(message?: string | undefined) {\n    super(message ?? \"Unable to connect to transport\");\n  }\n}\n\nexport class BotNotReadyError extends RTVIError {\n  constructor(message?: string | undefined) {\n    super(\n      message ??\n        \"Attempt to call action on transport when not in 'ready' state.\"\n    );\n  }\n}\n\nexport class ConfigUpdateError extends RTVIError {\n  override readonly status = 400;\n  constructor(message?: string | undefined) {\n    super(message ?? \"Unable to update configuration\");\n  }\n}\n\nexport class ActionEndpointNotSetError extends RTVIError {\n  constructor(message?: string | undefined) {\n    super(message ?? \"Action endpoint is not set\");\n  }\n}\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport { RTVIClientConfigOption } from \".\";\nimport { LLMFunctionCallData } from \"./helpers/llm\";\nimport {\n  BotLLMTextData,\n  BotLLMSearchResponseData,\n  BotReadyData,\n  BotTTSTextData,\n  PipecatMetricsData,\n  RTVIMessage,\n  StorageItemStoredData,\n  TranscriptData,\n} from \"./messages\";\nimport { Participant, TransportState } from \"./transport\";\n\nexport enum RTVIEvent {\n  MessageError = \"messageError\",\n  Error = \"error\",\n\n  Connected = \"connected\",\n  Disconnected = \"disconnected\",\n  TransportStateChanged = \"transportStateChanged\",\n\n  Config = \"config\",\n  ConfigDescribe = \"configDescribe\",\n  ActionsAvailable = \"actionsAvailable\",\n\n  ParticipantConnected = \"participantConnected\",\n  ParticipantLeft = \"participantLeft\",\n  TrackStarted = \"trackStarted\",\n  TrackStopped = \"trackStopped\",\n  ScreenTrackStarted = \"screenTrackStarted\",\n  ScreenTrackStopped = \"screenTrackStopped\",\n  ScreenShareError = \"screenShareError\",\n\n  AvailableCamsUpdated = \"availableCamsUpdated\",\n  AvailableMicsUpdated = \"availableMicsUpdated\",\n  AvailableSpeakersUpdated = \"availableSpeakersUpdated\",\n  CamUpdated = \"camUpdated\",\n  MicUpdated = \"micUpdated\",\n  SpeakerUpdated = \"speakerUpdated\",\n\n  BotConnected = \"botConnected\",\n  BotReady = \"botReady\",\n  BotDisconnected = \"botDisconnected\",\n  BotStartedSpeaking = \"botStartedSpeaking\",\n  BotStoppedSpeaking = \"botStoppedSpeaking\",\n  RemoteAudioLevel = \"remoteAudioLevel\",\n\n  UserStartedSpeaking = \"userStartedSpeaking\",\n  UserStoppedSpeaking = \"userStoppedSpeaking\",\n  LocalAudioLevel = \"localAudioLevel\",\n\n  Metrics = \"metrics\",\n\n  UserTranscript = \"userTranscript\",\n  BotTranscript = \"botTranscript\",\n\n  BotLlmText = \"botLlmText\",\n  BotLlmStarted = \"botLlmStarted\",\n  BotLlmStopped = \"botLlmStopped\",\n\n  BotTtsText = \"botTtsText\",\n  BotTtsStarted = \"botTtsStarted\",\n  BotTtsStopped = \"botTtsStopped\",\n\n  LLMFunctionCall = \"llmFunctionCall\",\n  LLMFunctionCallStart = \"llmFunctionCallStart\",\n  LLMJsonCompletion = \"llmJsonCompletion\",\n\n  StorageItemStored = \"storageItemStored\",\n\n  BotLlmSearchResponse = \"botLlmSearchResponse\",\n  ServerMessage = \"serverMessage\",\n}\n\nexport type RTVIEvents = Partial<{\n  connected: () => void;\n  disconnected: () => void;\n  transportStateChanged: (state: TransportState) => void;\n\n  config: (config: RTVIClientConfigOption[]) => void;\n  configUpdated: (config: RTVIClientConfigOption[]) => void;\n  configDescribe: (configDescription: unknown) => void;\n  actionsAvailable: (actions: unknown) => void;\n\n  participantConnected: (participant: Participant) => void;\n  participantLeft: (participant: Participant) => void;\n  trackStarted: (track: MediaStreamTrack, participant?: Participant) => void;\n  trackStopped: (track: MediaStreamTrack, participant?: Participant) => void;\n  screenTrackStarted: (track: MediaStreamTrack, p?: Participant) => void;\n  screenTrackStopped: (track: MediaStreamTrack, p?: Participant) => void;\n  screenShareError: (errorMessage: string) => void;\n\n  availableCamsUpdated: (cams: MediaDeviceInfo[]) => void;\n  availableMicsUpdated: (mics: MediaDeviceInfo[]) => void;\n  availableSpeakersUpdated: (speakers: MediaDeviceInfo[]) => void;\n  camUpdated: (cam: MediaDeviceInfo) => void;\n  micUpdated: (mic: MediaDeviceInfo) => void;\n  speakerUpdated: (speaker: MediaDeviceInfo) => void;\n\n  botReady: (botData: BotReadyData) => void;\n  botConnected: (participant: Participant) => void;\n  botDisconnected: (participant: Participant) => void;\n  botStartedSpeaking: () => void;\n  botStoppedSpeaking: () => void;\n  remoteAudioLevel: (level: number, p: Participant) => void;\n\n  userStartedSpeaking: () => void;\n  userStoppedSpeaking: () => void;\n  localAudioLevel: (level: number) => void;\n\n  metrics: (data: PipecatMetricsData) => void;\n\n  userTranscript: (data: TranscriptData) => void;\n  botTranscript: (data: BotLLMTextData) => void;\n\n  botLlmText: (data: BotLLMTextData) => void;\n  botLlmStarted: () => void;\n  botLlmStopped: () => void;\n\n  botTtsText: (data: BotTTSTextData) => void;\n  botTtsStarted: () => void;\n  botTtsStopped: () => void;\n\n  error: (message: RTVIMessage) => void;\n  messageError: (message: RTVIMessage) => void;\n\n  llmFunctionCall: (func: LLMFunctionCallData) => void;\n  llmFunctionCallStart: (functionName: string) => void;\n  llmJsonCompletion: (data: string) => void;\n\n  storageItemStored: (data: StorageItemStoredData) => void;\n\n  botLlmSearchResponse: (data: BotLLMSearchResponseData) => void;\n  serverMessage: (data: any) => void;\n}>;\n\nexport type RTVIEventHandler<E extends RTVIEvent> = E extends keyof RTVIEvents\n  ? RTVIEvents[E]\n  : never;\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport { RTVIClient } from \"../client\";\nimport { RTVIMessage } from \"../messages\";\n\nexport type RTVIClientHelpers = Partial<Record<string, RTVIClientHelper>>;\n\nexport type RTVIClientHelperCallbacks = Partial<object>;\n\nexport interface RTVIClientHelperOptions {\n  /**\n   * Callback methods for events / messages\n   */\n  callbacks?: RTVIClientHelperCallbacks;\n}\n\nexport abstract class RTVIClientHelper {\n  protected _options: RTVIClientHelperOptions;\n  protected declare _client: RTVIClient;\n  protected declare _service: string;\n\n  constructor(options: RTVIClientHelperOptions) {\n    this._options = options;\n  }\n\n  public abstract handleMessage(ev: RTVIMessage): void;\n  public abstract getMessageTypes(): string[];\n  public set client(client: RTVIClient) {\n    this._client = client;\n  }\n  public set service(service: string) {\n    this._service = service;\n  }\n}\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nexport enum LogLevel {\n  NONE = 0,\n  ERROR = 1,\n  WARN = 2,\n  INFO = 3,\n  DEBUG = 4,\n}\n\nclass Logger {\n  private static instance: Logger;\n  private level: LogLevel = LogLevel.DEBUG;\n\n  private constructor() {}\n\n  static getInstance(): Logger {\n    if (!Logger.instance) {\n      Logger.instance = new Logger();\n    }\n    return Logger.instance;\n  }\n\n  setLevel(level: LogLevel) {\n    this.level = level;\n  }\n\n  debug(...args: unknown[]) {\n    if (this.level >= LogLevel.DEBUG) {\n      console.debug(...args);\n    }\n  }\n\n  info(...args: unknown[]) {\n    if (this.level >= LogLevel.INFO) {\n      console.info(...args);\n    }\n  }\n\n  warn(...args: unknown[]) {\n    if (this.level >= LogLevel.WARN) {\n      console.warn(...args);\n    }\n  }\n\n  error(...args: unknown[]) {\n    if (this.level >= LogLevel.ERROR) {\n      console.error(...args);\n    }\n  }\n}\n\nexport const logger = Logger.getInstance();\n\nexport type ILogger = Logger;\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport { v4 as uuidv4 } from \"uuid\";\n\nimport { httpActionGenerator } from \"./actions\";\nimport { RTVIClient, RTVIClientConfigOption } from \"./client\";\nimport { ActionEndpointNotSetError } from \"./errors\";\nimport { logger } from \"./logger\";\n\nexport const RTVI_MESSAGE_LABEL = \"rtvi-ai\";\n\nexport enum RTVIMessageType {\n  // Outbound\n  CLIENT_READY = \"client-ready\",\n  UPDATE_CONFIG = \"update-config\",\n  GET_CONFIG = \"get-config\",\n  DESCRIBE_CONFIG = \"describe-config\",\n  DESCRIBE_ACTIONS = \"describe-actions\",\n  DISCONNECT_BOT = \"disconnect-bot\",\n  ACTION = \"action\",\n\n  // Inbound\n  BOT_READY = \"bot-ready\", // Bot is connected and ready to receive messages\n  ERROR = \"error\", // Bot initialization error\n  ERROR_RESPONSE = \"error-response\", // Error response from the bot in response to an action\n  CONFIG = \"config\", // Bot configuration\n  CONFIG_AVAILABLE = \"config-available\", // Configuration options available on the bot\n  CONFIG_ERROR = \"config-error\", // Configuration options have changed failed\n  ACTIONS_AVAILABLE = \"actions-available\", // Actions available on the bot\n  ACTION_RESPONSE = \"action-response\", // Action response from the bot\n  METRICS = \"metrics\", // RTVI reporting metrics\n  USER_TRANSCRIPTION = \"user-transcription\", // Local user speech to text transcription (partials and finals)\n  BOT_TRANSCRIPTION = \"bot-transcription\", // Bot full text transcription (sentence aggregated)\n  USER_STARTED_SPEAKING = \"user-started-speaking\", // User started speaking\n  USER_STOPPED_SPEAKING = \"user-stopped-speaking\", // User stopped speaking\n  BOT_STARTED_SPEAKING = \"bot-started-speaking\", // Bot started speaking\n  BOT_STOPPED_SPEAKING = \"bot-stopped-speaking\", // Bot stopped speaking\n  // Service-specific\n  USER_LLM_TEXT = \"user-llm-text\", // Aggregated user input text which is sent to LLM\n  BOT_LLM_TEXT = \"bot-llm-text\", // Streamed token returned by the LLM\n  BOT_LLM_STARTED = \"bot-llm-started\", // Bot LLM inference starts\n  BOT_LLM_STOPPED = \"bot-llm-stopped\", // Bot LLM inference stops\n  BOT_TTS_TEXT = \"bot-tts-text\", // Bot TTS text output (streamed word as it is spoken)\n  BOT_TTS_STARTED = \"bot-tts-started\", // Bot TTS response starts\n  BOT_TTS_STOPPED = \"bot-tts-stopped\", // Bot TTS response stops\n  BOT_LLM_SEARCH_RESPONSE = \"bot-llm-search-response\", // Bot LLM search response\n  // Storage\n  STORAGE_ITEM_STORED = \"storage-item-stored\", // Item was stored to configured storage, if applicable\n  // Server-to-client messages\n  SERVER_MESSAGE = \"server-message\",\n}\n\n// ----- Message Data Types\n\nexport type ConfigData = {\n  config: RTVIClientConfigOption[];\n};\n\nexport type BotReadyData = {\n  config: RTVIClientConfigOption[];\n  version: string;\n};\n\nexport type ErrorData = {\n  message: string;\n  fatal: boolean;\n};\n\nexport type PipecatMetricData = {\n  processor: string;\n  value: number;\n};\n\nexport type PipecatMetricsData = {\n  processing?: PipecatMetricData[];\n  ttfb?: PipecatMetricData[];\n  characters?: PipecatMetricData[];\n};\n\nexport type TranscriptData = {\n  text: string;\n  final: boolean;\n  timestamp: string;\n  user_id: string;\n};\n\nexport type BotLLMTextData = {\n  text: string;\n};\n\nexport type BotTTSTextData = {\n  text: string;\n};\n\nexport type StorageItemStoredData = {\n  action: string;\n  items: unknown;\n};\n\nexport type LLMSearchResult = {\n  text: string;\n  confidence: number[];\n};\n\nexport type LLMSearchOrigin = {\n  site_uri?: string;\n  site_title?: string;\n  results: LLMSearchResult[];\n};\n\nexport type BotLLMSearchResponseData = {\n  search_result?: string;\n  rendered_content?: string;\n  origins: LLMSearchOrigin[];\n};\n\nexport type ServerMessageData = {\n  data: any;\n};\n\n// ----- Message Classes\n\nexport type RTVIMessageActionResponse = {\n  id: string;\n  label: string;\n  type: string;\n  data: { result: unknown };\n};\n\nexport class RTVIMessage {\n  id: string;\n  label: string = RTVI_MESSAGE_LABEL;\n  type: string;\n  data: unknown;\n\n  constructor(type: string, data: unknown, id?: string) {\n    this.type = type;\n    this.data = data;\n    this.id = id || uuidv4().slice(0, 8);\n  }\n\n  // Outbound message types\n  static clientReady(): RTVIMessage {\n    return new RTVIMessage(RTVIMessageType.CLIENT_READY, {});\n  }\n\n  static updateConfig(\n    config: RTVIClientConfigOption[],\n    interrupt: boolean = false\n  ): RTVIMessage {\n    return new RTVIMessage(RTVIMessageType.UPDATE_CONFIG, {\n      config,\n      interrupt,\n    });\n  }\n\n  static describeConfig(): RTVIMessage {\n    return new RTVIMessage(RTVIMessageType.DESCRIBE_CONFIG, {});\n  }\n\n  static getBotConfig(): RTVIMessage {\n    return new RTVIMessage(RTVIMessageType.GET_CONFIG, {});\n  }\n\n  static describeActions(): RTVIMessage {\n    return new RTVIMessage(RTVIMessageType.DESCRIBE_ACTIONS, {});\n  }\n\n  static disconnectBot(): RTVIMessage {\n    return new RTVIMessage(RTVIMessageType.DISCONNECT_BOT, {});\n  }\n\n  static error(message: string, fatal = false): RTVIMessage {\n    return new RTVIMessage(RTVIMessageType.ERROR, { message, fatal });\n  }\n}\n\n// ----- Action Types\n\nexport type RTVIActionRequestData = {\n  service: string;\n  action: string;\n  arguments?: { name: string; value: unknown }[];\n};\n\nexport class RTVIActionRequest extends RTVIMessage {\n  constructor(data: RTVIActionRequestData) {\n    super(RTVIMessageType.ACTION, data);\n  }\n}\n\nexport type RTVIActionResponse = {\n  id: string;\n  label: string;\n  type: string;\n  data: { result: unknown };\n};\n\n// ----- Message Dispatcher\n\ninterface QueuedRTVIMessage {\n  message: RTVIMessage;\n  timestamp: number;\n  resolve: (value: unknown) => void;\n  reject: (reason?: unknown) => void;\n}\n\nexport class MessageDispatcher {\n  private _client: RTVIClient;\n  private _gcTime: number;\n  private _queue = new Array<QueuedRTVIMessage>();\n\n  constructor(client: RTVIClient) {\n    this._gcTime = 10000; // How long to wait before resolving the message\n    this._queue = [];\n    this._client = client;\n  }\n\n  public dispatch(message: RTVIMessage): Promise<RTVIMessage> {\n    const promise = new Promise((resolve, reject) => {\n      this._queue.push({\n        message,\n        timestamp: Date.now(),\n        resolve,\n        reject,\n      });\n    });\n\n    logger.debug(\"[MessageDispatcher] dispatch\", message);\n\n    this._client.sendMessage(message);\n\n    this._gc();\n\n    return promise as Promise<RTVIMessage | RTVIMessageActionResponse>;\n  }\n\n  public async dispatchAction(\n    action: RTVIActionRequest,\n    onMessage: (message: RTVIMessage) => void\n  ): Promise<RTVIMessageActionResponse> {\n    const promise = new Promise((resolve, reject) => {\n      this._queue.push({\n        message: action,\n        timestamp: Date.now(),\n        resolve,\n        reject,\n      });\n    });\n\n    logger.debug(\"[MessageDispatcher] action\", action);\n\n    if (this._client.connected) {\n      // Send message to transport when connected\n      this._client.sendMessage(action);\n    } else {\n      if (!this._client.params.endpoints?.action) {\n        logger.error(\n          \"[MessageDispatcher] Action endpoint is required when dispatching action in disconnected state\"\n        );\n        throw new ActionEndpointNotSetError();\n      }\n      const actionUrl = this._client.constructUrl(\"action\");\n\n      try {\n        // Dispatch action via HTTP when disconnected\n        await httpActionGenerator(\n          actionUrl,\n          action,\n          this._client.params,\n          (response: RTVIActionResponse) => {\n            onMessage(response);\n          }\n        );\n        // On HTTP success (resolve), send `action` message (for callbacks)\n      } catch (e) {\n        onMessage(\n          new RTVIMessage(\n            RTVIMessageType.ERROR_RESPONSE,\n            `Action endpoint '${actionUrl}' returned an error response`,\n            action.id\n          )\n        );\n      }\n    }\n\n    this._gc();\n\n    return promise as Promise<RTVIMessageActionResponse>;\n  }\n\n  private _resolveReject(\n    message: RTVIMessage,\n    resolve: boolean = true\n  ): RTVIMessage {\n    const queuedMessage = this._queue.find(\n      (msg) => msg.message.id === message.id\n    );\n\n    if (queuedMessage) {\n      if (resolve) {\n        logger.debug(\"[MessageDispatcher] Resolve\", message);\n        queuedMessage.resolve(\n          message.type === RTVIMessageType.ACTION_RESPONSE\n            ? (message as RTVIMessageActionResponse)\n            : (message as RTVIMessage)\n        );\n      } else {\n        logger.debug(\"[MessageDispatcher] Reject\", message);\n        queuedMessage.reject(message as RTVIMessage);\n      }\n      // Remove message from queue\n      this._queue = this._queue.filter((msg) => msg.message.id !== message.id);\n      logger.debug(\"[MessageDispatcher] Queue\", this._queue);\n    }\n\n    return message;\n  }\n\n  public resolve(message: RTVIMessage): RTVIMessage {\n    return this._resolveReject(message, true);\n  }\n\n  public reject(message: RTVIMessage): RTVIMessage {\n    return this._resolveReject(message, false);\n  }\n\n  private _gc() {\n    this._queue = this._queue.filter((msg) => {\n      return Date.now() - msg.timestamp < this._gcTime;\n    });\n    logger.debug(\"[MessageDispatcher] GC\", this._queue);\n  }\n}\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport * as RTVIErrors from \"./../errors\";\nimport { RTVIEvent } from \"./../events\";\nimport {\n  RTVIActionRequestData,\n  RTVIActionResponse,\n  RTVIMessage,\n} from \"./../messages\";\nimport { RTV<PERSON>lientHelper, RTVIClientHelperOptions } from \".\";\n\n// --- Types\n\nexport type LLMFunctionCallData = {\n  function_name: string;\n  tool_call_id: string;\n  args: unknown;\n  result?: unknown;\n};\n\nexport type LLMContextMessage = {\n  role: string;\n  content: unknown;\n};\n\nexport type LLMContext = Partial<{\n  messages?: LLMContextMessage[];\n  tools?: [];\n}>;\n\nexport type FunctionCallParams = {\n  functionName: string;\n  arguments: unknown;\n};\n\nexport type FunctionCallCallback = (fn: FunctionCallParams) => Promise<unknown>;\n\n// --- Message types\nexport enum LLMMessageType {\n  LLM_FUNCTION_CALL = \"llm-function-call\",\n  LLM_FUNCTION_CALL_START = \"llm-function-call-start\",\n  LLM_FUNCTION_CALL_RESULT = \"llm-function-call-result\",\n  LLM_JSON_COMPLETION = \"llm-json-completion\",\n}\n\nexport enum LLMActionType {\n  APPEND_TO_MESSAGES = \"append_to_messages\",\n  GET_CONTEXT = \"get_context\",\n  SET_CONTEXT = \"set_context\",\n  RUN = \"run\",\n}\n\n// --- Callbacks\nexport type LLMHelperCallbacks = Partial<{\n  onLLMJsonCompletion: (jsonString: string) => void;\n  onLLMFunctionCall: (func: LLMFunctionCallData) => void;\n  onLLMFunctionCallStart: (functionName: string) => void;\n  onLLMMessage: (message: LLMContextMessage) => void;\n}>;\n\n// --- Interface and class\nexport interface LLMHelperOptions extends RTVIClientHelperOptions {\n  callbacks?: LLMHelperCallbacks;\n}\n\nexport class LLMHelper extends RTVIClientHelper {\n  protected declare _options: LLMHelperOptions;\n  private _functionCallCallback: FunctionCallCallback | null;\n\n  constructor(options: LLMHelperOptions) {\n    super(options);\n\n    this._functionCallCallback = null;\n  }\n\n  public getMessageTypes(): string[] {\n    return Object.values(LLMMessageType) as string[];\n  }\n\n  // --- Actions\n\n  /**\n   * Retrieve the bot's current LLM context.\n   * @returns Promise<LLMContext>\n   */\n  public async getContext(): Promise<LLMContext> {\n    if (this._client.state !== \"ready\") {\n      throw new RTVIErrors.BotNotReadyError(\n        \"getContext called while transport not in ready state\"\n      );\n    }\n    const actionResponseMsg: RTVIActionResponse = await this._client.action({\n      service: this._service,\n      action: LLMActionType.GET_CONTEXT,\n    } as RTVIActionRequestData);\n    return actionResponseMsg.data.result as LLMContext;\n  }\n\n  /**\n   * Update the bot's LLM context.\n   * If this is called while the transport is not in the ready state, the local context will be updated\n   * @param context LLMContext - The new context\n   * @param interrupt boolean - Whether to interrupt the bot, or wait until it has finished speaking\n   * @returns Promise<boolean>\n   */\n\n  public async setContext(\n    context: LLMContext,\n    interrupt: boolean = false\n  ): Promise<boolean> {\n    if (this._client.state !== \"ready\") {\n      throw new RTVIErrors.BotNotReadyError(\n        \"setContext called while transport not in ready state\"\n      );\n    }\n\n    const actionResponse: RTVIActionResponse = (await this._client.action({\n      service: this._service,\n      action: LLMActionType.SET_CONTEXT,\n      arguments: [\n        {\n          name: \"messages\",\n          value: context.messages,\n        },\n        {\n          name: \"interrupt\",\n          value: interrupt,\n        },\n      ],\n    } as RTVIActionRequestData)) as RTVIActionResponse;\n\n    return !!actionResponse.data.result;\n  }\n\n  /**\n   * Append a new message to the LLM context.\n   * If this is called while the transport is not in the ready state, the local context will be updated\n   * @param context LLMContextMessage\n   * @param runImmediately boolean - wait until pipeline is idle before running\n   * @returns boolean\n   */\n\n  public async appendToMessages(\n    message: LLMContextMessage,\n    runImmediately: boolean = false\n  ): Promise<boolean> {\n    if (this._client.state !== \"ready\") {\n      throw new RTVIErrors.BotNotReadyError(\n        \"setContext called while transport not in ready state\"\n      );\n    }\n\n    const actionResponse = (await this._client.action({\n      service: this._service,\n      action: LLMActionType.APPEND_TO_MESSAGES,\n      arguments: [\n        {\n          name: \"messages\",\n          value: [message],\n        },\n        {\n          name: \"run_immediately\",\n          value: runImmediately,\n        },\n      ],\n    } as RTVIActionRequestData)) as RTVIActionResponse;\n    return !!actionResponse.data.result;\n  }\n\n  /**\n   * Run the bot's current LLM context.\n   * Useful when appending messages to the context without runImmediately set to true.\n   * Will do nothing if the bot is not in the ready state.\n   * @param interrupt boolean - Whether to interrupt the bot, or wait until it has finished speaking\n   * @returns Promise<unknown>\n   */\n  public async run(interrupt: boolean = false): Promise<unknown> {\n    if (this._client.state !== \"ready\") {\n      return;\n    }\n\n    return this._client.action({\n      service: this._service,\n      action: LLMActionType.RUN,\n      arguments: [\n        {\n          name: \"interrupt\",\n          value: interrupt,\n        },\n      ],\n    } as RTVIActionRequestData);\n  }\n\n  // --- Handlers\n\n  /**\n   * If the LLM wants to call a function, RTVI will invoke the callback defined\n   * here. Whatever the callback returns will be sent to the LLM as the function result.\n   * @param callback\n   * @returns void\n   */\n  public handleFunctionCall(callback: FunctionCallCallback): void {\n    this._functionCallCallback = callback;\n  }\n\n  public handleMessage(ev: RTVIMessage): void {\n    switch (ev.type) {\n      case LLMMessageType.LLM_JSON_COMPLETION:\n        this._options.callbacks?.onLLMJsonCompletion?.(ev.data as string);\n        this._client.emit(RTVIEvent.LLMJsonCompletion, ev.data as string);\n        break;\n      case LLMMessageType.LLM_FUNCTION_CALL: {\n        const d = ev.data as LLMFunctionCallData;\n        this._options.callbacks?.onLLMFunctionCall?.(\n          ev.data as LLMFunctionCallData\n        );\n        this._client.emit(\n          RTVIEvent.LLMFunctionCall,\n          ev.data as LLMFunctionCallData\n        );\n        if (this._functionCallCallback) {\n          const fn = {\n            functionName: d.function_name,\n            arguments: d.args,\n          };\n          if (this._client.state === \"ready\") {\n            this._functionCallCallback(fn).then((result) => {\n              this._client.sendMessage(\n                new RTVIMessage(LLMMessageType.LLM_FUNCTION_CALL_RESULT, {\n                  function_name: d.function_name,\n                  tool_call_id: d.tool_call_id,\n                  arguments: d.args,\n                  result,\n                })\n              );\n            });\n          } else {\n            throw new RTVIErrors.BotNotReadyError(\n              \"Attempted to send a function call result from bot while transport not in ready state\"\n            );\n          }\n        }\n        break;\n      }\n      case LLMMessageType.LLM_FUNCTION_CALL_START: {\n        const e = ev.data as LLMFunctionCallData;\n        this._options.callbacks?.onLLMFunctionCallStart?.(\n          e.function_name as string\n        );\n        this._client.emit(RTVIEvent.LLMFunctionCallStart, e.function_name);\n        break;\n      }\n    }\n  }\n}\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport { RTVIClientOptions, RTVIEventCallbacks } from \"./client\";\nimport { RTVIMessage } from \"./messages\";\n\nexport type TransportState =\n  | \"disconnected\"\n  | \"initializing\"\n  | \"initialized\"\n  | \"authenticating\"\n  | \"connecting\"\n  | \"connected\"\n  | \"ready\"\n  | \"disconnecting\"\n  | \"error\";\n\nexport type Participant = {\n  id: string;\n  name: string;\n  local: boolean;\n};\n\nexport type Tracks = {\n  local: {\n    audio?: MediaStreamTrack;\n    video?: MediaStreamTrack;\n    screenAudio?: MediaStreamTrack;\n    screenVideo?: MediaStreamTrack;\n  };\n  bot?: {\n    audio?: MediaStreamTrack;\n    screenAudio?: undefined;\n    screenVideo?: undefined;\n    video?: MediaStreamTrack;\n  };\n};\n\nexport abstract class Transport {\n  protected declare _options: RTVIClientOptions;\n  protected declare _onMessage: (ev: RTVIMessage) => void;\n  protected declare _callbacks: RTVIEventCallbacks;\n  protected _state: TransportState = \"disconnected\";\n  protected _expiry?: number = undefined;\n\n  constructor() {}\n\n  abstract initialize(\n    options: RTVIClientOptions,\n    messageHandler: (ev: RTVIMessage) => void\n  ): void;\n\n  abstract initDevices(): Promise<void>;\n\n  abstract connect(\n    authBundle: unknown,\n    abortController: AbortController\n  ): Promise<void>;\n  abstract disconnect(): Promise<void>;\n  abstract sendReadyMessage(): void;\n\n  abstract getAllMics(): Promise<MediaDeviceInfo[]>;\n  abstract getAllCams(): Promise<MediaDeviceInfo[]>;\n  abstract getAllSpeakers(): Promise<MediaDeviceInfo[]>;\n\n  abstract updateMic(micId: string): void;\n  abstract updateCam(camId: string): void;\n  abstract updateSpeaker(speakerId: string): void;\n\n  abstract get selectedMic(): MediaDeviceInfo | Record<string, never>;\n  abstract get selectedCam(): MediaDeviceInfo | Record<string, never>;\n  abstract get selectedSpeaker(): MediaDeviceInfo | Record<string, never>;\n\n  abstract enableMic(enable: boolean): void;\n  abstract enableCam(enable: boolean): void;\n  abstract enableScreenShare(enable: boolean): void;\n  abstract get isCamEnabled(): boolean;\n  abstract get isMicEnabled(): boolean;\n  abstract get isSharingScreen(): boolean;\n\n  abstract sendMessage(message: RTVIMessage): void;\n\n  abstract get state(): TransportState;\n  abstract set state(state: TransportState);\n\n  get expiry(): number | undefined {\n    return this._expiry;\n  }\n\n  abstract tracks(): Tracks;\n}\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA,QAAI,WAAW,OAAO,UAAU;AAEhC,WAAO,UAAU,SAAS,OAAO,KAAK;AACpC,UAAI,QAAQ,OAAQ,QAAO;AAC3B,UAAI,QAAQ,KAAM,QAAO;AAEzB,UAAI,OAAO,OAAO;AAClB,UAAI,SAAS,UAAW,QAAO;AAC/B,UAAI,SAAS,SAAU,QAAO;AAC9B,UAAI,SAAS,SAAU,QAAO;AAC9B,UAAI,SAAS,SAAU,QAAO;AAC9B,UAAI,SAAS,YAAY;AACvB,eAAO,cAAc,GAAG,IAAI,sBAAsB;AAAA,MACpD;AAEA,UAAI,QAAQ,GAAG,EAAG,QAAO;AACzB,UAAI,SAAS,GAAG,EAAG,QAAO;AAC1B,UAAI,YAAY,GAAG,EAAG,QAAO;AAC7B,UAAI,OAAO,GAAG,EAAG,QAAO;AACxB,UAAI,QAAQ,GAAG,EAAG,QAAO;AACzB,UAAI,SAAS,GAAG,EAAG,QAAO;AAE1B,cAAQ,SAAS,GAAG,GAAG;AAAA,QACrB,KAAK;AAAU,iBAAO;AAAA,QACtB,KAAK;AAAW,iBAAO;AAAA,QAGvB,KAAK;AAAW,iBAAO;AAAA,QACvB,KAAK;AAAW,iBAAO;AAAA,QACvB,KAAK;AAAO,iBAAO;AAAA,QACnB,KAAK;AAAO,iBAAO;AAAA,QAGnB,KAAK;AAAa,iBAAO;AAAA,QACzB,KAAK;AAAc,iBAAO;AAAA,QAC1B,KAAK;AAAqB,iBAAO;AAAA,QAGjC,KAAK;AAAc,iBAAO;AAAA,QAC1B,KAAK;AAAe,iBAAO;AAAA,QAG3B,KAAK;AAAc,iBAAO;AAAA,QAC1B,KAAK;AAAe,iBAAO;AAAA,QAC3B,KAAK;AAAgB,iBAAO;AAAA,QAC5B,KAAK;AAAgB,iBAAO;AAAA,MAC9B;AAEA,UAAI,eAAe,GAAG,GAAG;AACvB,eAAO;AAAA,MACT;AAGA,aAAO,SAAS,KAAK,GAAG;AACxB,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAmB,iBAAO;AAAA,QAE/B,KAAK;AAAyB,iBAAO;AAAA,QACrC,KAAK;AAAyB,iBAAO;AAAA,QACrC,KAAK;AAA4B,iBAAO;AAAA,QACxC,KAAK;AAA2B,iBAAO;AAAA,MACzC;AAGA,aAAO,KAAK,MAAM,GAAG,EAAE,EAAE,YAAY,EAAE,QAAQ,OAAO,EAAE;AAAA,IAC1D;AAEA,aAAS,SAAS,KAAK;AACrB,aAAO,OAAO,IAAI,gBAAgB,aAAa,IAAI,YAAY,OAAO;AAAA,IACxE;AAEA,aAAS,QAAQ,KAAK;AACpB,UAAI,MAAM,QAAS,QAAO,MAAM,QAAQ,GAAG;AAC3C,aAAO,eAAe;AAAA,IACxB;AAEA,aAAS,QAAQ,KAAK;AACpB,aAAO,eAAe,SAAU,OAAO,IAAI,YAAY,YAAY,IAAI,eAAe,OAAO,IAAI,YAAY,oBAAoB;AAAA,IACnI;AAEA,aAAS,OAAO,KAAK;AACnB,UAAI,eAAe,KAAM,QAAO;AAChC,aAAO,OAAO,IAAI,iBAAiB,cAC9B,OAAO,IAAI,YAAY,cACvB,OAAO,IAAI,YAAY;AAAA,IAC9B;AAEA,aAAS,SAAS,KAAK;AACrB,UAAI,eAAe,OAAQ,QAAO;AAClC,aAAO,OAAO,IAAI,UAAU,YACvB,OAAO,IAAI,eAAe,aAC1B,OAAO,IAAI,cAAc,aACzB,OAAO,IAAI,WAAW;AAAA,IAC7B;AAEA,aAAS,cAAc,MAAM,KAAK;AAChC,aAAO,SAAS,IAAI,MAAM;AAAA,IAC5B;AAEA,aAAS,eAAe,KAAK;AAC3B,aAAO,OAAO,IAAI,UAAU,cACvB,OAAO,IAAI,WAAW,cACtB,OAAO,IAAI,SAAS;AAAA,IAC3B;AAEA,aAAS,YAAY,KAAK;AACxB,UAAI;AACF,YAAI,OAAO,IAAI,WAAW,YAAY,OAAO,IAAI,WAAW,YAAY;AACtE,iBAAO;AAAA,QACT;AAAA,MACF,SAAS,KAAK;AACZ,YAAI,IAAI,QAAQ,QAAQ,QAAQ,MAAM,IAAI;AACxC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAOA,aAAS,SAAS,KAAK;AACrB,UAAI,IAAI,eAAe,OAAO,IAAI,YAAY,aAAa,YAAY;AACrE,eAAO,IAAI,YAAY,SAAS,GAAG;AAAA,MACrC;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;AChIA;AAAA;AAAA;AASA,QAAM,UAAU,OAAO,UAAU;AACjC,QAAM,SAAS;AAEf,aAAS,MAAM,KAAK,MAAM;AACxB,cAAQ,OAAO,GAAG,GAAG;AAAA,QACnB,KAAK;AACH,iBAAO,IAAI,MAAM;AAAA,QACnB,KAAK;AACH,iBAAO,OAAO,OAAO,CAAC,GAAG,GAAG;AAAA,QAC9B,KAAK;AACH,iBAAO,IAAI,IAAI,YAAY,OAAO,GAAG,CAAC;AAAA,QACxC,KAAK;AACH,iBAAO,IAAI,IAAI,GAAG;AAAA,QACpB,KAAK;AACH,iBAAO,IAAI,IAAI,GAAG;AAAA,QACpB,KAAK;AACH,iBAAO,YAAY,GAAG;AAAA,QACxB,KAAK;AACH,iBAAO,YAAY,GAAG;AAAA,QACxB,KAAK;AACH,iBAAO,iBAAiB,GAAG;AAAA,QAC7B,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,gBAAgB,GAAG;AAAA,QAC5B,KAAK;AACH,iBAAO,YAAY,GAAG;AAAA,QACxB,KAAK;AACH,iBAAO,OAAO,OAAO,GAAG;AAAA,QAC1B,SAAS;AACP,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAEA,aAAS,YAAY,KAAK;AACxB,YAAM,QAAQ,IAAI,UAAU,SAAS,IAAI,QAAS,OAAO,KAAK,GAAG,KAAK;AACtE,YAAM,KAAK,IAAI,IAAI,YAAY,IAAI,QAAQ,KAAK;AAChD,SAAG,YAAY,IAAI;AACnB,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB,KAAK;AAC7B,YAAM,MAAM,IAAI,IAAI,YAAY,IAAI,UAAU;AAC9C,UAAI,WAAW,GAAG,EAAE,IAAI,IAAI,WAAW,GAAG,CAAC;AAC3C,aAAO;AAAA,IACT;AAEA,aAAS,gBAAgB,KAAK,MAAM;AAClC,aAAO,IAAI,IAAI,YAAY,IAAI,QAAQ,IAAI,YAAY,IAAI,MAAM;AAAA,IACnE;AAEA,aAAS,YAAY,KAAK;AACxB,YAAM,MAAM,IAAI;AAChB,YAAM,MAAM,OAAO,cAAc,OAAO,YAAY,GAAG,IAAI,OAAO,KAAK,GAAG;AAC1E,UAAI,KAAK,GAAG;AACZ,aAAO;AAAA,IACT;AAEA,aAAS,YAAY,KAAK;AACxB,aAAO,UAAU,OAAO,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC;AAAA,IAChD;AAMA,WAAO,UAAU;AAAA;AAAA;;;AClFjB;AAAA;AAAA;AASA,WAAO,UAAU,SAAS,SAAS,KAAK;AACtC,aAAO,OAAO,QAAQ,OAAO,QAAQ,YAAY,MAAM,QAAQ,GAAG,MAAM;AAAA,IAC1E;AAAA;AAAA;;;ACXA;AAAA;AAAA;AASA,QAAI,WAAW;AAEf,aAAS,eAAe,GAAG;AACzB,aAAO,SAAS,CAAC,MAAM,QAClB,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAAA,IAC7C;AAEA,WAAO,UAAU,SAAS,cAAc,GAAG;AACzC,UAAI,MAAK;AAET,UAAI,eAAe,CAAC,MAAM,MAAO,QAAO;AAGxC,aAAO,EAAE;AACT,UAAI,OAAO,SAAS,WAAY,QAAO;AAGvC,aAAO,KAAK;AACZ,UAAI,eAAe,IAAI,MAAM,MAAO,QAAO;AAG3C,UAAI,KAAK,eAAe,eAAe,MAAM,OAAO;AAClD,eAAO;AAAA,MACT;AAGA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACpCA;AAAA;AAAA;AAMA,QAAM,QAAQ;AACd,QAAM,SAAS;AACf,QAAM,gBAAgB;AAEtB,aAAS,UAAU,KAAK,eAAe;AACrC,cAAQ,OAAO,GAAG,GAAG;AAAA,QACnB,KAAK;AACH,iBAAO,gBAAgB,KAAK,aAAa;AAAA,QAC3C,KAAK;AACH,iBAAO,eAAe,KAAK,aAAa;AAAA,QAC1C,SAAS;AACP,iBAAO,MAAM,GAAG;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AAEA,aAAS,gBAAgB,KAAK,eAAe;AAC3C,UAAI,OAAO,kBAAkB,YAAY;AACvC,eAAO,cAAc,GAAG;AAAA,MAC1B;AACA,UAAI,iBAAiB,cAAc,GAAG,GAAG;AACvC,cAAM,MAAM,IAAI,IAAI,YAAY;AAChC,iBAAS,OAAO,KAAK;AACnB,cAAI,GAAG,IAAI,UAAU,IAAI,GAAG,GAAG,aAAa;AAAA,QAC9C;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAEA,aAAS,eAAe,KAAK,eAAe;AAC1C,YAAM,MAAM,IAAI,IAAI,YAAY,IAAI,MAAM;AAC1C,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,CAAC,IAAI,UAAU,IAAI,CAAC,GAAG,aAAa;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AAMA,WAAO,UAAU;AAAA;AAAA;;;AChDjB;AAAA;AAAA;AAuBA,QAAI,IAAI,OAAO,YAAY,WAAW,UAAU;AAChD,QAAI,eAAe,KAAK,OAAO,EAAE,UAAU,aACvC,EAAE,QACF,SAASA,cAAa,QAAQ,UAAU,MAAM;AAC9C,aAAO,SAAS,UAAU,MAAM,KAAK,QAAQ,UAAU,IAAI;AAAA,IAC7D;AAEF,QAAI;AACJ,QAAI,KAAK,OAAO,EAAE,YAAY,YAAY;AACxC,uBAAiB,EAAE;AAAA,IACrB,WAAW,OAAO,uBAAuB;AACvC,uBAAiB,SAASC,gBAAe,QAAQ;AAC/C,eAAO,OAAO,oBAAoB,MAAM,EACrC,OAAO,OAAO,sBAAsB,MAAM,CAAC;AAAA,MAChD;AAAA,IACF,OAAO;AACL,uBAAiB,SAASA,gBAAe,QAAQ;AAC/C,eAAO,OAAO,oBAAoB,MAAM;AAAA,MAC1C;AAAA,IACF;AAEA,aAAS,mBAAmB,SAAS;AACnC,UAAI,WAAW,QAAQ,KAAM,SAAQ,KAAK,OAAO;AAAA,IACnD;AAEA,QAAI,cAAc,OAAO,SAAS,SAASC,aAAY,OAAO;AAC5D,aAAO,UAAU;AAAA,IACnB;AAEA,aAAS,eAAe;AACtB,mBAAa,KAAK,KAAK,IAAI;AAAA,IAC7B;AACA,WAAO,UAAU;AACjB,WAAO,QAAQ,OAAO;AAGtB,iBAAa,eAAe;AAE5B,iBAAa,UAAU,UAAU;AACjC,iBAAa,UAAU,eAAe;AACtC,iBAAa,UAAU,gBAAgB;AAIvC,QAAI,sBAAsB;AAE1B,aAAS,cAAc,UAAU;AAC/B,UAAI,OAAO,aAAa,YAAY;AAClC,cAAM,IAAI,UAAU,qEAAqE,OAAO,QAAQ;AAAA,MAC1G;AAAA,IACF;AAEA,WAAO,eAAe,cAAc,uBAAuB;AAAA,MACzD,YAAY;AAAA,MACZ,KAAK,WAAW;AACd,eAAO;AAAA,MACT;AAAA,MACA,KAAK,SAAS,KAAK;AACjB,YAAI,OAAO,QAAQ,YAAY,MAAM,KAAK,YAAY,GAAG,GAAG;AAC1D,gBAAM,IAAI,WAAW,oGAAoG,MAAM,GAAG;AAAA,QACpI;AACA,8BAAsB;AAAA,MACxB;AAAA,IACF,CAAC;AAED,iBAAa,OAAO,WAAW;AAE7B,UAAI,KAAK,YAAY,UACjB,KAAK,YAAY,OAAO,eAAe,IAAI,EAAE,SAAS;AACxD,aAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,aAAK,eAAe;AAAA,MACtB;AAEA,WAAK,gBAAgB,KAAK,iBAAiB;AAAA,IAC7C;AAIA,iBAAa,UAAU,kBAAkB,SAAS,gBAAgB,GAAG;AACnE,UAAI,OAAO,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,GAAG;AACpD,cAAM,IAAI,WAAW,kFAAkF,IAAI,GAAG;AAAA,MAChH;AACA,WAAK,gBAAgB;AACrB,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB,MAAM;AAC9B,UAAI,KAAK,kBAAkB;AACzB,eAAO,aAAa;AACtB,aAAO,KAAK;AAAA,IACd;AAEA,iBAAa,UAAU,kBAAkB,SAAS,kBAAkB;AAClE,aAAO,iBAAiB,IAAI;AAAA,IAC9B;AAEA,iBAAa,UAAU,OAAO,SAAS,KAAK,MAAM;AAChD,UAAI,OAAO,CAAC;AACZ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAK,MAAK,KAAK,UAAU,CAAC,CAAC;AACjE,UAAI,UAAW,SAAS;AAExB,UAAI,SAAS,KAAK;AAClB,UAAI,WAAW;AACb,kBAAW,WAAW,OAAO,UAAU;AAAA,eAChC,CAAC;AACR,eAAO;AAGT,UAAI,SAAS;AACX,YAAI;AACJ,YAAI,KAAK,SAAS;AAChB,eAAK,KAAK,CAAC;AACb,YAAI,cAAc,OAAO;AAGvB,gBAAM;AAAA,QACR;AAEA,YAAI,MAAM,IAAI,MAAM,sBAAsB,KAAK,OAAO,GAAG,UAAU,MAAM,GAAG;AAC5E,YAAI,UAAU;AACd,cAAM;AAAA,MACR;AAEA,UAAI,UAAU,OAAO,IAAI;AAEzB,UAAI,YAAY;AACd,eAAO;AAET,UAAI,OAAO,YAAY,YAAY;AACjC,qBAAa,SAAS,MAAM,IAAI;AAAA,MAClC,OAAO;AACL,YAAI,MAAM,QAAQ;AAClB,YAAI,YAAY,WAAW,SAAS,GAAG;AACvC,iBAAS,IAAI,GAAG,IAAI,KAAK,EAAE;AACzB,uBAAa,UAAU,CAAC,GAAG,MAAM,IAAI;AAAA,MACzC;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,aAAa,QAAQ,MAAM,UAAU,SAAS;AACrD,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,oBAAc,QAAQ;AAEtB,eAAS,OAAO;AAChB,UAAI,WAAW,QAAW;AACxB,iBAAS,OAAO,UAAU,uBAAO,OAAO,IAAI;AAC5C,eAAO,eAAe;AAAA,MACxB,OAAO;AAGL,YAAI,OAAO,gBAAgB,QAAW;AACpC,iBAAO;AAAA,YAAK;AAAA,YAAe;AAAA,YACf,SAAS,WAAW,SAAS,WAAW;AAAA,UAAQ;AAI5D,mBAAS,OAAO;AAAA,QAClB;AACA,mBAAW,OAAO,IAAI;AAAA,MACxB;AAEA,UAAI,aAAa,QAAW;AAE1B,mBAAW,OAAO,IAAI,IAAI;AAC1B,UAAE,OAAO;AAAA,MACX,OAAO;AACL,YAAI,OAAO,aAAa,YAAY;AAElC,qBAAW,OAAO,IAAI,IACpB,UAAU,CAAC,UAAU,QAAQ,IAAI,CAAC,UAAU,QAAQ;AAAA,QAExD,WAAW,SAAS;AAClB,mBAAS,QAAQ,QAAQ;AAAA,QAC3B,OAAO;AACL,mBAAS,KAAK,QAAQ;AAAA,QACxB;AAGA,YAAI,iBAAiB,MAAM;AAC3B,YAAI,IAAI,KAAK,SAAS,SAAS,KAAK,CAAC,SAAS,QAAQ;AACpD,mBAAS,SAAS;AAGlB,cAAI,IAAI,IAAI,MAAM,iDACE,SAAS,SAAS,MAAM,OAAO,IAAI,IAAI,mEAEvB;AACpC,YAAE,OAAO;AACT,YAAE,UAAU;AACZ,YAAE,OAAO;AACT,YAAE,QAAQ,SAAS;AACnB,6BAAmB,CAAC;AAAA,QACtB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,cAAc,SAAS,YAAY,MAAM,UAAU;AACxE,aAAO,aAAa,MAAM,MAAM,UAAU,KAAK;AAAA,IACjD;AAEA,iBAAa,UAAU,KAAK,aAAa,UAAU;AAEnD,iBAAa,UAAU,kBACnB,SAAS,gBAAgB,MAAM,UAAU;AACvC,aAAO,aAAa,MAAM,MAAM,UAAU,IAAI;AAAA,IAChD;AAEJ,aAAS,cAAc;AACrB,UAAI,CAAC,KAAK,OAAO;AACf,aAAK,OAAO,eAAe,KAAK,MAAM,KAAK,MAAM;AACjD,aAAK,QAAQ;AACb,YAAI,UAAU,WAAW;AACvB,iBAAO,KAAK,SAAS,KAAK,KAAK,MAAM;AACvC,eAAO,KAAK,SAAS,MAAM,KAAK,QAAQ,SAAS;AAAA,MACnD;AAAA,IACF;AAEA,aAAS,UAAU,QAAQ,MAAM,UAAU;AACzC,UAAI,QAAQ,EAAE,OAAO,OAAO,QAAQ,QAAW,QAAgB,MAAY,SAAmB;AAC9F,UAAI,UAAU,YAAY,KAAK,KAAK;AACpC,cAAQ,WAAW;AACnB,YAAM,SAAS;AACf,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,OAAO,SAASC,MAAK,MAAM,UAAU;AAC1D,oBAAc,QAAQ;AACtB,WAAK,GAAG,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC7C,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,sBACnB,SAAS,oBAAoB,MAAM,UAAU;AAC3C,oBAAc,QAAQ;AACtB,WAAK,gBAAgB,MAAM,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC1D,aAAO;AAAA,IACT;AAGJ,iBAAa,UAAU,iBACnB,SAAS,eAAe,MAAM,UAAU;AACtC,UAAI,MAAM,QAAQ,UAAU,GAAG;AAE/B,oBAAc,QAAQ;AAEtB,eAAS,KAAK;AACd,UAAI,WAAW;AACb,eAAO;AAET,aAAO,OAAO,IAAI;AAClB,UAAI,SAAS;AACX,eAAO;AAET,UAAI,SAAS,YAAY,KAAK,aAAa,UAAU;AACnD,YAAI,EAAE,KAAK,iBAAiB;AAC1B,eAAK,UAAU,uBAAO,OAAO,IAAI;AAAA,aAC9B;AACH,iBAAO,OAAO,IAAI;AAClB,cAAI,OAAO;AACT,iBAAK,KAAK,kBAAkB,MAAM,KAAK,YAAY,QAAQ;AAAA,QAC/D;AAAA,MACF,WAAW,OAAO,SAAS,YAAY;AACrC,mBAAW;AAEX,aAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACrC,cAAI,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,EAAE,aAAa,UAAU;AACzD,+BAAmB,KAAK,CAAC,EAAE;AAC3B,uBAAW;AACX;AAAA,UACF;AAAA,QACF;AAEA,YAAI,WAAW;AACb,iBAAO;AAET,YAAI,aAAa;AACf,eAAK,MAAM;AAAA,aACR;AACH,oBAAU,MAAM,QAAQ;AAAA,QAC1B;AAEA,YAAI,KAAK,WAAW;AAClB,iBAAO,IAAI,IAAI,KAAK,CAAC;AAEvB,YAAI,OAAO,mBAAmB;AAC5B,eAAK,KAAK,kBAAkB,MAAM,oBAAoB,QAAQ;AAAA,MAClE;AAEA,aAAO;AAAA,IACT;AAEJ,iBAAa,UAAU,MAAM,aAAa,UAAU;AAEpD,iBAAa,UAAU,qBACnB,SAAS,mBAAmB,MAAM;AAChC,UAAI,WAAW,QAAQ;AAEvB,eAAS,KAAK;AACd,UAAI,WAAW;AACb,eAAO;AAGT,UAAI,OAAO,mBAAmB,QAAW;AACvC,YAAI,UAAU,WAAW,GAAG;AAC1B,eAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,eAAK,eAAe;AAAA,QACtB,WAAW,OAAO,IAAI,MAAM,QAAW;AACrC,cAAI,EAAE,KAAK,iBAAiB;AAC1B,iBAAK,UAAU,uBAAO,OAAO,IAAI;AAAA;AAEjC,mBAAO,OAAO,IAAI;AAAA,QACtB;AACA,eAAO;AAAA,MACT;AAGA,UAAI,UAAU,WAAW,GAAG;AAC1B,YAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,YAAI;AACJ,aAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,gBAAM,KAAK,CAAC;AACZ,cAAI,QAAQ,iBAAkB;AAC9B,eAAK,mBAAmB,GAAG;AAAA,QAC7B;AACA,aAAK,mBAAmB,gBAAgB;AACxC,aAAK,UAAU,uBAAO,OAAO,IAAI;AACjC,aAAK,eAAe;AACpB,eAAO;AAAA,MACT;AAEA,kBAAY,OAAO,IAAI;AAEvB,UAAI,OAAO,cAAc,YAAY;AACnC,aAAK,eAAe,MAAM,SAAS;AAAA,MACrC,WAAW,cAAc,QAAW;AAElC,aAAK,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,eAAK,eAAe,MAAM,UAAU,CAAC,CAAC;AAAA,QACxC;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEJ,aAAS,WAAW,QAAQ,MAAM,QAAQ;AACxC,UAAI,SAAS,OAAO;AAEpB,UAAI,WAAW;AACb,eAAO,CAAC;AAEV,UAAI,aAAa,OAAO,IAAI;AAC5B,UAAI,eAAe;AACjB,eAAO,CAAC;AAEV,UAAI,OAAO,eAAe;AACxB,eAAO,SAAS,CAAC,WAAW,YAAY,UAAU,IAAI,CAAC,UAAU;AAEnE,aAAO,SACL,gBAAgB,UAAU,IAAI,WAAW,YAAY,WAAW,MAAM;AAAA,IAC1E;AAEA,iBAAa,UAAU,YAAY,SAAS,UAAU,MAAM;AAC1D,aAAO,WAAW,MAAM,MAAM,IAAI;AAAA,IACpC;AAEA,iBAAa,UAAU,eAAe,SAAS,aAAa,MAAM;AAChE,aAAO,WAAW,MAAM,MAAM,KAAK;AAAA,IACrC;AAEA,iBAAa,gBAAgB,SAAS,SAAS,MAAM;AACnD,UAAI,OAAO,QAAQ,kBAAkB,YAAY;AAC/C,eAAO,QAAQ,cAAc,IAAI;AAAA,MACnC,OAAO;AACL,eAAO,cAAc,KAAK,SAAS,IAAI;AAAA,MACzC;AAAA,IACF;AAEA,iBAAa,UAAU,gBAAgB;AACvC,aAAS,cAAc,MAAM;AAC3B,UAAI,SAAS,KAAK;AAElB,UAAI,WAAW,QAAW;AACxB,YAAI,aAAa,OAAO,IAAI;AAE5B,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT,WAAW,eAAe,QAAW;AACnC,iBAAO,WAAW;AAAA,QACpB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,iBAAa,UAAU,aAAa,SAAS,aAAa;AACxD,aAAO,KAAK,eAAe,IAAI,eAAe,KAAK,OAAO,IAAI,CAAC;AAAA,IACjE;AAEA,aAAS,WAAW,KAAK,GAAG;AAC1B,UAAI,OAAO,IAAI,MAAM,CAAC;AACtB,eAAS,IAAI,GAAG,IAAI,GAAG,EAAE;AACvB,aAAK,CAAC,IAAI,IAAI,CAAC;AACjB,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,MAAM,OAAO;AAC9B,aAAO,QAAQ,IAAI,KAAK,QAAQ;AAC9B,aAAK,KAAK,IAAI,KAAK,QAAQ,CAAC;AAC9B,WAAK,IAAI;AAAA,IACX;AAEA,aAAS,gBAAgB,KAAK;AAC5B,UAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AAC9B,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAI,CAAC,IAAI,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,KAAK,SAAS,MAAM;AAC3B,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,iBAAS,cAAc,KAAK;AAC1B,kBAAQ,eAAe,MAAM,QAAQ;AACrC,iBAAO,GAAG;AAAA,QACZ;AAEA,iBAAS,WAAW;AAClB,cAAI,OAAO,QAAQ,mBAAmB,YAAY;AAChD,oBAAQ,eAAe,SAAS,aAAa;AAAA,UAC/C;AACA,kBAAQ,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC;AAAA,QAClC;AAAC;AAED,uCAA+B,SAAS,MAAM,UAAU,EAAE,MAAM,KAAK,CAAC;AACtE,YAAI,SAAS,SAAS;AACpB,wCAA8B,SAAS,eAAe,EAAE,MAAM,KAAK,CAAC;AAAA,QACtE;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,8BAA8B,SAAS,SAAS,OAAO;AAC9D,UAAI,OAAO,QAAQ,OAAO,YAAY;AACpC,uCAA+B,SAAS,SAAS,SAAS,KAAK;AAAA,MACjE;AAAA,IACF;AAEA,aAAS,+BAA+B,SAAS,MAAM,UAAU,OAAO;AACtE,UAAI,OAAO,QAAQ,OAAO,YAAY;AACpC,YAAI,MAAM,MAAM;AACd,kBAAQ,KAAK,MAAM,QAAQ;AAAA,QAC7B,OAAO;AACL,kBAAQ,GAAG,MAAM,QAAQ;AAAA,QAC3B;AAAA,MACF,WAAW,OAAO,QAAQ,qBAAqB,YAAY;AAGzD,gBAAQ,iBAAiB,MAAM,SAAS,aAAa,KAAK;AAGxD,cAAI,MAAM,MAAM;AACd,oBAAQ,oBAAoB,MAAM,YAAY;AAAA,UAChD;AACA,mBAAS,GAAG;AAAA,QACd,CAAC;AAAA,MACH,OAAO;AACL,cAAM,IAAI,UAAU,wEAAwE,OAAO,OAAO;AAAA,MAC5G;AAAA,IACF;AAAA;AAAA;;;;;;;AChfA,IAAO,gBAAQ;;;ACCf,SAAS,SAAS,MAAM;AACtB,SAAO,OAAO,SAAS,YAAY,cAAM,KAAK,IAAI;AACpD;AACA,IAAO,mBAAQ;;;ACHf,SAAS,MAAM,MAAM;AACnB,MAAI,CAAC,iBAAS,IAAI,GAAG;AACnB,UAAM,UAAU,cAAc;AAAA,EAChC;AACA,MAAI;AACJ,MAAI,MAAM,IAAI,WAAW,EAAE;AAG3B,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE,OAAO;AAClD,MAAI,CAAC,IAAI,MAAM,KAAK;AACpB,MAAI,CAAC,IAAI,MAAM,IAAI;AACnB,MAAI,CAAC,IAAI,IAAI;AAGb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,GAAG,EAAE,GAAG,EAAE,OAAO;AACnD,MAAI,CAAC,IAAI,IAAI;AAGb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO;AACpD,MAAI,CAAC,IAAI,IAAI;AAGb,MAAI,CAAC,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO;AACpD,MAAI,CAAC,IAAI,IAAI;AAIb,MAAI,EAAE,KAAK,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,KAAK,gBAAgB;AACnE,MAAI,EAAE,IAAI,IAAI,aAAc;AAC5B,MAAI,EAAE,IAAI,MAAM,KAAK;AACrB,MAAI,EAAE,IAAI,MAAM,KAAK;AACrB,MAAI,EAAE,IAAI,MAAM,IAAI;AACpB,MAAI,EAAE,IAAI,IAAI;AACd,SAAO;AACT;AACA,IAAO,gBAAQ;;;AC9Bf,IAAI,YAAY,CAAC;AACjB,KAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,YAAU,MAAM,IAAI,KAAO,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;AAClD;AAFS;AAGF,SAAS,gBAAgB,KAAK,SAAS,GAAG;AAM/C,UAAQ,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,UAAU,IAAI,SAAS,CAAC,CAAC,IAAI,MAAM,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC,GAAG,YAAY;AACngB;;;ACbA,IAAI;AACJ,IAAI,QAAQ,IAAI,WAAW,EAAE;AACd,SAAR,MAAuB;AAE5B,MAAI,CAAC,iBAAiB;AAEpB,sBAAkB,OAAO,WAAW,eAAe,OAAO,mBAAmB,OAAO,gBAAgB,KAAK,MAAM;AAC/G,QAAI,CAAC,iBAAiB;AACpB,YAAM,IAAI,MAAM,0GAA0G;AAAA,IAC5H;AAAA,EACF;AACA,SAAO,gBAAgB,KAAK;AAC9B;;;ACdA,SAAS,cAAc,KAAK;AAC1B,QAAM,SAAS,mBAAmB,GAAG,CAAC;AAEtC,MAAI,QAAQ,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,UAAM,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,EAC9B;AACA,SAAO;AACT;AACO,IAAI,MAAM;AACV,IAAI,MAAM;AACF,SAAR,IAAqB,MAAM,SAAS,UAAU;AACnD,WAAS,aAAa,OAAO,WAAW,KAAK,QAAQ;AACnD,QAAI;AACJ,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,cAAc,KAAK;AAAA,IAC7B;AACA,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,cAAM,SAAS;AAAA,IAC7B;AACA,UAAM,aAAa,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,YAAY,IAAI;AACpG,YAAM,UAAU,kEAAkE;AAAA,IACpF;AAKA,QAAI,QAAQ,IAAI,WAAW,KAAK,MAAM,MAAM;AAC5C,UAAM,IAAI,SAAS;AACnB,UAAM,IAAI,OAAO,UAAU,MAAM;AACjC,YAAQ,SAAS,KAAK;AACtB,UAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAO;AAC7B,UAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAO;AAC7B,QAAI,KAAK;AACP,eAAS,UAAU;AACnB,eAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,YAAI,SAAS,CAAC,IAAI,MAAM,CAAC;AAAA,MAC3B;AACA,aAAO;AAAA,IACT;AACA,WAAO,gBAAgB,KAAK;AAAA,EAC9B;AAGA,MAAI;AACF,iBAAa,OAAO;AAAA,EACtB,SAAS,KAAK;AAAA,EAAC;AAGf,eAAa,MAAM;AACnB,eAAa,MAAM;AACnB,SAAO;AACT;;;AClCA,SAAS,IAAI,OAAO;AAClB,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,MAAM,SAAS,mBAAmB,KAAK,CAAC;AAE5C,YAAQ,IAAI,WAAW,IAAI,MAAM;AACjC,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAM,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,IAC7B;AAAA,EACF;AACA,SAAO,qBAAqB,WAAW,aAAa,KAAK,GAAG,MAAM,SAAS,CAAC,CAAC;AAC/E;AAKA,SAAS,qBAAqB,OAAO;AACnC,MAAI,SAAS,CAAC;AACd,MAAI,WAAW,MAAM,SAAS;AAC9B,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AACpC,QAAI,IAAI,MAAM,KAAK,CAAC,MAAM,IAAI,KAAK;AACnC,QAAI,MAAM,SAAS,OAAO,OAAO,MAAM,IAAI,EAAI,IAAI,OAAO,OAAO,IAAI,EAAI,GAAG,EAAE;AAC9E,WAAO,KAAK,GAAG;AAAA,EACjB;AACA,SAAO;AACT;AAKA,SAAS,gBAAgB,cAAc;AACrC,UAAQ,eAAe,OAAO,KAAK,KAAK,KAAK;AAC/C;AAKA,SAAS,WAAW,GAAG,KAAK;AAE1B,IAAE,OAAO,CAAC,KAAK,OAAQ,MAAM;AAC7B,IAAE,gBAAgB,GAAG,IAAI,CAAC,IAAI;AAC9B,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,IAAI;AACrC,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,MAAM;AAC3C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,OAAO;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AAAA,EACrB;AACA,SAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AACpB;AAMA,SAAS,aAAa,OAAO;AAC3B,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,UAAU,MAAM,SAAS;AAC7B,MAAI,SAAS,IAAI,YAAY,gBAAgB,OAAO,CAAC;AACrD,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,WAAO,KAAK,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,QAAS,IAAI;AAAA,EACjD;AACA,SAAO;AACT;AAMA,SAAS,QAAQ,GAAG,GAAG;AACrB,MAAI,OAAO,IAAI,UAAW,IAAI;AAC9B,MAAI,OAAO,KAAK,OAAO,KAAK,OAAO,OAAO;AAC1C,SAAO,OAAO,KAAK,MAAM;AAC3B;AAKA,SAAS,cAAc,KAAK,KAAK;AAC/B,SAAO,OAAO,MAAM,QAAQ,KAAK;AACnC;AAKA,SAAS,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,SAAO,QAAQ,cAAc,QAAQ,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AAC3E;AACA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7C;AACA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7C;AACA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACxC;AACA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,SAAO,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3C;AACA,IAAO,cAAQ;;;AC/Lf,IAAI,KAAK,IAAI,MAAM,IAAM,WAAG;;;ACF5B,IAAI,aAAa,OAAO,WAAW,eAAe,OAAO,cAAc,OAAO,WAAW,KAAK,MAAM;AACpG,IAAO,iBAAQ;AAAA,EACb;AACF;;;ACAA,SAAS,GAAG,SAAS,KAAK,QAAQ;AAChC,MAAI,eAAO,cAAc,CAAC,OAAO,CAAC,SAAS;AACzC,WAAO,eAAO,WAAW;AAAA,EAC3B;AACA,YAAU,WAAW,CAAC;AACtB,MAAI,OAAO,QAAQ,WAAW,QAAQ,OAAO,KAAK;AAGlD,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAC3B,OAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAO;AAG3B,MAAI,KAAK;AACP,aAAS,UAAU;AACnB,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAI,SAAS,CAAC,IAAI,KAAK,CAAC;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,IAAI;AAC7B;AACA,IAAO,aAAQ;;;ACtBf,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG;AACrB,UAAQ,GAAG;AAAA,IACT,KAAK;AACH,aAAO,IAAI,IAAI,CAAC,IAAI;AAAA,IACtB,KAAK;AACH,aAAO,IAAI,IAAI;AAAA,IACjB,KAAK;AACH,aAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,IAC7B,KAAK;AACH,aAAO,IAAI,IAAI;AAAA,EACnB;AACF;AACA,SAAS,KAAK,GAAG,GAAG;AAClB,SAAO,KAAK,IAAI,MAAM,KAAK;AAC7B;AACA,SAAS,KAAK,OAAO;AACnB,MAAI,IAAI,CAAC,YAAY,YAAY,YAAY,UAAU;AACvD,MAAI,IAAI,CAAC,YAAY,YAAY,YAAY,WAAY,UAAU;AACnE,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,MAAM,SAAS,mBAAmB,KAAK,CAAC;AAE5C,YAAQ,CAAC;AACT,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,YAAM,KAAK,IAAI,WAAW,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF,WAAW,CAAC,MAAM,QAAQ,KAAK,GAAG;AAEhC,YAAQ,MAAM,UAAU,MAAM,KAAK,KAAK;AAAA,EAC1C;AACA,QAAM,KAAK,GAAI;AACf,MAAI,IAAI,MAAM,SAAS,IAAI;AAC3B,MAAI,IAAI,KAAK,KAAK,IAAI,EAAE;AACxB,MAAI,IAAI,IAAI,MAAM,CAAC;AACnB,WAAS,KAAK,GAAG,KAAK,GAAG,EAAE,IAAI;AAC7B,QAAI,MAAM,IAAI,YAAY,EAAE;AAC5B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,UAAI,CAAC,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,KAAK,KAAK,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC;AAAA,IACxI;AACA,MAAE,EAAE,IAAI;AAAA,EACV;AACA,IAAE,IAAI,CAAC,EAAE,EAAE,KAAK,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE;AACtD,IAAE,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;AACtC,IAAE,IAAI,CAAC,EAAE,EAAE,KAAK,MAAM,SAAS,KAAK,IAAI;AACxC,WAAS,MAAM,GAAG,MAAM,GAAG,EAAE,KAAK;AAChC,QAAI,IAAI,IAAI,YAAY,EAAE;AAC1B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,QAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AAAA,IACjB;AACA,aAAS,KAAK,IAAI,KAAK,IAAI,EAAE,IAAI;AAC/B,QAAE,EAAE,IAAI,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;AAAA,IACjE;AACA,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,aAAS,MAAM,GAAG,MAAM,IAAI,EAAE,KAAK;AACjC,UAAI,IAAI,KAAK,MAAM,MAAM,EAAE;AAC3B,UAAI,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,MAAM;AAC3D,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,GAAG,EAAE,MAAM;AACpB,UAAI;AACJ,UAAI;AAAA,IACN;AACA,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AACpB,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AAAA,EACtB;AACA,SAAO,CAAC,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,KAAK,KAAM,EAAE,CAAC,KAAK,IAAI,KAAM,EAAE,CAAC,IAAI,GAAI;AACjW;AACA,IAAO,eAAQ;;;ACzEf,IAAI,KAAK,IAAI,MAAM,IAAM,YAAI;;;;;;;;;;;AEOtB,eAAe,0CACpB,WACA,QACA,QACA,gBAAsD;AAEtD,MAAI;AACF,KAAA,GAAA,0CAAO,MAAM,uBAAuB,WAAW,MAAA;AAE/C,UAAM,UAAU,IAAI,QAAQ;MAC1B,GAAG,OAAO,aAAa,OAAO,WAAW,IAAI,QAAA,GAAW,QAAO,CAAA;IAChE,CAAA;AAED,QAAI,CAAC,QAAQ,IAAI,cAAA,EACf,SAAQ,IAAI,gBAAgB,kBAAA;AAE9B,YAAQ,IAAI,iBAAiB,UAAA;AAC7B,YAAQ,IAAI,cAAc,YAAA;AAG1B,UAAM,WAAW,MAAM,MAAM,WAAW;MACtC,QAAQ;;MAER,MAAM,KAAK,UAAU;QAAE,GAAG,OAAO;QAAa,SAAS;UAAC;;MAAO,CAAA;IAChE,CAAA;AAGD,UAAM,cAAc,SAAS,QAAQ,IAAI,cAAA;AAGzC,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,eAAe,MAAM,SAAS,KAAI;AACxC,YAAM,KAAI,GAAA,2CACR,6BAA6B,YAAA,IAC7B,SAAS,MAAM;IAEnB;AAEA,QAAI,SAAS,SAAQ,2CAAa,SAAS,uBAAsB;AAE/D,YAAM,SAAS,SAAS,KACrB,YAAY,IAAI,kBAAA,CAAA,EAChB,UAAS;AAEZ,UAAI,SAAS;AAEb,aAAO,MAAM;AACX,cAAM,EAAA,OAAO,KAAM,IAAK,MAAM,OAAO,KAAI;AACzC,YAAI,KAAM;AAEV,kBAAU;AAEV,YAAI,WAAW,OAAO,QAAQ,MAAA;AAC9B,eAAO,aAAa,IAAI;AACtB,gBAAM,UAAU,OAAO,MAAM,GAAG,QAAA;AAChC,mBAAS,OAAO,MAAM,WAAW,CAAA;AAGjC,gBAAM,QAAQ,QAAQ,MAAM,IAAA;AAC5B,cAAI,cAAc;AAClB,qBAAW,QAAQ,OAAO;AACxB,kBAAM,aAAa,KAAK,QAAQ,GAAA;AAChC,gBAAI,eAAe,GACjB,gBAAe,KAAK,MAAM,aAAa,CAAA,EAAG,KAAI;UAElD;AAEA,cAAI;AACF,kBAAM,WAAW,KAAK,WAAA;AACtB,kBAAM,aAAa,KAAK,MAAM,QAAA;AAC9B,2BAAe,UAAA;UACjB,SAAS,OAAO;AACd,aAAA,GAAA,0CAAO,MAAM,gCAAgC,KAAA;AAC7C,kBAAM;UACR;AAEA,qBAAW,OAAO,QAAQ,MAAA;QAC5B;MACF;IACF,OAAO;AAEL,YAAM,OAAO,MAAM,SAAS,KAAI;AAChC,qBAAe,IAAA;IACjB;EACF,SAAS,OAAO;AACd,KAAA,GAAA,0CAAO,MAAM,8BAA8B,KAAA;AAC3C,UAAM;EACR;AACF;;;;AEjGA,4BAAiB,KAAK,MAAM,85BAAA;;;;;;;;;AEMtB,IAAO,4CAAP,cAAyB,MAAA;EAG7B,YAAY,SAAkB,QAA9B;AACE,UAAM,OAAA;AACN,SAAK,SAAS;EAChB;AACD;AAEK,IAAO,4CAAP,cAAsC,0CAAA;EAC1C,YAAY,SAAZ;AACE,UACE,WACE,oEAAA;EAEN;AACD;AAEK,IAAO,4CAAP,cAA6B,0CAAA;EAEjC,YAAY,SAA8B,QAA1C;AACE,UACE,WAAW,yDACX,UAAU,GAAA;AAJL,SAAA,QAAgB;EAMzB;AACD;AAEK,IAAO,2CAAP,cAAmC,0CAAA;EACvC,YAAY,SAAZ;AACE,UAAM,WAAW,gCAAA;EACnB;AACD;AAEK,IAAO,4CAAP,cAAgC,0CAAA;EACpC,YAAY,SAAZ;AACE,UACE,WACE,gEAAA;EAEN;AACD;AAEK,IAAO,4CAAP,cAAiC,0CAAA;EAErC,YAAY,SAAZ;AACE,UAAM,WAAW,gCAAA;AAFD,SAAA,SAAS;EAG3B;AACD;AAEK,IAAO,4CAAP,cAAyC,0CAAA;EAC7C,YAAY,SAAZ;AACE,UAAM,WAAW,4BAAA;EACnB;AACD;ADnDK,SAAU,0CACd,SACA,aACA,YAA8B;AAE9B,QAAM,iBAAiB,WAAW;AAElC,aAAW,QAAQ,YAAsB,MAAe;AACtD,QAAI,KAAK,UAAU,QACjB,QAAO,eAAe,MAAM,MAAM,IAAA;QAElC,OAAM,KAAI,GAAA,2CACR,mBAAmB,YAAY,SAAQ,CAAA,4DAA8D;EAG3G;AAEA,SAAO;AACT;AAuBM,SAAU,6CACX,QAAgB;AAEnB,WAAS;IAAC;OAAY;;AAEtB,SAAO,SACL,SACA,aACA,YAA8B;AAE9B,UAAM,iBAAiB,WAAW;AAElC,eAAW,MAAM,WAAA;AACf,UAAI,OAAO,SAAS,KAAK,KAAK,EAC5B,QAAO,iDAAgB,MAAM;UAE7B,OAAM,KAAI,GAAA,2CACR,mBAAmB,YAAY,SAAQ,CAAA,0BAA4B,MAAA,0BAAgC;IAGzG;AAEA,WAAO;EACT;AACF;;;AEtDA,IAAY;CAAZ,SAAY,WAAS;AACnB,YAAA,cAAA,IAAA;AACA,YAAA,OAAA,IAAA;AAEA,YAAA,WAAA,IAAA;AACA,YAAA,cAAA,IAAA;AACA,YAAA,uBAAA,IAAA;AAEA,YAAA,QAAA,IAAA;AACA,YAAA,gBAAA,IAAA;AACA,YAAA,kBAAA,IAAA;AAEA,YAAA,sBAAA,IAAA;AACA,YAAA,iBAAA,IAAA;AACA,YAAA,cAAA,IAAA;AACA,YAAA,cAAA,IAAA;AACA,YAAA,oBAAA,IAAA;AACA,YAAA,oBAAA,IAAA;AACA,YAAA,kBAAA,IAAA;AAEA,YAAA,sBAAA,IAAA;AACA,YAAA,sBAAA,IAAA;AACA,YAAA,0BAAA,IAAA;AACA,YAAA,YAAA,IAAA;AACA,YAAA,YAAA,IAAA;AACA,YAAA,gBAAA,IAAA;AAEA,YAAA,cAAA,IAAA;AACA,YAAA,UAAA,IAAA;AACA,YAAA,iBAAA,IAAA;AACA,YAAA,oBAAA,IAAA;AACA,YAAA,oBAAA,IAAA;AACA,YAAA,kBAAA,IAAA;AAEA,YAAA,qBAAA,IAAA;AACA,YAAA,qBAAA,IAAA;AACA,YAAA,iBAAA,IAAA;AAEA,YAAA,SAAA,IAAA;AAEA,YAAA,gBAAA,IAAA;AACA,YAAA,eAAA,IAAA;AAEA,YAAA,YAAA,IAAA;AACA,YAAA,eAAA,IAAA;AACA,YAAA,eAAA,IAAA;AAEA,YAAA,YAAA,IAAA;AACA,YAAA,eAAA,IAAA;AACA,YAAA,eAAA,IAAA;AAEA,YAAA,iBAAA,IAAA;AACA,YAAA,sBAAA,IAAA;AACA,YAAA,mBAAA,IAAA;AAEA,YAAA,mBAAA,IAAA;AAEA,YAAA,sBAAA,IAAA;AACA,YAAA,eAAA,IAAA;AACF,GA3DY,8CAAA,4CAAS,CAAA,EAAA;;;ACAf,IAAgB,4CAAhB,MAAgB;EAKpB,YAAY,SAAZ;AACE,SAAK,WAAW;EAClB;EAIA,IAAW,OAAO,QAAlB;AACE,SAAK,UAAU;EACjB;EACA,IAAW,QAAQ,SAAnB;AACE,SAAK,WAAW;EAClB;AACD;;;;AC/BD,IAAY;CAAZ,SAAY,UAAQ;AAClB,WAAA,SAAA,MAAA,IAAA,CAAA,IAAA;AACA,WAAA,SAAA,OAAA,IAAA,CAAA,IAAA;AACA,WAAA,SAAA,MAAA,IAAA,CAAA,IAAA;AACA,WAAA,SAAA,MAAA,IAAA,CAAA,IAAA;AACA,WAAA,SAAA,OAAA,IAAA,CAAA,IAAA;AACF,GANY,8CAAA,4CAAQ,CAAA,EAAA;AAQpB,IAAM,+BAAN,MAAM,8BAAA;EAIJ,cAAA;AAFQ,SAAA,QAAkB,0CAAS;EAEZ;EAEvB,OAAO,cAAP;AACE,QAAI,CAAC,8BAAO,SACV,+BAAO,WAAW,IAAI,8BAAA;AAExB,WAAO,8BAAO;EAChB;EAEA,SAAS,OAAT;AACE,SAAK,QAAQ;EACf;EAEA,SAAS,MAAT;AACE,QAAI,KAAK,SAAS,0CAAS,MACzB,SAAQ,MAAK,GAAI,IAAA;EAErB;EAEA,QAAQ,MAAR;AACE,QAAI,KAAK,SAAS,0CAAS,KACzB,SAAQ,KAAI,GAAI,IAAA;EAEpB;EAEA,QAAQ,MAAR;AACE,QAAI,KAAK,SAAS,0CAAS,KACzB,SAAQ,KAAI,GAAI,IAAA;EAEpB;EAEA,SAAS,MAAT;AACE,QAAI,KAAK,SAAS,0CAAS,MACzB,SAAQ,MAAK,GAAI,IAAA;EAErB;AACD;AAEM,IAAM,2CAAS,6BAAO,YAAW;;;;;;;AC3CjC,IAAM,4CAAqB;AAElC,IAAY;CAAZ,SAAY,iBAAe;AAEzB,kBAAA,cAAA,IAAA;AACA,kBAAA,eAAA,IAAA;AACA,kBAAA,YAAA,IAAA;AACA,kBAAA,iBAAA,IAAA;AACA,kBAAA,kBAAA,IAAA;AACA,kBAAA,gBAAA,IAAA;AACA,kBAAA,QAAA,IAAA;AAGA,kBAAA,WAAA,IAAA;AACA,kBAAA,OAAA,IAAA;AACA,kBAAA,gBAAA,IAAA;AACA,kBAAA,QAAA,IAAA;AACA,kBAAA,kBAAA,IAAA;AACA,kBAAA,cAAA,IAAA;AACA,kBAAA,mBAAA,IAAA;AACA,kBAAA,iBAAA,IAAA;AACA,kBAAA,SAAA,IAAA;AACA,kBAAA,oBAAA,IAAA;AACA,kBAAA,mBAAA,IAAA;AACA,kBAAA,uBAAA,IAAA;AACA,kBAAA,uBAAA,IAAA;AACA,kBAAA,sBAAA,IAAA;AACA,kBAAA,sBAAA,IAAA;AAEA,kBAAA,eAAA,IAAA;AACA,kBAAA,cAAA,IAAA;AACA,kBAAA,iBAAA,IAAA;AACA,kBAAA,iBAAA,IAAA;AACA,kBAAA,cAAA,IAAA;AACA,kBAAA,iBAAA,IAAA;AACA,kBAAA,iBAAA,IAAA;AACA,kBAAA,yBAAA,IAAA;AAEA,kBAAA,qBAAA,IAAA;AAEA,kBAAA,gBAAA,IAAA;AACF,GAvCY,8CAAA,4CAAe,CAAA,EAAA;AAsHrB,IAAO,2CAAP,MAAO,0CAAA;EAMX,YAAY,MAAc,MAAe,IAAzC;AAJA,SAAA,QAAgB;AAKd,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,KAAK,OAAM,GAAA,YAAA,EAAS,MAAM,GAAG,CAAA;EACpC;;EAGA,OAAO,cAAP;AACE,WAAO,IAAI,0CAAY,0CAAgB,cAAc,CAAA,CAAA;EACvD;EAEA,OAAO,aACL,QACA,YAAqB,OAFvB;AAIE,WAAO,IAAI,0CAAY,0CAAgB,eAAe;;;IAGrD,CAAA;EACH;EAEA,OAAO,iBAAP;AACE,WAAO,IAAI,0CAAY,0CAAgB,iBAAiB,CAAA,CAAA;EAC1D;EAEA,OAAO,eAAP;AACE,WAAO,IAAI,0CAAY,0CAAgB,YAAY,CAAA,CAAA;EACrD;EAEA,OAAO,kBAAP;AACE,WAAO,IAAI,0CAAY,0CAAgB,kBAAkB,CAAA,CAAA;EAC3D;EAEA,OAAO,gBAAP;AACE,WAAO,IAAI,0CAAY,0CAAgB,gBAAgB,CAAA,CAAA;EACzD;EAEA,OAAO,MAAM,SAAiB,QAAQ,OAAtC;AACE,WAAO,IAAI,0CAAY,0CAAgB,OAAO;;;IAAgB,CAAA;EAChE;AACD;AAUK,IAAO,4CAAP,cAAiC,yCAAA;EACrC,YAAY,MAAZ;AACE,UAAM,0CAAgB,QAAQ,IAAA;EAChC;AACD;AAkBK,IAAO,4CAAP,MAAO;EAKX,YAAY,QAAZ;AAFQ,SAAA,SAAS,IAAI,MAAA;AAGnB,SAAK,UAAU;AACf,SAAK,SAAS,CAAA;AACd,SAAK,UAAU;EACjB;EAEO,SAAS,SAAT;AACL,UAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAA;AACpC,WAAK,OAAO,KAAK;;QAEf,WAAW,KAAK,IAAG;;;MAGpB,CAAA;IACH,CAAA;AAEA,KAAA,GAAA,0CAAO,MAAM,gCAAgC,OAAA;AAE7C,SAAK,QAAQ,YAAY,OAAA;AAEzB,SAAK,IAAG;AAER,WAAO;EACT;EAEO,MAAM,eACX,QACA,WAFK;;AAIL,UAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAA;AACpC,WAAK,OAAO,KAAK;QACf,SAAS;QACT,WAAW,KAAK,IAAG;;;MAGpB,CAAA;IACH,CAAA;AAEA,KAAA,GAAA,0CAAO,MAAM,8BAA8B,MAAA;AAE3C,QAAI,KAAK,QAAQ;AAEf,WAAK,QAAQ,YAAY,MAAA;SACpB;AACL,UAAI,GAAC,UAAK,QAAQ,OAAO,cAApB,mBAA+B,SAAQ;AAC1C,SAAA,GAAA,0CAAO,MACL,+FAAA;AAEF,cAAM,KAAI,GAAA,2CAAA;MACZ;AACA,YAAM,YAAY,KAAK,QAAQ,aAAa,QAAA;AAE5C,UAAI;AAEF,eAAM,GAAA,2CACJ,WACA,QACA,KAAK,QAAQ,QACb,CAAC,aAAA;AACC,oBAAU,QAAA;QACZ,CAAA;MAGJ,SAAS,GAAG;AACV,kBACE,IAAI,yCACF,0CAAgB,gBAChB,oBAAoB,SAAA,gCACpB,OAAO,EAAE,CAAA;MAGf;IACF;AAEA,SAAK,IAAG;AAER,WAAO;EACT;EAEQ,eACN,SACA,UAAmB,MAFb;AAIN,UAAM,gBAAgB,KAAK,OAAO,KAChC,CAAC,QAAQ,IAAI,QAAQ,OAAO,QAAQ,EAAE;AAGxC,QAAI,eAAe;AACjB,UAAI,SAAS;AACX,SAAA,GAAA,0CAAO,MAAM,+BAA+B,OAAA;AAC5C,sBAAc,QACZ,QAAQ,SAAS,0CAAgB,kBAC5B,UACA,OAAA;MAET,OAAO;AACL,SAAA,GAAA,0CAAO,MAAM,8BAA8B,OAAA;AAC3C,sBAAc,OAAO,OAAA;MACvB;AAEA,WAAK,SAAS,KAAK,OAAO,OAAO,CAAC,QAAQ,IAAI,QAAQ,OAAO,QAAQ,EAAE;AACvE,OAAA,GAAA,0CAAO,MAAM,6BAA6B,KAAK,MAAM;IACvD;AAEA,WAAO;EACT;EAEO,QAAQ,SAAR;AACL,WAAO,KAAK,eAAe,SAAS,IAAA;EACtC;EAEO,OAAO,SAAP;AACL,WAAO,KAAK,eAAe,SAAS,KAAA;EACtC;EAEQ,MAAA;AACN,SAAK,SAAS,KAAK,OAAO,OAAO,CAAC,QAAA;AAChC,aAAO,KAAK,IAAG,IAAK,IAAI,YAAY,KAAK;IAC3C,CAAA;AACA,KAAA,GAAA,0CAAO,MAAM,0BAA0B,KAAK,MAAM;EACpD;AACD;;;;;;;APlSD,IAAM,yCAA4D;EAChE,SAAS;EACT,QAAQ;AACT;AAED,IAAM,yCAAN,MAAM;EAIJ,YAAY,WAAZ;AACE,SAAK,aAAa;AAClB,SAAK,SAAS,IAAI,MAAM,KAAK,YAAY;MACvC,KAAK,CAAC,QAAQ,MAAM,aAAA;AAClB,YAAI,OAAO,OAAO,IAAA,MAA6B,YAAY;AACzD,cAAI;AACJ,kBAAQ,OAAO,IAAA,GAAA;YAGb,KAAK;AACH,uBAAS;AACT;YACF,KAAK;AACH,uBAAS;AACT;YACF,KAAK;AACH,uBAAS;AACT;YACF,KAAK;AACH,uBAAS;AACT;UACJ;AACA,cAAI,OACF,QAAO,MAAA;AACL,kBAAM,IAAI,MAAM,MAAA;UAClB;AAGF,iBAAO,IAAI,SAAA;AACT,mBAAQ,OAAO,IAAA,EAAqC,GAAI,IAAA;UAC1D;QACF;AAEA,eAAO,QAAQ,IAAI,QAAQ,MAAM,QAAA;MACnC;IACD,CAAA;EACH;EAEA,IAAI,QAAJ;AACE,WAAO,KAAK;EACd;AACD;AA2HD,IAAe,yCAAf,eAAyC,GAAA,cAAAC,SAAA;AAAiE;AAEpG,IAAO,4CAAP,cAA0B,uCAAA;EAW9B,YAAY,SAAZ;AACE,UAAK;AAEL,SAAK,SAAS;MACZ,GAAG,QAAQ;MACX,WAAW;QACT,GAAG;QACH,GAAI,QAAQ,OAAO,aAAa,CAAA;MACjC;IACF;AAED,SAAK,WAAW,CAAA;AAChB,SAAK,aAAa,QAAQ;AAC1B,SAAK,oBAAoB,IAAI,uCAAiB,KAAK,UAAU;AAI7D,UAAM,mBAAuC;MAC3C,GAAG,QAAQ;MACX,gBAAgB,CAAC,YAAA;;AACf,uDAAS,cAAT,mBAAoB,mBAApB,4BAAqC;AACrC,aAAK,MAAK,GAAA,2CAAU,cAAc,OAAA;MACpC;MACA,SAAS,CAAC,YAAA;;AACR,uDAAS,cAAT,mBAAoB,YAApB,4BAA8B;AAC9B,YAAI;AACF,eAAK,MAAK,GAAA,2CAAU,OAAO,OAAA;QAC7B,SAAS,GAAG;AACV,WAAA,GAAA,0CAAO,MAAM,wBAAwB,OAAA;QACvC;AACA,cAAM,OAAO,QAAQ;AACrB,YAAI,6BAAM,OAAO;AACf,WAAA,GAAA,0CAAO,MAAM,wCAAA;AACb,eAAK,WAAU;QACjB;MACF;MACA,aAAa,MAAA;;AACX,uDAAS,cAAT,mBAAoB,gBAApB;AACA,aAAK,MAAK,GAAA,2CAAU,SAAS;MAC/B;MACA,gBAAgB,MAAA;;AACd,uDAAS,cAAT,mBAAoB,mBAApB;AACA,aAAK,MAAK,GAAA,2CAAU,YAAY;MAClC;MACA,yBAAyB,CAAC,UAAA;;AACxB,uDAAS,cAAT,mBAAoB,4BAApB,4BAA8C;AAC9C,aAAK,MAAK,GAAA,2CAAU,uBAAuB,KAAA;MAC7C;MACA,UAAU,CAAC,WAAA;;AACT,uDAAS,cAAT,mBAAoB,aAApB,4BAA+B;AAC/B,aAAK,MAAK,GAAA,2CAAU,QAAQ,MAAA;MAC9B;MACA,kBAAkB,CAAC,sBAAA;;AACjB,uDAAS,cAAT,mBAAoB,qBAApB,4BAAuC;AACvC,aAAK,MAAK,GAAA,2CAAU,gBAAgB,iBAAA;MACtC;MACA,oBAAoB,CAAC,qBAAA;;AACnB,uDAAS,cAAT,mBAAoB,uBAApB,4BAAyC;AACzC,aAAK,MAAK,GAAA,2CAAU,kBAAkB,gBAAA;MACxC;MACA,qBAAqB,CAAC,MAAA;;AACpB,uDAAS,cAAT,mBAAoB,wBAApB,4BAA0C;AAC1C,aAAK,MAAK,GAAA,2CAAU,sBAAsB,CAAA;MAC5C;MACA,mBAAmB,CAAC,MAAA;;AAClB,uDAAS,cAAT,mBAAoB,sBAApB,4BAAwC;AACxC,aAAK,MAAK,GAAA,2CAAU,iBAAiB,CAAA;MACvC;MACA,gBAAgB,CAAC,OAAO,MAAA;;AACtB,uDAAS,cAAT,mBAAoB,mBAApB,4BAAqC,OAAO;AAC5C,aAAK,MAAK,GAAA,2CAAU,cAAc,OAAO,CAAA;MAC3C;MACA,gBAAgB,CAAC,OAAO,MAAA;;AACtB,uDAAS,cAAT,mBAAoB,mBAApB,4BAAqC,OAAO;AAC5C,aAAK,MAAK,GAAA,2CAAU,cAAc,OAAO,CAAA;MAC3C;MACA,sBAAsB,CAAC,OAAO,MAAA;;AAC5B,uDAAS,cAAT,mBAAoB,yBAApB,4BAA2C,OAAO;AAClD,aAAK,MAAK,GAAA,2CAAU,oBAAoB,OAAO,CAAA;MACjD;MACA,sBAAsB,CAAC,OAAO,MAAA;;AAC5B,uDAAS,cAAT,mBAAoB,yBAApB,4BAA2C,OAAO;AAClD,aAAK,MAAK,GAAA,2CAAU,oBAAoB,OAAO,CAAA;MACjD;MACA,oBAAoB,CAAC,iBAAA;;AACnB,uDAAS,cAAT,mBAAoB,uBAApB,4BAAyC;AACzC,aAAK,MAAK,GAAA,2CAAU,kBAAkB,YAAA;MACxC;MACA,wBAAwB,CAAC,SAAA;;AACvB,uDAAS,cAAT,mBAAoB,2BAApB,4BAA6C;AAC7C,aAAK,MAAK,GAAA,2CAAU,sBAAsB,IAAA;MAC5C;MACA,wBAAwB,CAAC,SAAA;;AACvB,uDAAS,cAAT,mBAAoB,2BAApB,4BAA6C;AAC7C,aAAK,MAAK,GAAA,2CAAU,sBAAsB,IAAA;MAC5C;MACA,4BAA4B,CAAC,aAAA;;AAC3B,uDAAS,cAAT,mBAAoB,+BAApB,4BAAiD;AACjD,aAAK,MAAK,GAAA,2CAAU,0BAA0B,QAAA;MAChD;MACA,cAAc,CAAC,QAAA;;AACb,uDAAS,cAAT,mBAAoB,iBAApB,4BAAmC;AACnC,aAAK,MAAK,GAAA,2CAAU,YAAY,GAAA;MAClC;MACA,cAAc,CAAC,QAAA;;AACb,uDAAS,cAAT,mBAAoB,iBAApB,4BAAmC;AACnC,aAAK,MAAK,GAAA,2CAAU,YAAY,GAAA;MAClC;MACA,kBAAkB,CAAC,YAAA;;AACjB,uDAAS,cAAT,mBAAoB,qBAApB,4BAAuC;AACvC,aAAK,MAAK,GAAA,2CAAU,gBAAgB,OAAA;MACtC;MACA,gBAAgB,CAAC,MAAA;;AACf,uDAAS,cAAT,mBAAoB,mBAApB,4BAAqC;AACrC,aAAK,MAAK,GAAA,2CAAU,cAAc,CAAA;MACpC;MACA,YAAY,CAAC,iBAAA;;AACX,uDAAS,cAAT,mBAAoB,eAApB,4BAAiC;AACjC,aAAK,MAAK,GAAA,2CAAU,UAAU,YAAA;MAChC;MACA,mBAAmB,CAAC,MAAA;;AAClB,uDAAS,cAAT,mBAAoB,sBAApB,4BAAwC;AACxC,aAAK,MAAK,GAAA,2CAAU,iBAAiB,CAAA;MACvC;MACA,sBAAsB,MAAA;;AACpB,uDAAS,cAAT,mBAAoB,yBAApB;AACA,aAAK,MAAK,GAAA,2CAAU,kBAAkB;MACxC;MACA,sBAAsB,MAAA;;AACpB,uDAAS,cAAT,mBAAoB,yBAApB;AACA,aAAK,MAAK,GAAA,2CAAU,kBAAkB;MACxC;MACA,oBAAoB,CAAC,OAAO,MAAA;;AAC1B,uDAAS,cAAT,mBAAoB,uBAApB,4BAAyC,OAAO;AAChD,aAAK,MAAK,GAAA,2CAAU,kBAAkB,OAAO,CAAA;MAC/C;MACA,uBAAuB,MAAA;;AACrB,uDAAS,cAAT,mBAAoB,0BAApB;AACA,aAAK,MAAK,GAAA,2CAAU,mBAAmB;MACzC;MACA,uBAAuB,MAAA;;AACrB,uDAAS,cAAT,mBAAoB,0BAApB;AACA,aAAK,MAAK,GAAA,2CAAU,mBAAmB;MACzC;MACA,mBAAmB,CAAC,UAAA;;AAClB,uDAAS,cAAT,mBAAoB,sBAApB,4BAAwC;AACxC,aAAK,MAAK,GAAA,2CAAU,iBAAiB,KAAA;MACvC;MACA,kBAAkB,CAAC,SAAA;;AACjB,uDAAS,cAAT,mBAAoB,qBAApB,4BAAuC;AACvC,aAAK,MAAK,GAAA,2CAAU,gBAAgB,IAAA;MACtC;MACA,iBAAiB,CAAC,SAAA;;AAChB,uDAAS,cAAT,mBAAoB,oBAApB,4BAAsC;AACtC,aAAK,MAAK,GAAA,2CAAU,eAAe,IAAA;MACrC;MACA,cAAc,CAAC,SAAA;;AACb,uDAAS,cAAT,mBAAoB,iBAApB,4BAAmC;AACnC,aAAK,MAAK,GAAA,2CAAU,YAAY,IAAA;MAClC;MACA,iBAAiB,MAAA;;AACf,uDAAS,cAAT,mBAAoB,oBAApB;AACA,aAAK,MAAK,GAAA,2CAAU,aAAa;MACnC;MACA,iBAAiB,MAAA;;AACf,uDAAS,cAAT,mBAAoB,oBAApB;AACA,aAAK,MAAK,GAAA,2CAAU,aAAa;MACnC;MACA,cAAc,CAAC,SAAA;;AACb,uDAAS,cAAT,mBAAoB,iBAApB,4BAAmC;AACnC,aAAK,MAAK,GAAA,2CAAU,YAAY,IAAA;MAClC;MACA,iBAAiB,MAAA;;AACf,uDAAS,cAAT,mBAAoB,oBAApB;AACA,aAAK,MAAK,GAAA,2CAAU,aAAa;MACnC;MACA,iBAAiB,MAAA;;AACf,uDAAS,cAAT,mBAAoB,oBAApB;AACA,aAAK,MAAK,GAAA,2CAAU,aAAa;MACnC;MACA,qBAAqB,CAAC,SAAA;;AACpB,uDAAS,cAAT,mBAAoB,wBAApB,4BAA0C;AAC1C,aAAK,MAAK,GAAA,2CAAU,mBAAmB,IAAA;MACzC;IACD;AAGD,SAAK,WAAW;MACd,GAAG;MACH,WAAW;MACX,WAAW,QAAQ,aAAa;MAChC,WAAW,QAAQ,aAAa;IACjC;AAGD,SAAK,YAAW;AAGhB,KAAA,GAAA,0CAAO,MAAM,6BAA6B,KAAK,OAAO;EACxD;EAEO,aAAa,UAAb;;AACL,QAAI,CAAC,KAAK,OAAO,QACf,OAAM,IAAI,0CACR,wDAAA;AAGJ,UAAM,UAAU,KAAK,OAAO,QAAQ,QAAQ,QAAQ,EAAA;AACpD,WAAO,aAAW,UAAK,OAAO,cAAZ,mBAAwB,cAAa;EACzD;EAEO,YAAY,OAAZ;AACL,KAAA,GAAA,0CAAO,SAAS,KAAA;EAClB;;;;;EAOO,MAAM,cAAN;AACL,KAAA,GAAA,0CAAO,MAAM,uCAAA;AACb,UAAM,KAAK,WAAW,YAAW;EACnC;;;;;EAMO,MAAM,UAAN;AACL,QACE;MAAC;MAAkB;MAAc;MAAa;MAAS,SACrD,KAAK,WAAW,KAAK,EAGvB,OAAM,IAAI,0CACR,wFAAA;AAIJ,SAAK,mBAAmB,IAAI,gBAAA;AAG5B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAA;AAC1B,OAAA,YAAA;;AACC,aAAK,gBAAgB;AAErB,YAAI,KAAK,WAAW,UAAU,eAC5B,OAAM,KAAK,WAAW,YAAW;AAGnC,aAAK,WAAW,QAAQ;AAGxB,YAAI,KAAK,SAAS,QAChB,MAAK,oBAAoB,WAAW,YAAA;;AAClC,WAAAC,MAAA,KAAK,qBAAL,gBAAAA,IAAuB;AACvB,gBAAM,KAAK,WAAU;AACrB,eAAK,WAAW,QAAQ;AACxB,iBAAO,IAAI,0CAAA,CAAA;QACb,GAAG,KAAK,SAAS,OAAO;AAG1B,YAAI;AACJ,cAAM,uBAAuB,KAAK,SAAS;AAE3C,SAAA,GAAA,0CAAO,MAAM,8BAA8B,KAAK,MAAM;AAEtD,aAAK,SAAS;UACZ,GAAG,KAAK;UACR,aAAa;YACX,GAAG,KAAK,OAAO;YACf,qBAAqB,KAAK;UAC3B;QACF;AAED,YAAI,CAAC,KAAK,OAAO,WAAW,GAAC,UAAK,OAAO,cAAZ,mBAAuB,UAAS;AAI3D,WAAA,GAAA,0CAAO,MACL,kEAAA;AAEF,uBAAa,KAAK,iBAAiB;QACrC,OAAO;AACL,gBAAM,aAAa,KAAK,aAAa,SAAA;AAErC,WAAA,GAAA,0CAAO,MAAM,+BAA+B,UAAA;AAC5C,WAAA,GAAA,0CAAO,MAAM,8BAA8B,KAAK,MAAM;AAEtD,cAAI;AACF,gBAAI,qBACF,cAAa,MAAM,qBACjB,KAAK,QACL,KAAK,mBACL,KAAK,gBAAiB;gBAGxB,cAAa,MAAM,MAAM,YAAY;cACnC,QAAQ;cACR,MAAM;cACN,SAAS,IAAI,QAAQ;gBACnB,gBAAgB;gBAChB,GAAG,OAAO,aACP,KAAK,OAAO,WAAW,IAAI,QAAA,GAAW,QAAO,CAAA;cAEjD,CAAA;cACD,MAAM,KAAK,UAAU;gBACnB,QAAQ,KAAK,OAAO;gBACpB,GAAI,KAAK,OAAO,WACZ;kBAAE,UAAU,KAAK,OAAO;gBAAQ,IAChC,CAAA;gBACJ,GAAG,KAAK,OAAO;cAChB,CAAA;cACD,SAAQ,UAAK,qBAAL,mBAAuB;YAChC,CAAA,EAAE,KAAK,CAAC,QAAA;AACP,2BAAa,KAAK,iBAAiB;AAEnC,kBAAI,IAAI,GACN,QAAO,IAAI,KAAI;AAGjB,qBAAO,QAAQ,OAAO,GAAA;YACxB,CAAA;UAEJ,SAAS,GAAG;AACV,yBAAa,KAAK,iBAAiB;AAEnC,iBAAI,UAAK,qBAAL,mBAAuB,OAAO,QAChC;AAEF,iBAAK,WAAW,QAAQ;AACxB,gBAAI,aAAa,UAAU;AACzB,oBAAM,YAAY,MAAM,EAAE,KAAI;AAC9B,qBACE,IAAI,0CACF,UAAU,QAAQ,UAAU,UAAU,EAAE,YACxC,EAAE,MAAM,CAAA;YAGd,MACE,QAAO,IAAI,0CAAA,CAAA;AAEb;UACF;AAEA,WAAA,GAAA,0CAAO,MAAM,sCAAsC,UAAA;QACrD;AACA,YAAI;AACF,gBAAM,KAAK,WAAW,QACpB,YACA,KAAK,gBAAmC;AAE1C,gBAAM,KAAK,WAAW,iBAAgB;QACxC,SAAS,GAAG;AACV,uBAAa,KAAK,iBAAiB;AACnC,eAAK,WAAU;AACf,iBAAO,CAAA;AACP;QACF;MACF,GAAA;IACF,CAAA;EACF;;;;;EAMO,MAAM,aAAN;AACL,QAAI,KAAK,iBACP,MAAK,iBAAiB,MAAK;AAG7B,iBAAa,KAAK,iBAAiB;AAEnC,UAAM,KAAK,WAAW,WAAU;AAEhC,SAAK,qBAAqB,KAAI,GAAA,2CAAkB,IAAI;EACtD;EAEQ,cAAA;AACN,SAAK,WAAW,WAAW,KAAK,UAAU,KAAK,cAAc,KAAK,IAAI,CAAA;AAGtE,SAAK,qBAAqB,KAAI,GAAA,2CAAkB,IAAI;EACtD;;;;EAKA,IAAW,YAAX;AACE,WAAO;MAAC;MAAa;MAAS,SAAS,KAAK,WAAW,KAAK;EAC9D;EAEA,IAAW,YAAX;AACE,WAAO,KAAK,kBAAkB;EAChC;EAEA,IAAW,QAAX;AACE,WAAO,KAAK,WAAW;EACzB;EAEA,IAAW,UAAX;AACE,YAAO,GAAA,uBAAA,yBAAA,GAAY;EACrB;;EAIO,MAAM,aAAN;AACL,WAAO,MAAM,KAAK,WAAW,WAAU;EACzC;EAEO,MAAM,aAAN;AACL,WAAO,MAAM,KAAK,WAAW,WAAU;EACzC;EAEO,MAAM,iBAAN;AACL,WAAO,MAAM,KAAK,WAAW,eAAc;EAC7C;EAEA,IAAW,cAAX;AACE,WAAO,KAAK,WAAW;EACzB;EAEA,IAAW,cAAX;AACE,WAAO,KAAK,WAAW;EACzB;EAEA,IAAW,kBAAX;AACE,WAAO,KAAK,WAAW;EACzB;EAEO,UAAU,OAAV;AACL,SAAK,WAAW,UAAU,KAAA;EAC5B;EAEO,UAAU,OAAV;AACL,SAAK,WAAW,UAAU,KAAA;EAC5B;EAEO,cAAc,WAAd;AACL,SAAK,WAAW,cAAc,SAAA;EAChC;EAEO,UAAU,QAAV;AACL,SAAK,WAAW,UAAU,MAAA;EAC5B;EAEA,IAAW,eAAX;AACE,WAAO,KAAK,WAAW;EACzB;EAEO,UAAU,QAAV;AACL,SAAK,WAAW,UAAU,MAAA;EAC5B;EAEA,IAAW,eAAX;AACE,WAAO,KAAK,WAAW;EACzB;EAEO,SAAA;AACL,WAAO,KAAK,WAAW,OAAM;EAC/B;EAEO,kBAAkB,QAAlB;AACL,WAAO,KAAK,WAAW,kBAAkB,MAAA;EAC3C;EAEA,IAAW,kBAAX;AACE,WAAO,KAAK,WAAW;EACzB;;;;;;EASO,MAAM,YAAA;AACX,UAAM,YAAY,MAAM,KAAK,mBAAmB,UAC9C,GAAA,0CAAY,aAAY,CAAA;AAE1B,WAAQ,UAAU,KAAoB;EACxC;;;;;;;EASO,MAAM,aACX,QACA,YAAqB,OAFV;AAIX,KAAA,GAAA,0CAAO,MAAM,iCAAiC,MAAA;AAG9C,WAAO,KAAK,mBAAmB,UAC7B,GAAA,0CAAY,aAAa,QAAQ,SAAA,CAAA;EAErC;;;;;EAOO,MAAM,iBAAA;AACX,WAAO,KAAK,mBAAmB,UAAS,GAAA,0CAAY,eAAc,CAAA;EACpE;;;;;;;EAQO,MAAM,4BACX,YACA,QAFK;AAIL,QAAI,CAAC,UAAU,KAAK,UAAU,QAC5B,OAAM,IAAI,0CACR,6EAAA;AAIJ,WAAO,QAAQ,QAAO,EAAG,KAAK,YAAA;AAE5B,UAAI,CAAC,YAAY;AACf,SAAA,GAAA,0CAAO,MAAM,iCAAA;AACb,eAAO;MACT;AAEA,YAAM,eACJ,UAAW,MAAM,KAAK,UAAS;AAGjC,YAAM,mBAAmB,aAAa,KACpC,CAACC,YAAmCA,QAAO,YAAY,UAAA;AAGzD,UAAI,CAAC,kBAAkB;AACrB,SAAA,GAAA,0CAAO,MACL,0BAA0B,aAAa,sBAAA;AAEzC,eAAO;MACT;AAGA,aAAO;IACT,CAAA;EACF;;;;;;;EAQO,MAAM,gCACX,YACA,QACA,QAHK;AAKL,UAAM,mBACJ,MAAM,KAAK,4BAA4B,YAAY,MAAA;AAErD,QAAI,CAAC,kBAAkB;AACrB,OAAA,GAAA,0CAAO,MAAM,uBAAuB,aAAa,sBAAA;AACjD,aAAO;IACT;AAGA,UAAM,cAAwC,iBAAiB,QAAQ,KACrE,CAAC,MAAoB,EAAE,SAAS,MAAA;AAGlC,WAAO,cAAe,YAA6B,QAAQ;EAC7D;EAEQ,mBACN,iBACA,WAFM;AAIN,UAAM,sBAAsB,gBAAgB,UAC1C,CAAC,SAAS,KAAK,SAAS,UAAU,IAAI;AAExC,QAAI,wBAAwB;AAE1B,aAAO,gBAAgB,IAAI,CAAC,MAAM,UAChC,UAAU,sBACN;QAAE,GAAG;QAAM,OAAO,UAAU;MAAK,IACjC,IAAA;;AAIN,aAAO;WACF;QACH;UAAE,MAAM,UAAU;UAAM,OAAO,UAAU;QAAK;;EAGpD;;;;;;;;;EAUO,MAAM,yBACX,YACA,QACA,QAHK;AAKL,UAAM,aAAsC,GAAA,kBAAAC,SAC1C,UAAW,MAAM,KAAK,UAAS,CAAA;AAGjC,UAAM,iBAAiB,MAAM,KAAK,4BAChC,YACA,SAAA;AAGF,QAAI,CAAC,gBAAgB;AACnB,OAAA,GAAA,0CAAO,MACL,wBAAwB,aAAa,uBAAA;AAEvC,aAAO;IACT;AAEA,UAAM,eAAe,MAAM,QAAQ,MAAA,IAAU,SAAS;MAAC;;AAEvD,eAAW,OAAO,cAAc;AAC9B,YAAM,eAAe,UAAU,KAC7B,CAAC,SAAS,KAAK,YAAY,UAAA;AAE7B,YAAM,iBAAiB,eACnB,KAAK,mBAAmB,aAAa,SAAS,GAAA,IAC9C;QAAC;UAAE,MAAM,IAAI;UAAM,OAAO,IAAI;QAAK;;AAEvC,UAAI,aACF,cAAa,UAAU;UAEvB,WAAU,KAAK;QAAE,SAAS;QAAY,SAAS;MAAc,CAAA;IAEjE;AAEA,WAAO;EACT;;;;;;;EAQO,MAAM,iBACX,eACA,QAFK;AAIL,QAAI,eAAwC,GAAA,kBAAAA,SAC1C,UAAW,MAAM,KAAK,UAAS,CAAA;AAGjC,eAAW,gBAAgB,cACzB,eACG,MAAM,KAAK,yBACV,aAAa,SACb,aAAa,SACb,WAAA,KACI;AAEV,WAAO;EACT;;;;;EAOO,MAAM,OACX,QADK;AAGL,WAAO,KAAK,mBAAmB,eAC7B,KAAI,GAAA,2CAAkB,MAAA,GACtB,KAAK,cAAc,KAAK,IAAI,CAAA;EAEhC;;;;;EAOO,MAAM,kBAAA;AACX,WAAO,KAAK,mBAAmB,UAAS,GAAA,0CAAY,gBAAe,CAAA;EACrE;;;;;;EASA,IAAW,kBAAX;AACE,WAAO,KAAK,WAAW;EACzB;;;;;;EASO,YAAY,SAAZ;AACL,SAAK,WAAW,YAAY,OAAA;EAC9B;;;;EAMO,gBAAA;AACL,SAAK,WAAW,YACd,KAAI,GAAA,2CAAY,GAAA,2CAAgB,gBAAgB,CAAA,CAAA,CAAA;EAEpD;EAEU,cAAc,IAAd;;AACR,KAAA,GAAA,0CAAO,MAAM,kBAAkB,EAAA;AAE/B,YAAQ,GAAG,MAAI;MACb,MAAK,GAAA,2CAAgB;AACnB,qBAAa,KAAK,iBAAiB;AACnC,mBAAK,kBAAL,8BAAqB,GAAG;AACxB,yBAAK,SAAS,cAAd,mBAAyB,eAAzB,4BAAsC,GAAG;AACzC;MACF,MAAK,GAAA,2CAAgB;AACnB,aAAK,mBAAmB,QAAQ,EAAA;AAChC,yBAAK,SAAS,cAAd,mBAAyB,qBAAzB,4BAA4C,GAAG;AAC/C;MAEF,MAAK,GAAA,2CAAgB,QAAQ;AAC3B,cAAM,OAAO,KAAK,mBAAmB,QAAQ,EAAA;AAC7C,yBAAK,SAAS,cAAd,mBAAyB,aAAzB,4BAAqC,KAAK,KAAoB;AAC9D;MACF;MACA,MAAK,GAAA,2CAAgB;AACnB,aAAK,mBAAmB,QAAQ,EAAA;AAChC,yBAAK,SAAS,cAAd,mBAAyB,uBAAzB,4BAA8C,GAAG;AACjD;MAEF,MAAK,GAAA,2CAAgB;AACnB,aAAK,mBAAmB,QAAQ,EAAA;AAChC;MAEF,MAAK,GAAA,2CAAgB,gBAAgB;AACnC,cAAM,OAAO,KAAK,mBAAmB,OAAO,EAAA;AAC5C,yBAAK,SAAS,cAAd,mBAAyB,mBAAzB,4BAA0C;AAC1C;MACF;MACA,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,YAAzB,4BAAmC;AACnC;MACF,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,0BAAzB;AACA;MACF,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,0BAAzB;AACA;MACF,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,yBAAzB;AACA;MACF,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,yBAAzB;AACA;MACF,MAAK,GAAA,2CAAgB,oBAAoB;AACvC,cAAM,iBAAiB,GAAG;AAC1B,yBAAK,SAAS,cAAd,mBAAyB,qBAAzB,4BAA4C;AAC5C;MACF;MACA,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,oBAAzB,4BAA2C,GAAG;AAC9C;MAEF,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,iBAAzB,4BAAwC,GAAG;AAC3C;MACF,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,oBAAzB;AACA;MACF,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,oBAAzB;AACA;MACF,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,iBAAzB,4BAAwC,GAAG;AAC3C;MACF,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,oBAAzB;AACA;MACF,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,oBAAzB;AACA;MACF,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,2BAAzB,4BACE,GAAG;AAEL,aAAK,MACH,GAAA,2CAAU,sBACV,GAAG,IAAgC;AAErC;MACF,MAAK,GAAA,2CAAgB;AACnB,aAAK,MAAK,GAAA,2CAAU,SAAS,GAAG,IAA0B;AAC1D,yBAAK,SAAS,cAAd,mBAAyB,cAAzB,4BAAqC,GAAG;AACxC;MACF,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,wBAAzB,4BACE,GAAG;AAEL;MACF,MAAK,GAAA,2CAAgB;AACnB,yBAAK,SAAS,cAAd,mBAAyB,oBAAzB,4BAA2C,GAAG;AAC9C,aAAK,MAAK,GAAA,2CAAU,eAAe,GAAG,IAAI;AAC1C;MAEF,SAAS;AACP,YAAI,QAAiB;AAErB,mBAAW,UAAU,OAAO,OAC1B,KAAK,QAAQ,EAEb,KAAI,OAAO,gBAAe,EAAG,SAAS,GAAG,IAAI,GAAG;AAC9C,kBAAQ;AACR,iBAAO,cAAc,EAAA;QACvB;AAEF,YAAI,CAAC,MACH,kBAAK,SAAS,cAAd,mBAAyB,qBAAzB,4BAA4C,GAAG;MAEnD;IACF;EACF;;;;;;;;;;EAYO,eACL,SACA,QAFK;AAIL,QAAI,KAAK,SAAS,OAAA,EAChB,OAAM,IAAI,MAAM,qBAAqB,OAAA,sBAA6B;AAIpE,QAAI,EAAE,mBAAkB,GAAA,4CACtB,OAAM,IAAI,MAAM,gDAAgD;AAGlE,WAAO,UAAU;AACjB,WAAO,SAAS;AAEhB,SAAK,SAAS,OAAA,IAAW;AAEzB,WAAO,KAAK,SAAS,OAAA;EACvB;EAEO,UAAsC,SAAtC;AACL,UAAM,SAAS,KAAK,SAAS,OAAA;AAC7B,QAAI,CAAC,QAAQ;AACX,OAAA,GAAA,0CAAO,MAAM,6BAA6B,OAAA,aAAoB;AAC9D,aAAO;IACT;AACA,WAAO;EACT;EAEO,iBAAiB,SAAjB;AACL,QAAI,CAAC,KAAK,SAAS,OAAA,EACjB;AAEF,WAAO,KAAK,SAAS,OAAA;EACvB;AACD;AAnac,iCAAA;GADZ,GAAA;GAMA,0CAAA,WAAA,aAAA,IAAA;AASY,iCAAA;GADZ,GAAA;GAWA,0CAAA,WAAA,gBAAA,IAAA;AAOY,iCAAA;GADZ,GAAA;GAGA,0CAAA,WAAA,kBAAA,IAAA;AA4LY,iCAAA;GADZ,GAAA;GAGA,0CAAA,WAAA,mBAAA,IAAA;AASD,iCAAA;GADC,GAAA,2CAAsB,aAAa,OAAA;GAGnC,0CAAA,WAAA,mBAAA,IAAA;AASM,iCAAA;GADN,GAAA;GAGA,0CAAA,WAAA,eAAA,IAAA;AAMM,iCAAA;GADN,GAAA;GAKA,0CAAA,WAAA,iBAAA,IAAA;;;;;AQ75BH,IAAY;CAAZ,SAAY,gBAAc;AACxB,iBAAA,mBAAA,IAAA;AACA,iBAAA,yBAAA,IAAA;AACA,iBAAA,0BAAA,IAAA;AACA,iBAAA,qBAAA,IAAA;AACF,GALY,8CAAA,4CAAc,CAAA,EAAA;AAO1B,IAAY;CAAZ,SAAY,eAAa;AACvB,gBAAA,oBAAA,IAAA;AACA,gBAAA,aAAA,IAAA;AACA,gBAAA,aAAA,IAAA;AACA,gBAAA,KAAA,IAAA;AACF,GALY,6CAAA,2CAAa,CAAA,EAAA;AAoBnB,IAAO,4CAAP,eAAyB,GAAA,2CAAA;EAI7B,YAAY,SAAZ;AACE,UAAM,OAAA;AAEN,SAAK,wBAAwB;EAC/B;EAEO,kBAAA;AACL,WAAO,OAAO,OAAO,yCAAA;EACvB;;;;;;EAQO,MAAM,aAAN;AACL,QAAI,KAAK,QAAQ,UAAU,QACzB,OAAM,IAAI,0CACR,sDAAA;AAGJ,UAAM,oBAAwC,MAAM,KAAK,QAAQ,OAAO;MACtE,SAAS,KAAK;MACd,QAAQ,yCAAc;IACE,CAAA;AAC1B,WAAO,kBAAkB,KAAK;EAChC;;;;;;;;EAUO,MAAM,WACX,SACA,YAAqB,OAFhB;AAIL,QAAI,KAAK,QAAQ,UAAU,QACzB,OAAM,IAAI,0CACR,sDAAA;AAIJ,UAAM,iBAAsC,MAAM,KAAK,QAAQ,OAAO;MACpE,SAAS,KAAK;MACd,QAAQ,yCAAc;MACtB,WAAW;QACT;UACE,MAAM;UACN,OAAO,QAAQ;QAChB;QACD;UACE,MAAM;UACN,OAAO;QACR;;IAEqB,CAAA;AAE1B,WAAO,CAAC,CAAC,eAAe,KAAK;EAC/B;;;;;;;;EAUO,MAAM,iBACX,SACA,iBAA0B,OAFrB;AAIL,QAAI,KAAK,QAAQ,UAAU,QACzB,OAAM,IAAI,0CACR,sDAAA;AAIJ,UAAM,iBAAkB,MAAM,KAAK,QAAQ,OAAO;MAChD,SAAS,KAAK;MACd,QAAQ,yCAAc;MACtB,WAAW;QACT;UACE,MAAM;UACN,OAAO;YAAC;;QACT;QACD;UACE,MAAM;UACN,OAAO;QACR;;IAEqB,CAAA;AAC1B,WAAO,CAAC,CAAC,eAAe,KAAK;EAC/B;;;;;;;;EASO,MAAM,IAAI,YAAqB,OAA/B;AACL,QAAI,KAAK,QAAQ,UAAU,QACzB;AAGF,WAAO,KAAK,QAAQ,OAAO;MACzB,SAAS,KAAK;MACd,QAAQ,yCAAc;MACtB,WAAW;QACT;UACE,MAAM;UACN,OAAO;QACR;;IAEqB,CAAA;EAC5B;;;;;;;;EAUO,mBAAmB,UAAnB;AACL,SAAK,wBAAwB;EAC/B;EAEO,cAAc,IAAd;;AACL,YAAQ,GAAG,MAAI;MACb,KAAK,0CAAe;AAClB,yBAAK,SAAS,cAAd,mBAAyB,wBAAzB,4BAA+C,GAAG;AAClD,aAAK,QAAQ,MAAK,GAAA,2CAAU,mBAAmB,GAAG,IAAc;AAChE;MACF,KAAK,0CAAe,mBAAmB;AACrC,cAAM,IAAI,GAAG;AACb,yBAAK,SAAS,cAAd,mBAAyB,sBAAzB,4BACE,GAAG;AAEL,aAAK,QAAQ,MACX,GAAA,2CAAU,iBACV,GAAG,IAA2B;AAEhC,YAAI,KAAK,uBAAuB;AAC9B,gBAAM,KAAK;YACT,cAAc,EAAE;YAChB,WAAW,EAAE;UACd;AACD,cAAI,KAAK,QAAQ,UAAU,QACzB,MAAK,sBAAsB,EAAA,EAAI,KAAK,CAAC,WAAA;AACnC,iBAAK,QAAQ,YACX,KAAI,GAAA,0CAAY,0CAAe,0BAA0B;cACvD,eAAe,EAAE;cACjB,cAAc,EAAE;cAChB,WAAW,EAAE;;YAEd,CAAA,CAAA;UAEL,CAAA;cAEA,OAAM,IAAI,0CACR,sFAAA;QAGN;AACA;MACF;MACA,KAAK,0CAAe,yBAAyB;AAC3C,cAAM,IAAI,GAAG;AACb,yBAAK,SAAS,cAAd,mBAAyB,2BAAzB,4BACE,EAAE;AAEJ,aAAK,QAAQ,MAAK,GAAA,2CAAU,sBAAsB,EAAE,aAAa;AACjE;MACF;IACF;EACF;AACD;;;ACzNK,IAAgB,4CAAhB,MAAgB;EAOpB,cAAA;AAHU,SAAA,SAAyB;AACzB,SAAA,UAAmB;EAEd;EAwCf,IAAI,SAAJ;AACE,WAAO,KAAK;EACd;AAGD;", "names": ["ReflectApply", "ReflectOwnKeys", "NumberIsNaN", "once", "$eINDk$events", "_a", "config", "$eINDk$clonedeep"]}