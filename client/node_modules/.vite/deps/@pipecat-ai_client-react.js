import {
  require_jsx_runtime
} from "./chunk-WLVB5OIP.js";
import {
  require_react
} from "./chunk-CANBAPAS.js";
import {
  $f9fc0c57b9aaed9c$export$6b4624d233c61fcb
} from "./chunk-JAMCOKTQ.js";
import {
  __toESM
} from "./chunk-5WRI5ZAA.js";

// node_modules/@pipecat-ai/client-react/dist/index.module.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_react4 = __toESM(require_react());

// node_modules/jotai/esm/vanilla/internals.mjs
var isSelfAtom = (atom2, a) => atom2.unstable_is ? atom2.unstable_is(a) : a === atom2;
var hasInitialValue = (atom2) => "init" in atom2;
var isActuallyWritableAtom = (atom2) => !!atom2.write;
var isAtomStateInitialized = (atomState) => "v" in atomState || "e" in atomState;
var returnAtomValue = (atomState) => {
  if ("e" in atomState) {
    throw atomState.e;
  }
  if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production" && !("v" in atomState)) {
    throw new Error("[Bug] atom state is not initialized");
  }
  return atomState.v;
};
var promiseStateMap = /* @__PURE__ */ new WeakMap();
var isPendingPromise = (value) => {
  var _a;
  return isPromiseLike(value) && !!((_a = promiseStateMap.get(value)) == null ? void 0 : _a[0]);
};
var abortPromise = (promise) => {
  const promiseState = promiseStateMap.get(promise);
  if (promiseState == null ? void 0 : promiseState[0]) {
    promiseState[0] = false;
    promiseState[1].forEach((fn) => fn());
  }
};
var registerAbortHandler = (promise, abortHandler) => {
  let promiseState = promiseStateMap.get(promise);
  if (!promiseState) {
    promiseState = [true, /* @__PURE__ */ new Set()];
    promiseStateMap.set(promise, promiseState);
    const settle = () => {
      promiseState[0] = false;
    };
    promise.then(settle, settle);
  }
  promiseState[1].add(abortHandler);
};
var isPromiseLike = (p) => typeof (p == null ? void 0 : p.then) === "function";
var addPendingPromiseToDependency = (atom2, promise, dependencyAtomState) => {
  if (!dependencyAtomState.p.has(atom2)) {
    dependencyAtomState.p.add(atom2);
    promise.then(
      () => {
        dependencyAtomState.p.delete(atom2);
      },
      () => {
        dependencyAtomState.p.delete(atom2);
      }
    );
  }
};
var setAtomStateValueOrPromise = (atom2, valueOrPromise, ensureAtomState) => {
  const atomState = ensureAtomState(atom2);
  const hasPrevValue = "v" in atomState;
  const prevValue = atomState.v;
  if (isPromiseLike(valueOrPromise)) {
    for (const a of atomState.d.keys()) {
      addPendingPromiseToDependency(atom2, valueOrPromise, ensureAtomState(a));
    }
  }
  atomState.v = valueOrPromise;
  delete atomState.e;
  if (!hasPrevValue || !Object.is(prevValue, atomState.v)) {
    ++atomState.n;
    if (isPromiseLike(prevValue)) {
      abortPromise(prevValue);
    }
  }
};
var getMountedOrPendingDependents = (atom2, atomState, mountedMap) => {
  var _a;
  const dependents = /* @__PURE__ */ new Set();
  for (const a of ((_a = mountedMap.get(atom2)) == null ? void 0 : _a.t) || []) {
    if (mountedMap.has(a)) {
      dependents.add(a);
    }
  }
  for (const atomWithPendingPromise of atomState.p) {
    dependents.add(atomWithPendingPromise);
  }
  return dependents;
};
var createStoreHook = () => {
  const callbacks = /* @__PURE__ */ new Set();
  const notify = () => {
    callbacks.forEach((fn) => fn());
  };
  notify.add = (fn) => {
    callbacks.add(fn);
    return () => {
      callbacks.delete(fn);
    };
  };
  return notify;
};
var createStoreHookForAtoms = () => {
  const all = {};
  const callbacks = /* @__PURE__ */ new WeakMap();
  const notify = (atom2) => {
    var _a, _b;
    (_a = callbacks.get(all)) == null ? void 0 : _a.forEach((fn) => fn(atom2));
    (_b = callbacks.get(atom2)) == null ? void 0 : _b.forEach((fn) => fn());
  };
  notify.add = (atom2, fn) => {
    const key = atom2 || all;
    const fns = (callbacks.has(key) ? callbacks : callbacks.set(key, /* @__PURE__ */ new Set())).get(key);
    fns.add(fn);
    return () => {
      fns == null ? void 0 : fns.delete(fn);
      if (!fns.size) {
        callbacks.delete(key);
      }
    };
  };
  return notify;
};
var initializeStoreHooks = (storeHooks) => {
  storeHooks.c || (storeHooks.c = createStoreHookForAtoms());
  storeHooks.m || (storeHooks.m = createStoreHookForAtoms());
  storeHooks.u || (storeHooks.u = createStoreHookForAtoms());
  storeHooks.f || (storeHooks.f = createStoreHook());
  return storeHooks;
};
var BUILDING_BLOCKS = Symbol();
var buildStore = (atomStateMap = /* @__PURE__ */ new WeakMap(), mountedMap = /* @__PURE__ */ new WeakMap(), invalidatedAtoms = /* @__PURE__ */ new WeakMap(), changedAtoms = /* @__PURE__ */ new Set(), mountCallbacks = /* @__PURE__ */ new Set(), unmountCallbacks = /* @__PURE__ */ new Set(), storeHooks = {}, atomRead = (atom2, ...params) => atom2.read(...params), atomWrite = (atom2, ...params) => atom2.write(...params), atomOnInit = (atom2, store) => {
  var _a;
  return (_a = atom2.unstable_onInit) == null ? void 0 : _a.call(atom2, store);
}, atomOnMount = (atom2, setAtom) => {
  var _a;
  return (_a = atom2.onMount) == null ? void 0 : _a.call(atom2, setAtom);
}, ...buildingBlockFunctions) => {
  const ensureAtomState = buildingBlockFunctions[0] || ((atom2) => {
    if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production" && !atom2) {
      throw new Error("Atom is undefined or null");
    }
    let atomState = atomStateMap.get(atom2);
    if (!atomState) {
      atomState = { d: /* @__PURE__ */ new Map(), p: /* @__PURE__ */ new Set(), n: 0 };
      atomStateMap.set(atom2, atomState);
      atomOnInit == null ? void 0 : atomOnInit(atom2, store);
    }
    return atomState;
  });
  const flushCallbacks = buildingBlockFunctions[1] || (() => {
    const errors = [];
    const call = (fn) => {
      try {
        fn();
      } catch (e) {
        errors.push(e);
      }
    };
    do {
      if (storeHooks.f) {
        call(storeHooks.f);
      }
      const callbacks = /* @__PURE__ */ new Set();
      const add = callbacks.add.bind(callbacks);
      changedAtoms.forEach((atom2) => {
        var _a;
        return (_a = mountedMap.get(atom2)) == null ? void 0 : _a.l.forEach(add);
      });
      changedAtoms.clear();
      unmountCallbacks.forEach(add);
      unmountCallbacks.clear();
      mountCallbacks.forEach(add);
      mountCallbacks.clear();
      callbacks.forEach(call);
      if (changedAtoms.size) {
        recomputeInvalidatedAtoms();
      }
    } while (changedAtoms.size || unmountCallbacks.size || mountCallbacks.size);
    if (errors.length) {
      throw new AggregateError(errors);
    }
  });
  const recomputeInvalidatedAtoms = buildingBlockFunctions[2] || (() => {
    const topSortedReversed = [];
    const visiting = /* @__PURE__ */ new WeakSet();
    const visited = /* @__PURE__ */ new WeakSet();
    const stack = Array.from(changedAtoms);
    while (stack.length) {
      const a = stack[stack.length - 1];
      const aState = ensureAtomState(a);
      if (visited.has(a)) {
        stack.pop();
        continue;
      }
      if (visiting.has(a)) {
        if (invalidatedAtoms.get(a) === aState.n) {
          topSortedReversed.push([a, aState]);
        } else if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production" && invalidatedAtoms.has(a)) {
          throw new Error("[Bug] invalidated atom exists");
        }
        visited.add(a);
        stack.pop();
        continue;
      }
      visiting.add(a);
      for (const d of getMountedOrPendingDependents(a, aState, mountedMap)) {
        if (!visiting.has(d)) {
          stack.push(d);
        }
      }
    }
    for (let i = topSortedReversed.length - 1; i >= 0; --i) {
      const [a, aState] = topSortedReversed[i];
      let hasChangedDeps = false;
      for (const dep of aState.d.keys()) {
        if (dep !== a && changedAtoms.has(dep)) {
          hasChangedDeps = true;
          break;
        }
      }
      if (hasChangedDeps) {
        readAtomState(a);
        mountDependencies(a);
      }
      invalidatedAtoms.delete(a);
    }
  });
  const readAtomState = buildingBlockFunctions[3] || ((atom2) => {
    var _a;
    const atomState = ensureAtomState(atom2);
    if (isAtomStateInitialized(atomState)) {
      if (mountedMap.has(atom2) && invalidatedAtoms.get(atom2) !== atomState.n) {
        return atomState;
      }
      if (Array.from(atomState.d).every(
        ([a, n]) => (
          // Recursively, read the atom state of the dependency, and
          // check if the atom epoch number is unchanged
          readAtomState(a).n === n
        )
      )) {
        return atomState;
      }
    }
    atomState.d.clear();
    let isSync = true;
    const mountDependenciesIfAsync = () => {
      if (mountedMap.has(atom2)) {
        mountDependencies(atom2);
        recomputeInvalidatedAtoms();
        flushCallbacks();
      }
    };
    const getter = (a) => {
      var _a2;
      if (isSelfAtom(atom2, a)) {
        const aState2 = ensureAtomState(a);
        if (!isAtomStateInitialized(aState2)) {
          if (hasInitialValue(a)) {
            setAtomStateValueOrPromise(a, a.init, ensureAtomState);
          } else {
            throw new Error("no atom init");
          }
        }
        return returnAtomValue(aState2);
      }
      const aState = readAtomState(a);
      try {
        return returnAtomValue(aState);
      } finally {
        atomState.d.set(a, aState.n);
        if (isPendingPromise(atomState.v)) {
          addPendingPromiseToDependency(atom2, atomState.v, aState);
        }
        (_a2 = mountedMap.get(a)) == null ? void 0 : _a2.t.add(atom2);
        if (!isSync) {
          mountDependenciesIfAsync();
        }
      }
    };
    let controller;
    let setSelf;
    const options = {
      get signal() {
        if (!controller) {
          controller = new AbortController();
        }
        return controller.signal;
      },
      get setSelf() {
        if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production" && !isActuallyWritableAtom(atom2)) {
          console.warn("setSelf function cannot be used with read-only atom");
        }
        if (!setSelf && isActuallyWritableAtom(atom2)) {
          setSelf = (...args) => {
            if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production" && isSync) {
              console.warn("setSelf function cannot be called in sync");
            }
            if (!isSync) {
              try {
                return writeAtomState(atom2, ...args);
              } finally {
                recomputeInvalidatedAtoms();
                flushCallbacks();
              }
            }
          };
        }
        return setSelf;
      }
    };
    const prevEpochNumber = atomState.n;
    try {
      const valueOrPromise = atomRead(atom2, getter, options);
      setAtomStateValueOrPromise(atom2, valueOrPromise, ensureAtomState);
      if (isPromiseLike(valueOrPromise)) {
        registerAbortHandler(valueOrPromise, () => controller == null ? void 0 : controller.abort());
        valueOrPromise.then(
          mountDependenciesIfAsync,
          mountDependenciesIfAsync
        );
      }
      return atomState;
    } catch (error) {
      delete atomState.v;
      atomState.e = error;
      ++atomState.n;
      return atomState;
    } finally {
      isSync = false;
      if (prevEpochNumber !== atomState.n && invalidatedAtoms.get(atom2) === prevEpochNumber) {
        invalidatedAtoms.set(atom2, atomState.n);
        changedAtoms.add(atom2);
        (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, atom2);
      }
    }
  });
  const invalidateDependents = buildingBlockFunctions[4] || ((atom2) => {
    const stack = [atom2];
    while (stack.length) {
      const a = stack.pop();
      const aState = ensureAtomState(a);
      for (const d of getMountedOrPendingDependents(a, aState, mountedMap)) {
        const dState = ensureAtomState(d);
        invalidatedAtoms.set(d, dState.n);
        stack.push(d);
      }
    }
  });
  const writeAtomState = buildingBlockFunctions[5] || ((atom2, ...args) => {
    let isSync = true;
    const getter = (a) => returnAtomValue(readAtomState(a));
    const setter = (a, ...args2) => {
      var _a;
      const aState = ensureAtomState(a);
      try {
        if (isSelfAtom(atom2, a)) {
          if (!hasInitialValue(a)) {
            throw new Error("atom not writable");
          }
          const prevEpochNumber = aState.n;
          const v = args2[0];
          setAtomStateValueOrPromise(a, v, ensureAtomState);
          mountDependencies(a);
          if (prevEpochNumber !== aState.n) {
            changedAtoms.add(a);
            (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, a);
            invalidateDependents(a);
          }
          return void 0;
        } else {
          return writeAtomState(a, ...args2);
        }
      } finally {
        if (!isSync) {
          recomputeInvalidatedAtoms();
          flushCallbacks();
        }
      }
    };
    try {
      return atomWrite(atom2, getter, setter, ...args);
    } finally {
      isSync = false;
    }
  });
  const mountDependencies = buildingBlockFunctions[6] || ((atom2) => {
    var _a;
    const atomState = ensureAtomState(atom2);
    const mounted = mountedMap.get(atom2);
    if (mounted && !isPendingPromise(atomState.v)) {
      for (const [a, n] of atomState.d) {
        if (!mounted.d.has(a)) {
          const aState = ensureAtomState(a);
          const aMounted = mountAtom(a);
          aMounted.t.add(atom2);
          mounted.d.add(a);
          if (n !== aState.n) {
            changedAtoms.add(a);
            (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, a);
            invalidateDependents(a);
          }
        }
      }
      for (const a of mounted.d || []) {
        if (!atomState.d.has(a)) {
          mounted.d.delete(a);
          const aMounted = unmountAtom(a);
          aMounted == null ? void 0 : aMounted.t.delete(atom2);
        }
      }
    }
  });
  const mountAtom = buildingBlockFunctions[7] || ((atom2) => {
    var _a;
    const atomState = ensureAtomState(atom2);
    let mounted = mountedMap.get(atom2);
    if (!mounted) {
      readAtomState(atom2);
      for (const a of atomState.d.keys()) {
        const aMounted = mountAtom(a);
        aMounted.t.add(atom2);
      }
      mounted = {
        l: /* @__PURE__ */ new Set(),
        d: new Set(atomState.d.keys()),
        t: /* @__PURE__ */ new Set()
      };
      mountedMap.set(atom2, mounted);
      (_a = storeHooks.m) == null ? void 0 : _a.call(storeHooks, atom2);
      if (isActuallyWritableAtom(atom2)) {
        const processOnMount = () => {
          let isSync = true;
          const setAtom = (...args) => {
            try {
              return writeAtomState(atom2, ...args);
            } finally {
              if (!isSync) {
                recomputeInvalidatedAtoms();
                flushCallbacks();
              }
            }
          };
          try {
            const onUnmount = atomOnMount(atom2, setAtom);
            if (onUnmount) {
              mounted.u = () => {
                isSync = true;
                try {
                  onUnmount();
                } finally {
                  isSync = false;
                }
              };
            }
          } finally {
            isSync = false;
          }
        };
        mountCallbacks.add(processOnMount);
      }
    }
    return mounted;
  });
  const unmountAtom = buildingBlockFunctions[8] || ((atom2) => {
    var _a;
    const atomState = ensureAtomState(atom2);
    let mounted = mountedMap.get(atom2);
    if (mounted && !mounted.l.size && !Array.from(mounted.t).some((a) => {
      var _a2;
      return (_a2 = mountedMap.get(a)) == null ? void 0 : _a2.d.has(atom2);
    })) {
      if (mounted.u) {
        unmountCallbacks.add(mounted.u);
      }
      mounted = void 0;
      mountedMap.delete(atom2);
      (_a = storeHooks.u) == null ? void 0 : _a.call(storeHooks, atom2);
      for (const a of atomState.d.keys()) {
        const aMounted = unmountAtom(a);
        aMounted == null ? void 0 : aMounted.t.delete(atom2);
      }
      return void 0;
    }
    return mounted;
  });
  const buildingBlocks = [
    // store state
    atomStateMap,
    mountedMap,
    invalidatedAtoms,
    changedAtoms,
    mountCallbacks,
    unmountCallbacks,
    storeHooks,
    // atom interceptors
    atomRead,
    atomWrite,
    atomOnInit,
    atomOnMount,
    // building-block functions
    ensureAtomState,
    flushCallbacks,
    recomputeInvalidatedAtoms,
    readAtomState,
    invalidateDependents,
    writeAtomState,
    mountDependencies,
    mountAtom,
    unmountAtom
  ];
  const store = {
    get: (atom2) => returnAtomValue(readAtomState(atom2)),
    set: (atom2, ...args) => {
      try {
        return writeAtomState(atom2, ...args);
      } finally {
        recomputeInvalidatedAtoms();
        flushCallbacks();
      }
    },
    sub: (atom2, listener) => {
      const mounted = mountAtom(atom2);
      const listeners = mounted.l;
      listeners.add(listener);
      flushCallbacks();
      return () => {
        listeners.delete(listener);
        unmountAtom(atom2);
        flushCallbacks();
      };
    }
  };
  Object.defineProperty(store, BUILDING_BLOCKS, { value: buildingBlocks });
  return store;
};
var INTERNAL_buildStoreRev1 = buildStore;
var INTERNAL_initializeStoreHooks = initializeStoreHooks;
var INTERNAL_registerAbortHandler = registerAbortHandler;

// node_modules/jotai/esm/vanilla.mjs
var keyCount = 0;
function atom(read, write) {
  const key = `atom${++keyCount}`;
  const config = {
    toString() {
      return (import.meta.env ? import.meta.env.MODE : void 0) !== "production" && this.debugLabel ? key + ":" + this.debugLabel : key;
    }
  };
  if (typeof read === "function") {
    config.read = read;
  } else {
    config.init = read;
    config.read = defaultRead;
    config.write = defaultWrite;
  }
  if (write) {
    config.write = write;
  }
  return config;
}
function defaultRead(get) {
  return get(this);
}
function defaultWrite(get, set, arg) {
  return set(
    this,
    typeof arg === "function" ? arg(get(this)) : arg
  );
}
var createDevStoreRev4 = () => {
  let inRestoreAtom = 0;
  const storeHooks = INTERNAL_initializeStoreHooks({});
  const atomStateMap = /* @__PURE__ */ new WeakMap();
  const mountedAtoms = /* @__PURE__ */ new WeakMap();
  const store = INTERNAL_buildStoreRev1(
    atomStateMap,
    mountedAtoms,
    void 0,
    void 0,
    void 0,
    void 0,
    storeHooks,
    void 0,
    (atom2, get, set, ...args) => {
      if (inRestoreAtom) {
        return set(atom2, ...args);
      }
      return atom2.write(get, set, ...args);
    }
  );
  const debugMountedAtoms = /* @__PURE__ */ new Set();
  storeHooks.m.add(void 0, (atom2) => {
    debugMountedAtoms.add(atom2);
    const atomState = atomStateMap.get(atom2);
    atomState.m = mountedAtoms.get(atom2);
  });
  storeHooks.u.add(void 0, (atom2) => {
    debugMountedAtoms.delete(atom2);
    const atomState = atomStateMap.get(atom2);
    delete atomState.m;
  });
  const devStore = {
    // store dev methods (these are tentative and subject to change without notice)
    dev4_get_internal_weak_map: () => {
      console.log("Deprecated: Use devstore from the devtools library");
      return atomStateMap;
    },
    dev4_get_mounted_atoms: () => debugMountedAtoms,
    dev4_restore_atoms: (values) => {
      const restoreAtom = {
        read: () => null,
        write: (_get, set) => {
          ++inRestoreAtom;
          try {
            for (const [atom2, value] of values) {
              if ("init" in atom2) {
                set(atom2, value);
              }
            }
          } finally {
            --inRestoreAtom;
          }
        }
      };
      store.set(restoreAtom);
    }
  };
  return Object.assign(store, devStore);
};
var overiddenCreateStore;
function createStore() {
  if (overiddenCreateStore) {
    return overiddenCreateStore();
  }
  if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production") {
    return createDevStoreRev4();
  }
  return INTERNAL_buildStoreRev1();
}
var defaultStore;
function getDefaultStore() {
  if (!defaultStore) {
    defaultStore = createStore();
    if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production") {
      globalThis.__JOTAI_DEFAULT_STORE__ || (globalThis.__JOTAI_DEFAULT_STORE__ = defaultStore);
      if (globalThis.__JOTAI_DEFAULT_STORE__ !== defaultStore) {
        console.warn(
          "Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044"
        );
      }
    }
  }
  return defaultStore;
}

// node_modules/jotai/esm/react.mjs
var import_react = __toESM(require_react(), 1);
var StoreContext = (0, import_react.createContext)(
  void 0
);
function useStore(options) {
  const store = (0, import_react.useContext)(StoreContext);
  return (options == null ? void 0 : options.store) || store || getDefaultStore();
}
function Provider({
  children,
  store
}) {
  const storeRef = (0, import_react.useRef)(void 0);
  if (!store && !storeRef.current) {
    storeRef.current = createStore();
  }
  return (0, import_react.createElement)(
    StoreContext.Provider,
    {
      value: store || storeRef.current
    },
    children
  );
}
var isPromiseLike2 = (x) => typeof (x == null ? void 0 : x.then) === "function";
var attachPromiseStatus = (promise) => {
  if (!promise.status) {
    promise.status = "pending";
    promise.then(
      (v) => {
        promise.status = "fulfilled";
        promise.value = v;
      },
      (e) => {
        promise.status = "rejected";
        promise.reason = e;
      }
    );
  }
};
var use = import_react.default.use || // A shim for older React versions
((promise) => {
  if (promise.status === "pending") {
    throw promise;
  } else if (promise.status === "fulfilled") {
    return promise.value;
  } else if (promise.status === "rejected") {
    throw promise.reason;
  } else {
    attachPromiseStatus(promise);
    throw promise;
  }
});
var continuablePromiseMap = /* @__PURE__ */ new WeakMap();
var createContinuablePromise = (promise, getValue) => {
  let continuablePromise = continuablePromiseMap.get(promise);
  if (!continuablePromise) {
    continuablePromise = new Promise((resolve, reject) => {
      let curr = promise;
      const onFulfilled = (me) => (v) => {
        if (curr === me) {
          resolve(v);
        }
      };
      const onRejected = (me) => (e) => {
        if (curr === me) {
          reject(e);
        }
      };
      const onAbort = () => {
        try {
          const nextValue = getValue();
          if (isPromiseLike2(nextValue)) {
            continuablePromiseMap.set(nextValue, continuablePromise);
            curr = nextValue;
            nextValue.then(onFulfilled(nextValue), onRejected(nextValue));
            INTERNAL_registerAbortHandler(nextValue, onAbort);
          } else {
            resolve(nextValue);
          }
        } catch (e) {
          reject(e);
        }
      };
      promise.then(onFulfilled(promise), onRejected(promise));
      INTERNAL_registerAbortHandler(promise, onAbort);
    });
    continuablePromiseMap.set(promise, continuablePromise);
  }
  return continuablePromise;
};
function useAtomValue(atom2, options) {
  const { delay, unstable_promiseStatus: promiseStatus = !import_react.default.use } = options || {};
  const store = useStore(options);
  const [[valueFromReducer, storeFromReducer, atomFromReducer], rerender] = (0, import_react.useReducer)(
    (prev) => {
      const nextValue = store.get(atom2);
      if (Object.is(prev[0], nextValue) && prev[1] === store && prev[2] === atom2) {
        return prev;
      }
      return [nextValue, store, atom2];
    },
    void 0,
    () => [store.get(atom2), store, atom2]
  );
  let value = valueFromReducer;
  if (storeFromReducer !== store || atomFromReducer !== atom2) {
    rerender();
    value = store.get(atom2);
  }
  (0, import_react.useEffect)(() => {
    const unsub = store.sub(atom2, () => {
      if (promiseStatus) {
        try {
          const value2 = store.get(atom2);
          if (isPromiseLike2(value2)) {
            attachPromiseStatus(
              createContinuablePromise(value2, () => store.get(atom2))
            );
          }
        } catch (e) {
        }
      }
      if (typeof delay === "number") {
        setTimeout(rerender, delay);
        return;
      }
      rerender();
    });
    rerender();
    return unsub;
  }, [store, atom2, delay, promiseStatus]);
  (0, import_react.useDebugValue)(value);
  if (isPromiseLike2(value)) {
    const promise = createContinuablePromise(value, () => store.get(atom2));
    if (promiseStatus) {
      attachPromiseStatus(promise);
    }
    return use(promise);
  }
  return value;
}
function useSetAtom(atom2, options) {
  const store = useStore(options);
  const setAtom = (0, import_react.useCallback)(
    (...args) => {
      if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production" && !("write" in atom2)) {
        throw new Error("not writable atom");
      }
      return store.set(atom2, ...args);
    },
    [store, atom2]
  );
  return setAtom;
}
function useAtom(atom2, options) {
  return [
    useAtomValue(atom2, options),
    // We do wrong type assertion here, which results in throwing an error.
    useSetAtom(atom2, options)
  ];
}

// node_modules/jotai/esm/vanilla/utils.mjs
var RESET = Symbol(
  (import.meta.env ? import.meta.env.MODE : void 0) !== "production" ? "RESET" : ""
);
function atomFamily(initializeAtom, areEqual) {
  let shouldRemove = null;
  const atoms = /* @__PURE__ */ new Map();
  const listeners = /* @__PURE__ */ new Set();
  const createAtom = (param) => {
    let item;
    if (areEqual === void 0) {
      item = atoms.get(param);
    } else {
      for (const [key, value] of atoms) {
        if (areEqual(key, param)) {
          item = value;
          break;
        }
      }
    }
    if (item !== void 0) {
      if (shouldRemove == null ? void 0 : shouldRemove(item[1], param)) {
        createAtom.remove(param);
      } else {
        return item[0];
      }
    }
    const newAtom = initializeAtom(param);
    atoms.set(param, [newAtom, Date.now()]);
    notifyListeners("CREATE", param, newAtom);
    return newAtom;
  };
  const notifyListeners = (type, param, atom2) => {
    for (const listener of listeners) {
      listener({ type, param, atom: atom2 });
    }
  };
  createAtom.unstable_listen = (callback) => {
    listeners.add(callback);
    return () => {
      listeners.delete(callback);
    };
  };
  createAtom.getParams = () => atoms.keys();
  createAtom.remove = (param) => {
    if (areEqual === void 0) {
      if (!atoms.has(param)) return;
      const [atom2] = atoms.get(param);
      atoms.delete(param);
      notifyListeners("REMOVE", param, atom2);
    } else {
      for (const [key, [atom2]] of atoms) {
        if (areEqual(key, param)) {
          atoms.delete(key);
          notifyListeners("REMOVE", key, atom2);
          break;
        }
      }
    }
  };
  createAtom.setShouldRemove = (fn) => {
    shouldRemove = fn;
    if (!shouldRemove) return;
    for (const [key, [atom2, createdAt]] of atoms) {
      if (shouldRemove(createdAt, key)) {
        atoms.delete(key);
        notifyListeners("REMOVE", key, atom2);
      }
    }
  };
  return createAtom;
}
var isPromiseLike$3 = (x) => typeof (x == null ? void 0 : x.then) === "function";
function createJSONStorage(getStringStorage = () => {
  try {
    return window.localStorage;
  } catch (e) {
    if ((import.meta.env ? import.meta.env.MODE : void 0) !== "production") {
      if (typeof window !== "undefined") {
        console.warn(e);
      }
    }
    return void 0;
  }
}, options) {
  var _a;
  let lastStr;
  let lastValue;
  const storage = {
    getItem: (key, initialValue) => {
      var _a2, _b;
      const parse = (str2) => {
        str2 = str2 || "";
        if (lastStr !== str2) {
          try {
            lastValue = JSON.parse(str2, options == null ? void 0 : options.reviver);
          } catch (e) {
            return initialValue;
          }
          lastStr = str2;
        }
        return lastValue;
      };
      const str = (_b = (_a2 = getStringStorage()) == null ? void 0 : _a2.getItem(key)) != null ? _b : null;
      if (isPromiseLike$3(str)) {
        return str.then(parse);
      }
      return parse(str);
    },
    setItem: (key, newValue) => {
      var _a2;
      return (_a2 = getStringStorage()) == null ? void 0 : _a2.setItem(
        key,
        JSON.stringify(newValue, options == null ? void 0 : options.replacer)
      );
    },
    removeItem: (key) => {
      var _a2;
      return (_a2 = getStringStorage()) == null ? void 0 : _a2.removeItem(key);
    }
  };
  const createHandleSubscribe = (subscriber2) => (key, callback, initialValue) => subscriber2(key, (v) => {
    let newValue;
    try {
      newValue = JSON.parse(v || "");
    } catch (e) {
      newValue = initialValue;
    }
    callback(newValue);
  });
  let subscriber;
  try {
    subscriber = (_a = getStringStorage()) == null ? void 0 : _a.subscribe;
  } catch (e) {
  }
  if (!subscriber && typeof window !== "undefined" && typeof window.addEventListener === "function" && window.Storage) {
    subscriber = (key, callback) => {
      if (!(getStringStorage() instanceof window.Storage)) {
        return () => {
        };
      }
      const storageEventCallback = (e) => {
        if (e.storageArea === getStringStorage() && e.key === key) {
          callback(e.newValue);
        }
      };
      window.addEventListener("storage", storageEventCallback);
      return () => {
        window.removeEventListener("storage", storageEventCallback);
      };
    };
  }
  if (subscriber) {
    storage.subscribe = createHandleSubscribe(subscriber);
  }
  return storage;
}
var defaultStorage = createJSONStorage();

// node_modules/jotai/esm/react/utils.mjs
var import_react2 = __toESM(require_react(), 1);
function useAtomCallback(callback, options) {
  const anAtom = (0, import_react2.useMemo)(
    () => atom(null, (get, set, ...args) => callback(get, set, ...args)),
    [callback]
  );
  return useSetAtom(anAtom, options);
}

// node_modules/@pipecat-ai/client-react/dist/index.module.js
var $f3f7d4263dc13c6a$var$defaultStore = (0, createStore)();
var $f3f7d4263dc13c6a$export$8d2b07cbee622e7c = (0, import_react4.createContext)({});
var $f3f7d4263dc13c6a$export$4a4ae2d5dc96782 = ({ children, client, jotaiStore = $f3f7d4263dc13c6a$var$defaultStore }) => {
  return (0, import_jsx_runtime.jsx)((0, Provider), {
    store: jotaiStore,
    children: (0, import_jsx_runtime.jsx)($f3f7d4263dc13c6a$export$8d2b07cbee622e7c.Provider, {
      value: {
        client
      },
      children
    })
  });
};
$f3f7d4263dc13c6a$export$4a4ae2d5dc96782.displayName = "RTVIClientProvider";
var $54a3c9f5bdbf0854$export$31a5f6a22c9b8fba = () => {
  const { client } = (0, import_react4.useContext)((0, $f3f7d4263dc13c6a$export$8d2b07cbee622e7c));
  return client;
};
var $824ea64b5f757259$export$33a6ac53b8f02625 = (event, handler) => {
  const client = (0, $54a3c9f5bdbf0854$export$31a5f6a22c9b8fba)();
  (0, import_react4.useEffect)(() => {
    if (!client) return;
    client.on(event, handler);
    return () => {
      client.off(event, handler);
    };
  }, [
    event,
    handler,
    client
  ]);
};
var $194c75143b7a1fa0$var$localAudioTrackAtom = (0, atom)(null);
var $194c75143b7a1fa0$var$localVideoTrackAtom = (0, atom)(null);
var $194c75143b7a1fa0$var$localScreenAudioTrackAtom = (0, atom)(null);
var $194c75143b7a1fa0$var$localScreenVideoTrackAtom = (0, atom)(null);
var $194c75143b7a1fa0$var$botAudioTrackAtom = (0, atom)(null);
var $194c75143b7a1fa0$var$botVideoTrackAtom = (0, atom)(null);
var $194c75143b7a1fa0$var$trackAtom = (0, atomFamily)(({ local, trackType }) => {
  if (local) switch (trackType) {
    case "audio":
      return $194c75143b7a1fa0$var$localAudioTrackAtom;
    case "screenAudio":
      return $194c75143b7a1fa0$var$localScreenAudioTrackAtom;
    case "screenVideo":
      return $194c75143b7a1fa0$var$localScreenVideoTrackAtom;
    case "video":
      return $194c75143b7a1fa0$var$localVideoTrackAtom;
  }
  return trackType === "audio" ? $194c75143b7a1fa0$var$botAudioTrackAtom : $194c75143b7a1fa0$var$botVideoTrackAtom;
});
var $194c75143b7a1fa0$export$7c03381e0d26a6c3 = (trackType, participantType) => {
  const client = (0, $54a3c9f5bdbf0854$export$31a5f6a22c9b8fba)();
  const track = (0, useAtomValue)($194c75143b7a1fa0$var$trackAtom({
    local: participantType === "local",
    trackType
  }));
  const updateTrack = (0, useAtomCallback)((0, import_react4.useCallback)((get, set, track2, trackType2, local) => {
    const atom2 = $194c75143b7a1fa0$var$trackAtom({
      local,
      trackType: trackType2
    });
    const oldTrack = get(atom2);
    if ((oldTrack == null ? void 0 : oldTrack.id) === track2.id) return;
    set(atom2, track2);
  }, [
    participantType,
    track,
    trackType
  ]));
  (0, $824ea64b5f757259$export$33a6ac53b8f02625)((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).TrackStarted, (0, import_react4.useCallback)((track2, participant) => {
    updateTrack(track2, track2.kind, Boolean(participant == null ? void 0 : participant.local));
  }, []));
  (0, $824ea64b5f757259$export$33a6ac53b8f02625)((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).ScreenTrackStarted, (0, import_react4.useCallback)((track2, participant) => {
    const trackType2 = track2.kind === "audio" ? "screenAudio" : "screenVideo";
    updateTrack(track2, trackType2, Boolean(participant == null ? void 0 : participant.local));
  }, []));
  (0, import_react4.useEffect)(() => {
    var _a;
    if (!client) return;
    const tracks = client.tracks();
    const track2 = (_a = tracks == null ? void 0 : tracks[participantType]) == null ? void 0 : _a[trackType];
    if (!track2) return;
    updateTrack(track2, trackType, participantType === "local");
  }, [
    participantType,
    trackType,
    updateTrack,
    client
  ]);
  return track;
};
var $f8b885726fc652c0$export$ba1245f7cbf3ae02 = () => {
  const botAudioRef = (0, import_react4.useRef)(null);
  const botAudioTrack = (0, $194c75143b7a1fa0$export$7c03381e0d26a6c3)("audio", "bot");
  (0, import_react4.useEffect)(() => {
    if (!botAudioRef.current || !botAudioTrack) return;
    if (botAudioRef.current.srcObject) {
      const oldTrack = botAudioRef.current.srcObject.getAudioTracks()[0];
      if (oldTrack.id === botAudioTrack.id) return;
    }
    botAudioRef.current.srcObject = new MediaStream([
      botAudioTrack
    ]);
  }, [
    botAudioTrack
  ]);
  (0, $824ea64b5f757259$export$33a6ac53b8f02625)((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).SpeakerUpdated, (0, import_react4.useCallback)((speaker) => {
    if (!botAudioRef.current) return;
    if (typeof botAudioRef.current.setSinkId !== "function") return;
    botAudioRef.current.setSinkId(speaker.deviceId);
  }, []));
  return (0, import_jsx_runtime.jsx)((0, import_jsx_runtime.Fragment), {
    children: (0, import_jsx_runtime.jsx)("audio", {
      ref: botAudioRef,
      autoPlay: true
    })
  });
};
$f8b885726fc652c0$export$ba1245f7cbf3ae02.displayName = "RTVIClientAudio";
function $9098519210cf34e2$var$useMergedRef(...refs) {
  return (0, import_react4.useCallback)(
    (element) => {
      for (let i = 0; i < refs.length; i++) {
        const ref = refs[i];
        if (typeof ref === "function") ref(element);
        else if (ref && typeof ref === "object") ref.current = element;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    refs
  );
}
var $9098519210cf34e2$export$2e2bcd8739ae039 = $9098519210cf34e2$var$useMergedRef;
var $b76d887910983811$export$d090a384943608eb = (0, import_react4.forwardRef)(function VoiceClientVideo({ participant = "local", fit = "contain", mirror, onResize, style = {}, trackType = "video", ...props }, ref) {
  const videoTrack = (0, $194c75143b7a1fa0$export$7c03381e0d26a6c3)(trackType, participant);
  const videoEl = (0, import_react4.useRef)(null);
  const videoRef = (0, $9098519210cf34e2$export$2e2bcd8739ae039)(videoEl, ref);
  (0, import_react4.useEffect)(function setupVideoEvents() {
    const video = videoEl.current;
    if (!video) return;
    const playVideo = () => {
      const promise = video.play();
      if (promise !== void 0) promise.then(() => {
        video.controls = false;
      }).catch((error) => {
        video.controls = true;
        console.warn("Failed to play video", error);
      });
    };
    const handleCanPlay = () => {
      if (!video.paused) return;
      playVideo();
    };
    const handleEnterPIP = () => {
      video.style.transform = "scale(1)";
    };
    const handleLeavePIP = () => {
      video.style.transform = "";
      setTimeout(() => {
        if (video.paused) playVideo();
      }, 100);
    };
    const handleVisibilityChange = () => {
      if (document.visibilityState === "hidden") return;
      if (!video.paused) return;
      playVideo();
    };
    video.addEventListener("canplay", handleCanPlay);
    video.addEventListener("enterpictureinpicture", handleEnterPIP);
    video.addEventListener("leavepictureinpicture", handleLeavePIP);
    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => {
      video.removeEventListener("canplay", handleCanPlay);
      video.removeEventListener("enterpictureinpicture", handleEnterPIP);
      video.removeEventListener("leavepictureinpicture", handleLeavePIP);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);
  (0, import_react4.useEffect)(function updateSrcObject() {
    const video = videoEl.current;
    if (!video || !videoTrack) return;
    video.srcObject = new MediaStream([
      videoTrack
    ]);
    video.load();
    return () => {
      video.srcObject = null;
      video.load();
    };
  }, [
    videoTrack,
    videoTrack == null ? void 0 : videoTrack.id
  ]);
  (0, import_react4.useEffect)(function reportVideoDimensions() {
    const video = videoEl.current;
    if (!onResize || !video) return;
    let frame;
    function handleResize() {
      if (frame) cancelAnimationFrame(frame);
      frame = requestAnimationFrame(() => {
        const video2 = videoEl.current;
        if (!video2 || document.hidden) return;
        const videoWidth = video2.videoWidth;
        const videoHeight = video2.videoHeight;
        if (videoWidth && videoHeight) onResize == null ? void 0 : onResize({
          aspectRatio: videoWidth / videoHeight,
          height: videoHeight,
          width: videoWidth
        });
      });
    }
    handleResize();
    video.addEventListener("loadedmetadata", handleResize);
    video.addEventListener("resize", handleResize);
    return () => {
      if (frame) cancelAnimationFrame(frame);
      video.removeEventListener("loadedmetadata", handleResize);
      video.removeEventListener("resize", handleResize);
    };
  }, [
    onResize
  ]);
  return (0, import_jsx_runtime.jsx)("video", {
    autoPlay: true,
    muted: true,
    playsInline: true,
    ref: videoRef,
    style: {
      objectFit: fit,
      transform: mirror ? "scale(-1, 1)" : "",
      ...style
    },
    ...props
  });
});
$b76d887910983811$export$d090a384943608eb.displayName = "RTVIClientVideo";
var $c7d06534b21735c2$var$availableMicsAtom = (0, atom)([]);
var $c7d06534b21735c2$var$availableCamsAtom = (0, atom)([]);
var $c7d06534b21735c2$var$availableSpeakersAtom = (0, atom)([]);
var $c7d06534b21735c2$var$selectedMicAtom = (0, atom)({});
var $c7d06534b21735c2$var$selectedCamAtom = (0, atom)({});
var $c7d06534b21735c2$var$selectedSpeakerAtom = (0, atom)({});
var $c7d06534b21735c2$export$652c54907b83a48d = () => {
  const client = (0, $54a3c9f5bdbf0854$export$31a5f6a22c9b8fba)();
  const availableCams = (0, useAtomValue)($c7d06534b21735c2$var$availableCamsAtom);
  const availableMics = (0, useAtomValue)($c7d06534b21735c2$var$availableMicsAtom);
  const availableSpeakers = (0, useAtomValue)($c7d06534b21735c2$var$availableSpeakersAtom);
  const selectedCam = (0, useAtomValue)($c7d06534b21735c2$var$selectedCamAtom);
  const selectedMic = (0, useAtomValue)($c7d06534b21735c2$var$selectedMicAtom);
  const selectedSpeaker = (0, useAtomValue)($c7d06534b21735c2$var$selectedSpeakerAtom);
  (0, $824ea64b5f757259$export$33a6ac53b8f02625)((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).AvailableCamsUpdated, (0, useAtomCallback)((0, import_react4.useCallback)((_get, set, cams) => {
    set($c7d06534b21735c2$var$availableCamsAtom, cams);
  }, [])));
  (0, $824ea64b5f757259$export$33a6ac53b8f02625)((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).AvailableMicsUpdated, (0, useAtomCallback)((0, import_react4.useCallback)((_get, set, mics) => {
    set($c7d06534b21735c2$var$availableMicsAtom, mics);
  }, [])));
  (0, $824ea64b5f757259$export$33a6ac53b8f02625)((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).AvailableSpeakersUpdated, (0, useAtomCallback)((0, import_react4.useCallback)((_get, set, speakers) => {
    set($c7d06534b21735c2$var$availableSpeakersAtom, speakers);
  }, [])));
  (0, $824ea64b5f757259$export$33a6ac53b8f02625)((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).CamUpdated, (0, useAtomCallback)((0, import_react4.useCallback)((_get, set, cam) => {
    set($c7d06534b21735c2$var$selectedCamAtom, cam);
  }, [])));
  (0, $824ea64b5f757259$export$33a6ac53b8f02625)((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).MicUpdated, (0, useAtomCallback)((0, import_react4.useCallback)((_get, set, mic) => {
    set($c7d06534b21735c2$var$selectedMicAtom, mic);
  }, [])));
  (0, $824ea64b5f757259$export$33a6ac53b8f02625)((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).SpeakerUpdated, (0, useAtomCallback)((0, import_react4.useCallback)((_get, set, speaker) => {
    set($c7d06534b21735c2$var$selectedSpeakerAtom, speaker);
  }, [])));
  const updateCam = (0, import_react4.useCallback)((id) => {
    client == null ? void 0 : client.updateCam(id);
  }, [
    client
  ]);
  const updateMic = (0, import_react4.useCallback)((id) => {
    client == null ? void 0 : client.updateMic(id);
  }, [
    client
  ]);
  const updateSpeaker = (0, import_react4.useCallback)((id) => {
    client == null ? void 0 : client.updateSpeaker(id);
  }, [
    client
  ]);
  return {
    availableCams,
    availableMics,
    availableSpeakers,
    selectedCam,
    selectedMic,
    selectedSpeaker,
    updateCam,
    updateMic,
    updateSpeaker
  };
};
var $8376ffbc1b1f3c97$var$transportStateAtom = (0, atom)("disconnected");
var $8376ffbc1b1f3c97$export$599fa01283bd4ece = () => {
  const [transportState, setTransportState] = (0, useAtom)($8376ffbc1b1f3c97$var$transportStateAtom);
  (0, $824ea64b5f757259$export$33a6ac53b8f02625)((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).TransportStateChanged, setTransportState);
  return transportState;
};
var $993a744193844a95$export$59bf27bd43679db6 = (0, import_react4.default).memo(({ backgroundColor = "transparent", barColor = "black", barWidth = 30, barGap = 12, barMaxHeight = 120, participantType }) => {
  const canvasRef = (0, import_react4.useRef)(null);
  const track = (0, $194c75143b7a1fa0$export$7c03381e0d26a6c3)("audio", participantType);
  (0, import_react4.useEffect)(() => {
    if (!canvasRef.current) return;
    const canvasWidth = 5 * barWidth + 4 * barGap;
    const canvasHeight = barMaxHeight;
    const canvas = canvasRef.current;
    const scaleFactor = 2;
    const resizeCanvas = () => {
      canvas.width = canvasWidth * scaleFactor;
      canvas.height = canvasHeight * scaleFactor;
      canvas.style.width = `${canvasWidth}px`;
      canvas.style.height = `${canvasHeight}px`;
      canvasCtx.lineCap = "round";
      canvasCtx.scale(scaleFactor, scaleFactor);
    };
    const canvasCtx = canvas.getContext("2d");
    resizeCanvas();
    if (!track) return;
    const audioContext = new AudioContext();
    const source = audioContext.createMediaStreamSource(new MediaStream([
      track
    ]));
    const analyser = audioContext.createAnalyser();
    analyser.fftSize = 1024;
    source.connect(analyser);
    const frequencyData = new Uint8Array(analyser.frequencyBinCount);
    canvasCtx.lineCap = "round";
    const bands = [
      {
        startFreq: 85,
        endFreq: 255,
        smoothValue: 0
      },
      {
        startFreq: 255,
        endFreq: 500,
        smoothValue: 0
      },
      {
        startFreq: 500,
        endFreq: 2e3,
        smoothValue: 0
      },
      {
        startFreq: 2e3,
        endFreq: 4e3,
        smoothValue: 0
      },
      {
        startFreq: 4e3,
        endFreq: 8e3,
        smoothValue: 0
      }
    ];
    const getFrequencyBinIndex = (frequency) => {
      const nyquist = audioContext.sampleRate / 2;
      return Math.round(frequency / nyquist * (analyser.frequencyBinCount - 1));
    };
    function drawSpectrum() {
      analyser.getByteFrequencyData(frequencyData);
      canvasCtx.clearRect(0, 0, canvas.width / scaleFactor, canvas.height / scaleFactor);
      canvasCtx.fillStyle = backgroundColor;
      canvasCtx.fillRect(0, 0, canvas.width / scaleFactor, canvas.height / scaleFactor);
      let isActive = false;
      const totalBarsWidth = bands.length * barWidth + (bands.length - 1) * barGap;
      const startX = (canvas.width / scaleFactor - totalBarsWidth) / 2;
      const adjustedCircleRadius = barWidth / 2;
      bands.forEach((band, i) => {
        const startIndex = getFrequencyBinIndex(band.startFreq);
        const endIndex = getFrequencyBinIndex(band.endFreq);
        const bandData = frequencyData.slice(startIndex, endIndex);
        const bandValue = bandData.reduce((acc, val) => acc + val, 0) / bandData.length;
        const smoothingFactor = 0.2;
        if (bandValue < 1) band.smoothValue = Math.max(band.smoothValue - smoothingFactor * 5, 0);
        else {
          band.smoothValue = band.smoothValue + (bandValue - band.smoothValue) * smoothingFactor;
          isActive = true;
        }
        const x = startX + i * (barWidth + barGap);
        const barHeight = Math.min(band.smoothValue / 255 * barMaxHeight, barMaxHeight);
        const yTop = Math.max(canvas.height / scaleFactor / 2 - barHeight / 2, adjustedCircleRadius);
        const yBottom = Math.min(canvas.height / scaleFactor / 2 + barHeight / 2, canvas.height / scaleFactor - adjustedCircleRadius);
        if (band.smoothValue > 0) {
          canvasCtx.beginPath();
          canvasCtx.moveTo(x + barWidth / 2, yTop);
          canvasCtx.lineTo(x + barWidth / 2, yBottom);
          canvasCtx.lineWidth = barWidth;
          canvasCtx.strokeStyle = barColor;
          canvasCtx.stroke();
        } else {
          canvasCtx.beginPath();
          canvasCtx.arc(x + barWidth / 2, canvas.height / scaleFactor / 2, adjustedCircleRadius, 0, 2 * Math.PI);
          canvasCtx.fillStyle = barColor;
          canvasCtx.fill();
          canvasCtx.closePath();
        }
      });
      if (!isActive) drawInactiveCircles(adjustedCircleRadius, barColor);
      requestAnimationFrame(drawSpectrum);
    }
    function drawInactiveCircles(circleRadius, color) {
      const totalBarsWidth = bands.length * barWidth + (bands.length - 1) * barGap;
      const startX = (canvas.width / scaleFactor - totalBarsWidth) / 2;
      const y = canvas.height / scaleFactor / 2;
      bands.forEach((_, i) => {
        const x = startX + i * (barWidth + barGap);
        canvasCtx.beginPath();
        canvasCtx.arc(x + barWidth / 2, y, circleRadius, 0, 2 * Math.PI);
        canvasCtx.fillStyle = color;
        canvasCtx.fill();
        canvasCtx.closePath();
      });
    }
    drawSpectrum();
    window.addEventListener("resize", resizeCanvas);
    return () => {
      audioContext.close();
      window.removeEventListener("resize", resizeCanvas);
    };
  }, [
    backgroundColor,
    barColor,
    barGap,
    barMaxHeight,
    barWidth,
    track
  ]);
  return (0, import_jsx_runtime.jsx)("canvas", {
    ref: canvasRef,
    style: {
      display: "block",
      width: "100%",
      height: "100%"
    }
  });
});
$993a744193844a95$export$59bf27bd43679db6.displayName = "VoiceVisualizer";
export {
  $f8b885726fc652c0$export$ba1245f7cbf3ae02 as RTVIClientAudio,
  $f3f7d4263dc13c6a$export$4a4ae2d5dc96782 as RTVIClientProvider,
  $b76d887910983811$export$d090a384943608eb as RTVIClientVideo,
  $993a744193844a95$export$59bf27bd43679db6 as VoiceVisualizer,
  $54a3c9f5bdbf0854$export$31a5f6a22c9b8fba as useRTVIClient,
  $824ea64b5f757259$export$33a6ac53b8f02625 as useRTVIClientEvent,
  $c7d06534b21735c2$export$652c54907b83a48d as useRTVIClientMediaDevices,
  $194c75143b7a1fa0$export$7c03381e0d26a6c3 as useRTVIClientMediaTrack,
  $8376ffbc1b1f3c97$export$599fa01283bd4ece as useRTVIClientTransportState
};
//# sourceMappingURL=@pipecat-ai_client-react.js.map
