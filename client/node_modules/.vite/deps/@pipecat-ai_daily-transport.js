import {
  $4086f06442fcb7d7$export$86495b081fef8e52,
  $7afbbd59ebaa42bf$export$af88d00dbe7f521,
  $8ead7b33b8402751$export$59b4786f333aac02,
  $8ead7b33b8402751$export$e0624a511a2c4e9,
  $b48f893ed1354c1e$export$69aa9ab0334b212
} from "./chunk-JAMCOKTQ.js";
import "./chunk-5WRI5ZAA.js";

// node_modules/@daily-co/daily-js/dist/daily-esm.js
function e(e2, t2) {
  if (null == e2) return {};
  var n2, r2, i2 = function(e3, t3) {
    if (null == e3) return {};
    var n3 = {};
    for (var r3 in e3) if ({}.hasOwnProperty.call(e3, r3)) {
      if (-1 !== t3.indexOf(r3)) continue;
      n3[r3] = e3[r3];
    }
    return n3;
  }(e2, t2);
  if (Object.getOwnPropertySymbols) {
    var o2 = Object.getOwnPropertySymbols(e2);
    for (r2 = 0; r2 < o2.length; r2++) n2 = o2[r2], -1 === t2.indexOf(n2) && {}.propertyIsEnumerable.call(e2, n2) && (i2[n2] = e2[n2]);
  }
  return i2;
}
function t(e2, t2) {
  if (!(e2 instanceof t2)) throw new TypeError("Cannot call a class as a function");
}
function n(e2) {
  return n = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e3) {
    return typeof e3;
  } : function(e3) {
    return e3 && "function" == typeof Symbol && e3.constructor === Symbol && e3 !== Symbol.prototype ? "symbol" : typeof e3;
  }, n(e2);
}
function r(e2) {
  var t2 = function(e3, t3) {
    if ("object" != n(e3) || !e3) return e3;
    var r2 = e3[Symbol.toPrimitive];
    if (void 0 !== r2) {
      var i2 = r2.call(e3, t3 || "default");
      if ("object" != n(i2)) return i2;
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return ("string" === t3 ? String : Number)(e3);
  }(e2, "string");
  return "symbol" == n(t2) ? t2 : t2 + "";
}
function i(e2, t2) {
  for (var n2 = 0; n2 < t2.length; n2++) {
    var i2 = t2[n2];
    i2.enumerable = i2.enumerable || false, i2.configurable = true, "value" in i2 && (i2.writable = true), Object.defineProperty(e2, r(i2.key), i2);
  }
}
function o(e2, t2, n2) {
  return t2 && i(e2.prototype, t2), n2 && i(e2, n2), Object.defineProperty(e2, "prototype", { writable: false }), e2;
}
function s(e2, t2) {
  if (t2 && ("object" == n(t2) || "function" == typeof t2)) return t2;
  if (void 0 !== t2) throw new TypeError("Derived constructors may only return object or undefined");
  return function(e3) {
    if (void 0 === e3) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    return e3;
  }(e2);
}
function a(e2) {
  return a = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e3) {
    return e3.__proto__ || Object.getPrototypeOf(e3);
  }, a(e2);
}
function c(e2, t2) {
  return c = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e3, t3) {
    return e3.__proto__ = t3, e3;
  }, c(e2, t2);
}
function l(e2, t2) {
  if ("function" != typeof t2 && null !== t2) throw new TypeError("Super expression must either be null or a function");
  e2.prototype = Object.create(t2 && t2.prototype, { constructor: { value: e2, writable: true, configurable: true } }), Object.defineProperty(e2, "prototype", { writable: false }), t2 && c(e2, t2);
}
function u(e2, t2, n2) {
  return (t2 = r(t2)) in e2 ? Object.defineProperty(e2, t2, { value: n2, enumerable: true, configurable: true, writable: true }) : e2[t2] = n2, e2;
}
function d(e2, t2, n2, r2, i2, o2, s2) {
  try {
    var a2 = e2[o2](s2), c2 = a2.value;
  } catch (e3) {
    return void n2(e3);
  }
  a2.done ? t2(c2) : Promise.resolve(c2).then(r2, i2);
}
function p(e2) {
  return function() {
    var t2 = this, n2 = arguments;
    return new Promise(function(r2, i2) {
      var o2 = e2.apply(t2, n2);
      function s2(e3) {
        d(o2, r2, i2, s2, a2, "next", e3);
      }
      function a2(e3) {
        d(o2, r2, i2, s2, a2, "throw", e3);
      }
      s2(void 0);
    });
  };
}
function h(e2, t2) {
  (null == t2 || t2 > e2.length) && (t2 = e2.length);
  for (var n2 = 0, r2 = Array(t2); n2 < t2; n2++) r2[n2] = e2[n2];
  return r2;
}
function f(e2, t2) {
  return function(e3) {
    if (Array.isArray(e3)) return e3;
  }(e2) || function(e3, t3) {
    var n2 = null == e3 ? null : "undefined" != typeof Symbol && e3[Symbol.iterator] || e3["@@iterator"];
    if (null != n2) {
      var r2, i2, o2, s2, a2 = [], c2 = true, l2 = false;
      try {
        if (o2 = (n2 = n2.call(e3)).next, 0 === t3) {
          if (Object(n2) !== n2) return;
          c2 = false;
        } else for (; !(c2 = (r2 = o2.call(n2)).done) && (a2.push(r2.value), a2.length !== t3); c2 = true) ;
      } catch (e4) {
        l2 = true, i2 = e4;
      } finally {
        try {
          if (!c2 && null != n2.return && (s2 = n2.return(), Object(s2) !== s2)) return;
        } finally {
          if (l2) throw i2;
        }
      }
      return a2;
    }
  }(e2, t2) || function(e3, t3) {
    if (e3) {
      if ("string" == typeof e3) return h(e3, t3);
      var n2 = {}.toString.call(e3).slice(8, -1);
      return "Object" === n2 && e3.constructor && (n2 = e3.constructor.name), "Map" === n2 || "Set" === n2 ? Array.from(e3) : "Arguments" === n2 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n2) ? h(e3, t3) : void 0;
    }
  }(e2, t2) || function() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function v(e2) {
  return e2 && e2.__esModule && Object.prototype.hasOwnProperty.call(e2, "default") ? e2.default : e2;
}
var g;
var m = { exports: {} };
var y = "object" == typeof Reflect ? Reflect : null;
var b = y && "function" == typeof y.apply ? y.apply : function(e2, t2, n2) {
  return Function.prototype.apply.call(e2, t2, n2);
};
g = y && "function" == typeof y.ownKeys ? y.ownKeys : Object.getOwnPropertySymbols ? function(e2) {
  return Object.getOwnPropertyNames(e2).concat(Object.getOwnPropertySymbols(e2));
} : function(e2) {
  return Object.getOwnPropertyNames(e2);
};
var _ = Number.isNaN || function(e2) {
  return e2 != e2;
};
function w() {
  w.init.call(this);
}
m.exports = w, m.exports.once = function(e2, t2) {
  return new Promise(function(n2, r2) {
    function i2(n3) {
      e2.removeListener(t2, o2), r2(n3);
    }
    function o2() {
      "function" == typeof e2.removeListener && e2.removeListener("error", i2), n2([].slice.call(arguments));
    }
    j(e2, t2, o2, { once: true }), "error" !== t2 && function(e3, t3, n3) {
      "function" == typeof e3.on && j(e3, "error", t3, n3);
    }(e2, i2, { once: true });
  });
}, w.EventEmitter = w, w.prototype._events = void 0, w.prototype._eventsCount = 0, w.prototype._maxListeners = void 0;
var S = 10;
function k(e2) {
  if ("function" != typeof e2) throw new TypeError('The "listener" argument must be of type Function. Received type ' + typeof e2);
}
function M(e2) {
  return void 0 === e2._maxListeners ? w.defaultMaxListeners : e2._maxListeners;
}
function C(e2, t2, n2, r2) {
  var i2, o2, s2, a2;
  if (k(n2), void 0 === (o2 = e2._events) ? (o2 = e2._events = /* @__PURE__ */ Object.create(null), e2._eventsCount = 0) : (void 0 !== o2.newListener && (e2.emit("newListener", t2, n2.listener ? n2.listener : n2), o2 = e2._events), s2 = o2[t2]), void 0 === s2) s2 = o2[t2] = n2, ++e2._eventsCount;
  else if ("function" == typeof s2 ? s2 = o2[t2] = r2 ? [n2, s2] : [s2, n2] : r2 ? s2.unshift(n2) : s2.push(n2), (i2 = M(e2)) > 0 && s2.length > i2 && !s2.warned) {
    s2.warned = true;
    var c2 = new Error("Possible EventEmitter memory leak detected. " + s2.length + " " + String(t2) + " listeners added. Use emitter.setMaxListeners() to increase limit");
    c2.name = "MaxListenersExceededWarning", c2.emitter = e2, c2.type = t2, c2.count = s2.length, a2 = c2, console && console.warn && console.warn(a2);
  }
  return e2;
}
function E() {
  if (!this.fired) return this.target.removeListener(this.type, this.wrapFn), this.fired = true, 0 === arguments.length ? this.listener.call(this.target) : this.listener.apply(this.target, arguments);
}
function T(e2, t2, n2) {
  var r2 = { fired: false, wrapFn: void 0, target: e2, type: t2, listener: n2 }, i2 = E.bind(r2);
  return i2.listener = n2, r2.wrapFn = i2, i2;
}
function O(e2, t2, n2) {
  var r2 = e2._events;
  if (void 0 === r2) return [];
  var i2 = r2[t2];
  return void 0 === i2 ? [] : "function" == typeof i2 ? n2 ? [i2.listener || i2] : [i2] : n2 ? function(e3) {
    for (var t3 = new Array(e3.length), n3 = 0; n3 < t3.length; ++n3) t3[n3] = e3[n3].listener || e3[n3];
    return t3;
  }(i2) : A(i2, i2.length);
}
function P(e2) {
  var t2 = this._events;
  if (void 0 !== t2) {
    var n2 = t2[e2];
    if ("function" == typeof n2) return 1;
    if (void 0 !== n2) return n2.length;
  }
  return 0;
}
function A(e2, t2) {
  for (var n2 = new Array(t2), r2 = 0; r2 < t2; ++r2) n2[r2] = e2[r2];
  return n2;
}
function j(e2, t2, n2, r2) {
  if ("function" == typeof e2.on) r2.once ? e2.once(t2, n2) : e2.on(t2, n2);
  else {
    if ("function" != typeof e2.addEventListener) throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type ' + typeof e2);
    e2.addEventListener(t2, function i2(o2) {
      r2.once && e2.removeEventListener(t2, i2), n2(o2);
    });
  }
}
Object.defineProperty(w, "defaultMaxListeners", { enumerable: true, get: function() {
  return S;
}, set: function(e2) {
  if ("number" != typeof e2 || e2 < 0 || _(e2)) throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received ' + e2 + ".");
  S = e2;
} }), w.init = function() {
  void 0 !== this._events && this._events !== Object.getPrototypeOf(this)._events || (this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0), this._maxListeners = this._maxListeners || void 0;
}, w.prototype.setMaxListeners = function(e2) {
  if ("number" != typeof e2 || e2 < 0 || _(e2)) throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received ' + e2 + ".");
  return this._maxListeners = e2, this;
}, w.prototype.getMaxListeners = function() {
  return M(this);
}, w.prototype.emit = function(e2) {
  for (var t2 = [], n2 = 1; n2 < arguments.length; n2++) t2.push(arguments[n2]);
  var r2 = "error" === e2, i2 = this._events;
  if (void 0 !== i2) r2 = r2 && void 0 === i2.error;
  else if (!r2) return false;
  if (r2) {
    var o2;
    if (t2.length > 0 && (o2 = t2[0]), o2 instanceof Error) throw o2;
    var s2 = new Error("Unhandled error." + (o2 ? " (" + o2.message + ")" : ""));
    throw s2.context = o2, s2;
  }
  var a2 = i2[e2];
  if (void 0 === a2) return false;
  if ("function" == typeof a2) b(a2, this, t2);
  else {
    var c2 = a2.length, l2 = A(a2, c2);
    for (n2 = 0; n2 < c2; ++n2) b(l2[n2], this, t2);
  }
  return true;
}, w.prototype.addListener = function(e2, t2) {
  return C(this, e2, t2, false);
}, w.prototype.on = w.prototype.addListener, w.prototype.prependListener = function(e2, t2) {
  return C(this, e2, t2, true);
}, w.prototype.once = function(e2, t2) {
  return k(t2), this.on(e2, T(this, e2, t2)), this;
}, w.prototype.prependOnceListener = function(e2, t2) {
  return k(t2), this.prependListener(e2, T(this, e2, t2)), this;
}, w.prototype.removeListener = function(e2, t2) {
  var n2, r2, i2, o2, s2;
  if (k(t2), void 0 === (r2 = this._events)) return this;
  if (void 0 === (n2 = r2[e2])) return this;
  if (n2 === t2 || n2.listener === t2) 0 == --this._eventsCount ? this._events = /* @__PURE__ */ Object.create(null) : (delete r2[e2], r2.removeListener && this.emit("removeListener", e2, n2.listener || t2));
  else if ("function" != typeof n2) {
    for (i2 = -1, o2 = n2.length - 1; o2 >= 0; o2--) if (n2[o2] === t2 || n2[o2].listener === t2) {
      s2 = n2[o2].listener, i2 = o2;
      break;
    }
    if (i2 < 0) return this;
    0 === i2 ? n2.shift() : function(e3, t3) {
      for (; t3 + 1 < e3.length; t3++) e3[t3] = e3[t3 + 1];
      e3.pop();
    }(n2, i2), 1 === n2.length && (r2[e2] = n2[0]), void 0 !== r2.removeListener && this.emit("removeListener", e2, s2 || t2);
  }
  return this;
}, w.prototype.off = w.prototype.removeListener, w.prototype.removeAllListeners = function(e2) {
  var t2, n2, r2;
  if (void 0 === (n2 = this._events)) return this;
  if (void 0 === n2.removeListener) return 0 === arguments.length ? (this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0) : void 0 !== n2[e2] && (0 == --this._eventsCount ? this._events = /* @__PURE__ */ Object.create(null) : delete n2[e2]), this;
  if (0 === arguments.length) {
    var i2, o2 = Object.keys(n2);
    for (r2 = 0; r2 < o2.length; ++r2) "removeListener" !== (i2 = o2[r2]) && this.removeAllListeners(i2);
    return this.removeAllListeners("removeListener"), this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0, this;
  }
  if ("function" == typeof (t2 = n2[e2])) this.removeListener(e2, t2);
  else if (void 0 !== t2) for (r2 = t2.length - 1; r2 >= 0; r2--) this.removeListener(e2, t2[r2]);
  return this;
}, w.prototype.listeners = function(e2) {
  return O(this, e2, true);
}, w.prototype.rawListeners = function(e2) {
  return O(this, e2, false);
}, w.listenerCount = function(e2, t2) {
  return "function" == typeof e2.listenerCount ? e2.listenerCount(t2) : P.call(e2, t2);
}, w.prototype.listenerCount = P, w.prototype.eventNames = function() {
  return this._eventsCount > 0 ? g(this._events) : [];
};
var I = m.exports;
var x = v(I);
var L = Object.prototype.hasOwnProperty;
function D(e2, t2, n2) {
  for (n2 of e2.keys()) if (N(n2, t2)) return n2;
}
function N(e2, t2) {
  var n2, r2, i2;
  if (e2 === t2) return true;
  if (e2 && t2 && (n2 = e2.constructor) === t2.constructor) {
    if (n2 === Date) return e2.getTime() === t2.getTime();
    if (n2 === RegExp) return e2.toString() === t2.toString();
    if (n2 === Array) {
      if ((r2 = e2.length) === t2.length) for (; r2-- && N(e2[r2], t2[r2]); ) ;
      return -1 === r2;
    }
    if (n2 === Set) {
      if (e2.size !== t2.size) return false;
      for (r2 of e2) {
        if ((i2 = r2) && "object" == typeof i2 && !(i2 = D(t2, i2))) return false;
        if (!t2.has(i2)) return false;
      }
      return true;
    }
    if (n2 === Map) {
      if (e2.size !== t2.size) return false;
      for (r2 of e2) {
        if ((i2 = r2[0]) && "object" == typeof i2 && !(i2 = D(t2, i2))) return false;
        if (!N(r2[1], t2.get(i2))) return false;
      }
      return true;
    }
    if (n2 === ArrayBuffer) e2 = new Uint8Array(e2), t2 = new Uint8Array(t2);
    else if (n2 === DataView) {
      if ((r2 = e2.byteLength) === t2.byteLength) for (; r2-- && e2.getInt8(r2) === t2.getInt8(r2); ) ;
      return -1 === r2;
    }
    if (ArrayBuffer.isView(e2)) {
      if ((r2 = e2.byteLength) === t2.byteLength) for (; r2-- && e2[r2] === t2[r2]; ) ;
      return -1 === r2;
    }
    if (!n2 || "object" == typeof e2) {
      for (n2 in r2 = 0, e2) {
        if (L.call(e2, n2) && ++r2 && !L.call(t2, n2)) return false;
        if (!(n2 in t2) || !N(e2[n2], t2[n2])) return false;
      }
      return Object.keys(t2).length === r2;
    }
  }
  return e2 != e2 && t2 != t2;
}
var R = { "Amazon Silk": "amazon_silk", "Android Browser": "android", Bada: "bada", BlackBerry: "blackberry", Chrome: "chrome", Chromium: "chromium", Electron: "electron", Epiphany: "epiphany", Firefox: "firefox", Focus: "focus", Generic: "generic", "Google Search": "google_search", Googlebot: "googlebot", "Internet Explorer": "ie", "K-Meleon": "k_meleon", Maxthon: "maxthon", "Microsoft Edge": "edge", "MZ Browser": "mz", "NAVER Whale Browser": "naver", Opera: "opera", "Opera Coast": "opera_coast", PhantomJS: "phantomjs", Puffin: "puffin", QupZilla: "qupzilla", QQ: "qq", QQLite: "qqlite", Safari: "safari", Sailfish: "sailfish", "Samsung Internet for Android": "samsung_internet", SeaMonkey: "seamonkey", Sleipnir: "sleipnir", Swing: "swing", Tizen: "tizen", "UC Browser": "uc", Vivaldi: "vivaldi", "WebOS Browser": "webos", WeChat: "wechat", "Yandex Browser": "yandex", Roku: "roku" };
var F = { amazon_silk: "Amazon Silk", android: "Android Browser", bada: "Bada", blackberry: "BlackBerry", chrome: "Chrome", chromium: "Chromium", electron: "Electron", epiphany: "Epiphany", firefox: "Firefox", focus: "Focus", generic: "Generic", googlebot: "Googlebot", google_search: "Google Search", ie: "Internet Explorer", k_meleon: "K-Meleon", maxthon: "Maxthon", edge: "Microsoft Edge", mz: "MZ Browser", naver: "NAVER Whale Browser", opera: "Opera", opera_coast: "Opera Coast", phantomjs: "PhantomJS", puffin: "Puffin", qupzilla: "QupZilla", qq: "QQ Browser", qqlite: "QQ Browser Lite", safari: "Safari", sailfish: "Sailfish", samsung_internet: "Samsung Internet for Android", seamonkey: "SeaMonkey", sleipnir: "Sleipnir", swing: "Swing", tizen: "Tizen", uc: "UC Browser", vivaldi: "Vivaldi", webos: "WebOS Browser", wechat: "WeChat", yandex: "Yandex Browser" };
var B = { tablet: "tablet", mobile: "mobile", desktop: "desktop", tv: "tv" };
var U = { WindowsPhone: "Windows Phone", Windows: "Windows", MacOS: "macOS", iOS: "iOS", Android: "Android", WebOS: "WebOS", BlackBerry: "BlackBerry", Bada: "Bada", Tizen: "Tizen", Linux: "Linux", ChromeOS: "Chrome OS", PlayStation4: "PlayStation 4", Roku: "Roku" };
var V = { EdgeHTML: "EdgeHTML", Blink: "Blink", Trident: "Trident", Presto: "Presto", Gecko: "Gecko", WebKit: "WebKit" };
var J = class _J {
  static getFirstMatch(e2, t2) {
    const n2 = t2.match(e2);
    return n2 && n2.length > 0 && n2[1] || "";
  }
  static getSecondMatch(e2, t2) {
    const n2 = t2.match(e2);
    return n2 && n2.length > 1 && n2[2] || "";
  }
  static matchAndReturnConst(e2, t2, n2) {
    if (e2.test(t2)) return n2;
  }
  static getWindowsVersionName(e2) {
    switch (e2) {
      case "NT":
        return "NT";
      case "XP":
      case "NT 5.1":
        return "XP";
      case "NT 5.0":
        return "2000";
      case "NT 5.2":
        return "2003";
      case "NT 6.0":
        return "Vista";
      case "NT 6.1":
        return "7";
      case "NT 6.2":
        return "8";
      case "NT 6.3":
        return "8.1";
      case "NT 10.0":
        return "10";
      default:
        return;
    }
  }
  static getMacOSVersionName(e2) {
    const t2 = e2.split(".").splice(0, 2).map((e3) => parseInt(e3, 10) || 0);
    if (t2.push(0), 10 === t2[0]) switch (t2[1]) {
      case 5:
        return "Leopard";
      case 6:
        return "Snow Leopard";
      case 7:
        return "Lion";
      case 8:
        return "Mountain Lion";
      case 9:
        return "Mavericks";
      case 10:
        return "Yosemite";
      case 11:
        return "El Capitan";
      case 12:
        return "Sierra";
      case 13:
        return "High Sierra";
      case 14:
        return "Mojave";
      case 15:
        return "Catalina";
      default:
        return;
    }
  }
  static getAndroidVersionName(e2) {
    const t2 = e2.split(".").splice(0, 2).map((e3) => parseInt(e3, 10) || 0);
    if (t2.push(0), !(1 === t2[0] && t2[1] < 5)) return 1 === t2[0] && t2[1] < 6 ? "Cupcake" : 1 === t2[0] && t2[1] >= 6 ? "Donut" : 2 === t2[0] && t2[1] < 2 ? "Eclair" : 2 === t2[0] && 2 === t2[1] ? "Froyo" : 2 === t2[0] && t2[1] > 2 ? "Gingerbread" : 3 === t2[0] ? "Honeycomb" : 4 === t2[0] && t2[1] < 1 ? "Ice Cream Sandwich" : 4 === t2[0] && t2[1] < 4 ? "Jelly Bean" : 4 === t2[0] && t2[1] >= 4 ? "KitKat" : 5 === t2[0] ? "Lollipop" : 6 === t2[0] ? "Marshmallow" : 7 === t2[0] ? "Nougat" : 8 === t2[0] ? "Oreo" : 9 === t2[0] ? "Pie" : void 0;
  }
  static getVersionPrecision(e2) {
    return e2.split(".").length;
  }
  static compareVersions(e2, t2, n2 = false) {
    const r2 = _J.getVersionPrecision(e2), i2 = _J.getVersionPrecision(t2);
    let o2 = Math.max(r2, i2), s2 = 0;
    const a2 = _J.map([e2, t2], (e3) => {
      const t3 = o2 - _J.getVersionPrecision(e3), n3 = e3 + new Array(t3 + 1).join(".0");
      return _J.map(n3.split("."), (e4) => new Array(20 - e4.length).join("0") + e4).reverse();
    });
    for (n2 && (s2 = o2 - Math.min(r2, i2)), o2 -= 1; o2 >= s2; ) {
      if (a2[0][o2] > a2[1][o2]) return 1;
      if (a2[0][o2] === a2[1][o2]) {
        if (o2 === s2) return 0;
        o2 -= 1;
      } else if (a2[0][o2] < a2[1][o2]) return -1;
    }
  }
  static map(e2, t2) {
    const n2 = [];
    let r2;
    if (Array.prototype.map) return Array.prototype.map.call(e2, t2);
    for (r2 = 0; r2 < e2.length; r2 += 1) n2.push(t2(e2[r2]));
    return n2;
  }
  static find(e2, t2) {
    let n2, r2;
    if (Array.prototype.find) return Array.prototype.find.call(e2, t2);
    for (n2 = 0, r2 = e2.length; n2 < r2; n2 += 1) {
      const r3 = e2[n2];
      if (t2(r3, n2)) return r3;
    }
  }
  static assign(e2, ...t2) {
    const n2 = e2;
    let r2, i2;
    if (Object.assign) return Object.assign(e2, ...t2);
    for (r2 = 0, i2 = t2.length; r2 < i2; r2 += 1) {
      const e3 = t2[r2];
      if ("object" == typeof e3 && null !== e3) {
        Object.keys(e3).forEach((t3) => {
          n2[t3] = e3[t3];
        });
      }
    }
    return e2;
  }
  static getBrowserAlias(e2) {
    return R[e2];
  }
  static getBrowserTypeByAlias(e2) {
    return F[e2] || "";
  }
};
var $ = /version\/(\d+(\.?_?\d+)+)/i;
var q = [{ test: [/googlebot/i], describe(e2) {
  const t2 = { name: "Googlebot" }, n2 = J.getFirstMatch(/googlebot\/(\d+(\.\d+))/i, e2) || J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/opera/i], describe(e2) {
  const t2 = { name: "Opera" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/opr\/|opios/i], describe(e2) {
  const t2 = { name: "Opera" }, n2 = J.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i, e2) || J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/SamsungBrowser/i], describe(e2) {
  const t2 = { name: "Samsung Internet for Android" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/Whale/i], describe(e2) {
  const t2 = { name: "NAVER Whale Browser" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/MZBrowser/i], describe(e2) {
  const t2 = { name: "MZ Browser" }, n2 = J.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i, e2) || J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/focus/i], describe(e2) {
  const t2 = { name: "Focus" }, n2 = J.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i, e2) || J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/swing/i], describe(e2) {
  const t2 = { name: "Swing" }, n2 = J.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i, e2) || J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/coast/i], describe(e2) {
  const t2 = { name: "Opera Coast" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/opt\/\d+(?:.?_?\d+)+/i], describe(e2) {
  const t2 = { name: "Opera Touch" }, n2 = J.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/yabrowser/i], describe(e2) {
  const t2 = { name: "Yandex Browser" }, n2 = J.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/ucbrowser/i], describe(e2) {
  const t2 = { name: "UC Browser" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/Maxthon|mxios/i], describe(e2) {
  const t2 = { name: "Maxthon" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/epiphany/i], describe(e2) {
  const t2 = { name: "Epiphany" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/puffin/i], describe(e2) {
  const t2 = { name: "Puffin" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/sleipnir/i], describe(e2) {
  const t2 = { name: "Sleipnir" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/k-meleon/i], describe(e2) {
  const t2 = { name: "K-Meleon" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/micromessenger/i], describe(e2) {
  const t2 = { name: "WeChat" }, n2 = J.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/qqbrowser/i], describe(e2) {
  const t2 = { name: /qqbrowserlite/i.test(e2) ? "QQ Browser Lite" : "QQ Browser" }, n2 = J.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/msie|trident/i], describe(e2) {
  const t2 = { name: "Internet Explorer" }, n2 = J.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/\sedg\//i], describe(e2) {
  const t2 = { name: "Microsoft Edge" }, n2 = J.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/edg([ea]|ios)/i], describe(e2) {
  const t2 = { name: "Microsoft Edge" }, n2 = J.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/vivaldi/i], describe(e2) {
  const t2 = { name: "Vivaldi" }, n2 = J.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/seamonkey/i], describe(e2) {
  const t2 = { name: "SeaMonkey" }, n2 = J.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/sailfish/i], describe(e2) {
  const t2 = { name: "Sailfish" }, n2 = J.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/silk/i], describe(e2) {
  const t2 = { name: "Amazon Silk" }, n2 = J.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/phantom/i], describe(e2) {
  const t2 = { name: "PhantomJS" }, n2 = J.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/slimerjs/i], describe(e2) {
  const t2 = { name: "SlimerJS" }, n2 = J.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/blackberry|\bbb\d+/i, /rim\stablet/i], describe(e2) {
  const t2 = { name: "BlackBerry" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/(web|hpw)[o0]s/i], describe(e2) {
  const t2 = { name: "WebOS Browser" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/bada/i], describe(e2) {
  const t2 = { name: "Bada" }, n2 = J.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/tizen/i], describe(e2) {
  const t2 = { name: "Tizen" }, n2 = J.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/qupzilla/i], describe(e2) {
  const t2 = { name: "QupZilla" }, n2 = J.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/firefox|iceweasel|fxios/i], describe(e2) {
  const t2 = { name: "Firefox" }, n2 = J.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/electron/i], describe(e2) {
  const t2 = { name: "Electron" }, n2 = J.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/MiuiBrowser/i], describe(e2) {
  const t2 = { name: "Miui" }, n2 = J.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/chromium/i], describe(e2) {
  const t2 = { name: "Chromium" }, n2 = J.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/chrome|crios|crmo/i], describe(e2) {
  const t2 = { name: "Chrome" }, n2 = J.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/GSA/i], describe(e2) {
  const t2 = { name: "Google Search" }, n2 = J.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test(e2) {
  const t2 = !e2.test(/like android/i), n2 = e2.test(/android/i);
  return t2 && n2;
}, describe(e2) {
  const t2 = { name: "Android Browser" }, n2 = J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/playstation 4/i], describe(e2) {
  const t2 = { name: "PlayStation 4" }, n2 = J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/safari|applewebkit/i], describe(e2) {
  const t2 = { name: "Safari" }, n2 = J.getFirstMatch($, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/.*/i], describe(e2) {
  const t2 = -1 !== e2.search("\\(") ? /^(.*)\/(.*)[ \t]\((.*)/ : /^(.*)\/(.*) /;
  return { name: J.getFirstMatch(t2, e2), version: J.getSecondMatch(t2, e2) };
} }];
var z = [{ test: [/Roku\/DVP/], describe(e2) {
  const t2 = J.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i, e2);
  return { name: U.Roku, version: t2 };
} }, { test: [/windows phone/i], describe(e2) {
  const t2 = J.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i, e2);
  return { name: U.WindowsPhone, version: t2 };
} }, { test: [/windows /i], describe(e2) {
  const t2 = J.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i, e2), n2 = J.getWindowsVersionName(t2);
  return { name: U.Windows, version: t2, versionName: n2 };
} }, { test: [/Macintosh(.*?) FxiOS(.*?)\//], describe(e2) {
  const t2 = { name: U.iOS }, n2 = J.getSecondMatch(/(Version\/)(\d[\d.]+)/, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/macintosh/i], describe(e2) {
  const t2 = J.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i, e2).replace(/[_\s]/g, "."), n2 = J.getMacOSVersionName(t2), r2 = { name: U.MacOS, version: t2 };
  return n2 && (r2.versionName = n2), r2;
} }, { test: [/(ipod|iphone|ipad)/i], describe(e2) {
  const t2 = J.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i, e2).replace(/[_\s]/g, ".");
  return { name: U.iOS, version: t2 };
} }, { test(e2) {
  const t2 = !e2.test(/like android/i), n2 = e2.test(/android/i);
  return t2 && n2;
}, describe(e2) {
  const t2 = J.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i, e2), n2 = J.getAndroidVersionName(t2), r2 = { name: U.Android, version: t2 };
  return n2 && (r2.versionName = n2), r2;
} }, { test: [/(web|hpw)[o0]s/i], describe(e2) {
  const t2 = J.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i, e2), n2 = { name: U.WebOS };
  return t2 && t2.length && (n2.version = t2), n2;
} }, { test: [/blackberry|\bbb\d+/i, /rim\stablet/i], describe(e2) {
  const t2 = J.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i, e2) || J.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i, e2) || J.getFirstMatch(/\bbb(\d+)/i, e2);
  return { name: U.BlackBerry, version: t2 };
} }, { test: [/bada/i], describe(e2) {
  const t2 = J.getFirstMatch(/bada\/(\d+(\.\d+)*)/i, e2);
  return { name: U.Bada, version: t2 };
} }, { test: [/tizen/i], describe(e2) {
  const t2 = J.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i, e2);
  return { name: U.Tizen, version: t2 };
} }, { test: [/linux/i], describe: () => ({ name: U.Linux }) }, { test: [/CrOS/], describe: () => ({ name: U.ChromeOS }) }, { test: [/PlayStation 4/], describe(e2) {
  const t2 = J.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i, e2);
  return { name: U.PlayStation4, version: t2 };
} }];
var W = [{ test: [/googlebot/i], describe: () => ({ type: "bot", vendor: "Google" }) }, { test: [/huawei/i], describe(e2) {
  const t2 = J.getFirstMatch(/(can-l01)/i, e2) && "Nova", n2 = { type: B.mobile, vendor: "Huawei" };
  return t2 && (n2.model = t2), n2;
} }, { test: [/nexus\s*(?:7|8|9|10).*/i], describe: () => ({ type: B.tablet, vendor: "Nexus" }) }, { test: [/ipad/i], describe: () => ({ type: B.tablet, vendor: "Apple", model: "iPad" }) }, { test: [/Macintosh(.*?) FxiOS(.*?)\//], describe: () => ({ type: B.tablet, vendor: "Apple", model: "iPad" }) }, { test: [/kftt build/i], describe: () => ({ type: B.tablet, vendor: "Amazon", model: "Kindle Fire HD 7" }) }, { test: [/silk/i], describe: () => ({ type: B.tablet, vendor: "Amazon" }) }, { test: [/tablet(?! pc)/i], describe: () => ({ type: B.tablet }) }, { test(e2) {
  const t2 = e2.test(/ipod|iphone/i), n2 = e2.test(/like (ipod|iphone)/i);
  return t2 && !n2;
}, describe(e2) {
  const t2 = J.getFirstMatch(/(ipod|iphone)/i, e2);
  return { type: B.mobile, vendor: "Apple", model: t2 };
} }, { test: [/nexus\s*[0-6].*/i, /galaxy nexus/i], describe: () => ({ type: B.mobile, vendor: "Nexus" }) }, { test: [/[^-]mobi/i], describe: () => ({ type: B.mobile }) }, { test: (e2) => "blackberry" === e2.getBrowserName(true), describe: () => ({ type: B.mobile, vendor: "BlackBerry" }) }, { test: (e2) => "bada" === e2.getBrowserName(true), describe: () => ({ type: B.mobile }) }, { test: (e2) => "windows phone" === e2.getBrowserName(), describe: () => ({ type: B.mobile, vendor: "Microsoft" }) }, { test(e2) {
  const t2 = Number(String(e2.getOSVersion()).split(".")[0]);
  return "android" === e2.getOSName(true) && t2 >= 3;
}, describe: () => ({ type: B.tablet }) }, { test: (e2) => "android" === e2.getOSName(true), describe: () => ({ type: B.mobile }) }, { test: (e2) => "macos" === e2.getOSName(true), describe: () => ({ type: B.desktop, vendor: "Apple" }) }, { test: (e2) => "windows" === e2.getOSName(true), describe: () => ({ type: B.desktop }) }, { test: (e2) => "linux" === e2.getOSName(true), describe: () => ({ type: B.desktop }) }, { test: (e2) => "playstation 4" === e2.getOSName(true), describe: () => ({ type: B.tv }) }, { test: (e2) => "roku" === e2.getOSName(true), describe: () => ({ type: B.tv }) }];
var H = [{ test: (e2) => "microsoft edge" === e2.getBrowserName(true), describe(e2) {
  if (/\sedg\//i.test(e2)) return { name: V.Blink };
  const t2 = J.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i, e2);
  return { name: V.EdgeHTML, version: t2 };
} }, { test: [/trident/i], describe(e2) {
  const t2 = { name: V.Trident }, n2 = J.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: (e2) => e2.test(/presto/i), describe(e2) {
  const t2 = { name: V.Presto }, n2 = J.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test(e2) {
  const t2 = e2.test(/gecko/i), n2 = e2.test(/like gecko/i);
  return t2 && !n2;
}, describe(e2) {
  const t2 = { name: V.Gecko }, n2 = J.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }, { test: [/(apple)?webkit\/537\.36/i], describe: () => ({ name: V.Blink }) }, { test: [/(apple)?webkit/i], describe(e2) {
  const t2 = { name: V.WebKit }, n2 = J.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i, e2);
  return n2 && (t2.version = n2), t2;
} }];
var G = class {
  constructor(e2, t2 = false) {
    if (null == e2 || "" === e2) throw new Error("UserAgent parameter can't be empty");
    this._ua = e2, this.parsedResult = {}, true !== t2 && this.parse();
  }
  getUA() {
    return this._ua;
  }
  test(e2) {
    return e2.test(this._ua);
  }
  parseBrowser() {
    this.parsedResult.browser = {};
    const e2 = J.find(q, (e3) => {
      if ("function" == typeof e3.test) return e3.test(this);
      if (e3.test instanceof Array) return e3.test.some((e4) => this.test(e4));
      throw new Error("Browser's test function is not valid");
    });
    return e2 && (this.parsedResult.browser = e2.describe(this.getUA())), this.parsedResult.browser;
  }
  getBrowser() {
    return this.parsedResult.browser ? this.parsedResult.browser : this.parseBrowser();
  }
  getBrowserName(e2) {
    return e2 ? String(this.getBrowser().name).toLowerCase() || "" : this.getBrowser().name || "";
  }
  getBrowserVersion() {
    return this.getBrowser().version;
  }
  getOS() {
    return this.parsedResult.os ? this.parsedResult.os : this.parseOS();
  }
  parseOS() {
    this.parsedResult.os = {};
    const e2 = J.find(z, (e3) => {
      if ("function" == typeof e3.test) return e3.test(this);
      if (e3.test instanceof Array) return e3.test.some((e4) => this.test(e4));
      throw new Error("Browser's test function is not valid");
    });
    return e2 && (this.parsedResult.os = e2.describe(this.getUA())), this.parsedResult.os;
  }
  getOSName(e2) {
    const { name: t2 } = this.getOS();
    return e2 ? String(t2).toLowerCase() || "" : t2 || "";
  }
  getOSVersion() {
    return this.getOS().version;
  }
  getPlatform() {
    return this.parsedResult.platform ? this.parsedResult.platform : this.parsePlatform();
  }
  getPlatformType(e2 = false) {
    const { type: t2 } = this.getPlatform();
    return e2 ? String(t2).toLowerCase() || "" : t2 || "";
  }
  parsePlatform() {
    this.parsedResult.platform = {};
    const e2 = J.find(W, (e3) => {
      if ("function" == typeof e3.test) return e3.test(this);
      if (e3.test instanceof Array) return e3.test.some((e4) => this.test(e4));
      throw new Error("Browser's test function is not valid");
    });
    return e2 && (this.parsedResult.platform = e2.describe(this.getUA())), this.parsedResult.platform;
  }
  getEngine() {
    return this.parsedResult.engine ? this.parsedResult.engine : this.parseEngine();
  }
  getEngineName(e2) {
    return e2 ? String(this.getEngine().name).toLowerCase() || "" : this.getEngine().name || "";
  }
  parseEngine() {
    this.parsedResult.engine = {};
    const e2 = J.find(H, (e3) => {
      if ("function" == typeof e3.test) return e3.test(this);
      if (e3.test instanceof Array) return e3.test.some((e4) => this.test(e4));
      throw new Error("Browser's test function is not valid");
    });
    return e2 && (this.parsedResult.engine = e2.describe(this.getUA())), this.parsedResult.engine;
  }
  parse() {
    return this.parseBrowser(), this.parseOS(), this.parsePlatform(), this.parseEngine(), this;
  }
  getResult() {
    return J.assign({}, this.parsedResult);
  }
  satisfies(e2) {
    const t2 = {};
    let n2 = 0;
    const r2 = {};
    let i2 = 0;
    if (Object.keys(e2).forEach((o2) => {
      const s2 = e2[o2];
      "string" == typeof s2 ? (r2[o2] = s2, i2 += 1) : "object" == typeof s2 && (t2[o2] = s2, n2 += 1);
    }), n2 > 0) {
      const e3 = Object.keys(t2), n3 = J.find(e3, (e4) => this.isOS(e4));
      if (n3) {
        const e4 = this.satisfies(t2[n3]);
        if (void 0 !== e4) return e4;
      }
      const r3 = J.find(e3, (e4) => this.isPlatform(e4));
      if (r3) {
        const e4 = this.satisfies(t2[r3]);
        if (void 0 !== e4) return e4;
      }
    }
    if (i2 > 0) {
      const e3 = Object.keys(r2), t3 = J.find(e3, (e4) => this.isBrowser(e4, true));
      if (void 0 !== t3) return this.compareVersion(r2[t3]);
    }
  }
  isBrowser(e2, t2 = false) {
    const n2 = this.getBrowserName().toLowerCase();
    let r2 = e2.toLowerCase();
    const i2 = J.getBrowserTypeByAlias(r2);
    return t2 && i2 && (r2 = i2.toLowerCase()), r2 === n2;
  }
  compareVersion(e2) {
    let t2 = [0], n2 = e2, r2 = false;
    const i2 = this.getBrowserVersion();
    if ("string" == typeof i2) return ">" === e2[0] || "<" === e2[0] ? (n2 = e2.substr(1), "=" === e2[1] ? (r2 = true, n2 = e2.substr(2)) : t2 = [], ">" === e2[0] ? t2.push(1) : t2.push(-1)) : "=" === e2[0] ? n2 = e2.substr(1) : "~" === e2[0] && (r2 = true, n2 = e2.substr(1)), t2.indexOf(J.compareVersions(i2, n2, r2)) > -1;
  }
  isOS(e2) {
    return this.getOSName(true) === String(e2).toLowerCase();
  }
  isPlatform(e2) {
    return this.getPlatformType(true) === String(e2).toLowerCase();
  }
  isEngine(e2) {
    return this.getEngineName(true) === String(e2).toLowerCase();
  }
  is(e2, t2 = false) {
    return this.isBrowser(e2, t2) || this.isOS(e2) || this.isPlatform(e2);
  }
  some(e2 = []) {
    return e2.some((e3) => this.is(e3));
  }
};
var Q = class {
  static getParser(e2, t2 = false) {
    if ("string" != typeof e2) throw new Error("UserAgent should be a string");
    return new G(e2, t2);
  }
  static parse(e2) {
    return new G(e2).getResult();
  }
  static get BROWSER_MAP() {
    return F;
  }
  static get ENGINE_MAP() {
    return V;
  }
  static get OS_MAP() {
    return U;
  }
  static get PLATFORMS_MAP() {
    return B;
  }
};
function K() {
  return Date.now() + Math.random().toString();
}
function Y() {
  throw new Error("Method must be implemented in subclass");
}
function X(e2, t2) {
  return null != t2 && t2.proxyUrl ? t2.proxyUrl + ("/" === t2.proxyUrl.slice(-1) ? "" : "/") + e2.substring(8) : e2;
}
function Z(e2) {
  return null != e2 && e2.callObjectBundleUrlOverride ? e2.callObjectBundleUrlOverride : X("https://c.daily.co/call-machine/versioned/".concat("0.77.0", "/static/call-machine-object-bundle.js"), e2);
}
function ee(e2) {
  try {
    new URL(e2);
  } catch (e3) {
    return false;
  }
  return true;
}
var te = "undefined" == typeof __SENTRY_DEBUG__ || __SENTRY_DEBUG__;
var ne = "8.55.0";
var re = globalThis;
function ie(e2, t2, n2) {
  const r2 = n2 || re, i2 = r2.__SENTRY__ = r2.__SENTRY__ || {}, o2 = i2[ne] = i2[ne] || {};
  return o2[e2] || (o2[e2] = t2());
}
var oe = "undefined" == typeof __SENTRY_DEBUG__ || __SENTRY_DEBUG__;
var se = ["debug", "info", "warn", "error", "log", "assert", "trace"];
var ae = {};
function ce(e2) {
  if (!("console" in re)) return e2();
  const t2 = re.console, n2 = {}, r2 = Object.keys(ae);
  r2.forEach((e3) => {
    const r3 = ae[e3];
    n2[e3] = t2[e3], t2[e3] = r3;
  });
  try {
    return e2();
  } finally {
    r2.forEach((e3) => {
      t2[e3] = n2[e3];
    });
  }
}
var le = ie("logger", function() {
  let e2 = false;
  const t2 = { enable: () => {
    e2 = true;
  }, disable: () => {
    e2 = false;
  }, isEnabled: () => e2 };
  return oe ? se.forEach((n2) => {
    t2[n2] = (...t3) => {
      e2 && ce(() => {
        re.console[n2](`Sentry Logger [${n2}]:`, ...t3);
      });
    };
  }) : se.forEach((e3) => {
    t2[e3] = () => {
    };
  }), t2;
});
var ue = "?";
var de = /\(error: (.*)\)/;
var pe = /captureMessage|captureException/;
function he(e2) {
  return e2[e2.length - 1] || {};
}
var fe = "<anonymous>";
function ve(e2) {
  try {
    return e2 && "function" == typeof e2 && e2.name || fe;
  } catch (e3) {
    return fe;
  }
}
function ge(e2) {
  const t2 = e2.exception;
  if (t2) {
    const e3 = [];
    try {
      return t2.values.forEach((t3) => {
        t3.stacktrace.frames && e3.push(...t3.stacktrace.frames);
      }), e3;
    } catch (e4) {
      return;
    }
  }
}
var me = {};
var ye = {};
function be(e2, t2) {
  me[e2] = me[e2] || [], me[e2].push(t2);
}
function _e(e2, t2) {
  if (!ye[e2]) {
    ye[e2] = true;
    try {
      t2();
    } catch (t3) {
      oe && le.error(`Error while instrumenting ${e2}`, t3);
    }
  }
}
function we(e2, t2) {
  const n2 = e2 && me[e2];
  if (n2) for (const r2 of n2) try {
    r2(t2);
  } catch (t3) {
    oe && le.error(`Error while triggering instrumentation handler.
Type: ${e2}
Name: ${ve(r2)}
Error:`, t3);
  }
}
var Se = null;
function ke() {
  Se = re.onerror, re.onerror = function(e2, t2, n2, r2, i2) {
    return we("error", { column: r2, error: i2, line: n2, msg: e2, url: t2 }), !!Se && Se.apply(this, arguments);
  }, re.onerror.__SENTRY_INSTRUMENTED__ = true;
}
var Me = null;
function Ce() {
  Me = re.onunhandledrejection, re.onunhandledrejection = function(e2) {
    return we("unhandledrejection", e2), !Me || Me.apply(this, arguments);
  }, re.onunhandledrejection.__SENTRY_INSTRUMENTED__ = true;
}
function Ee() {
  return Te(re), re;
}
function Te(e2) {
  const t2 = e2.__SENTRY__ = e2.__SENTRY__ || {};
  return t2.version = t2.version || ne, t2[ne] = t2[ne] || {};
}
var Oe = Object.prototype.toString;
function Pe(e2) {
  switch (Oe.call(e2)) {
    case "[object Error]":
    case "[object Exception]":
    case "[object DOMException]":
    case "[object WebAssembly.Exception]":
      return true;
    default:
      return Be(e2, Error);
  }
}
function Ae(e2, t2) {
  return Oe.call(e2) === `[object ${t2}]`;
}
function je(e2) {
  return Ae(e2, "ErrorEvent");
}
function Ie(e2) {
  return Ae(e2, "DOMError");
}
function xe(e2) {
  return Ae(e2, "String");
}
function Le(e2) {
  return "object" == typeof e2 && null !== e2 && "__sentry_template_string__" in e2 && "__sentry_template_values__" in e2;
}
function De(e2) {
  return null === e2 || Le(e2) || "object" != typeof e2 && "function" != typeof e2;
}
function Ne(e2) {
  return Ae(e2, "Object");
}
function Re(e2) {
  return "undefined" != typeof Event && Be(e2, Event);
}
function Fe(e2) {
  return Boolean(e2 && e2.then && "function" == typeof e2.then);
}
function Be(e2, t2) {
  try {
    return e2 instanceof t2;
  } catch (e3) {
    return false;
  }
}
function Ue(e2) {
  return !("object" != typeof e2 || null === e2 || !e2.__isVue && !e2._isVue);
}
var Ve = re;
function Je(e2, t2 = {}) {
  if (!e2) return "<unknown>";
  try {
    let n2 = e2;
    const r2 = 5, i2 = [];
    let o2 = 0, s2 = 0;
    const a2 = " > ", c2 = a2.length;
    let l2;
    const u2 = Array.isArray(t2) ? t2 : t2.keyAttrs, d2 = !Array.isArray(t2) && t2.maxStringLength || 80;
    for (; n2 && o2++ < r2 && (l2 = $e(n2, u2), !("html" === l2 || o2 > 1 && s2 + i2.length * c2 + l2.length >= d2)); ) i2.push(l2), s2 += l2.length, n2 = n2.parentNode;
    return i2.reverse().join(a2);
  } catch (e3) {
    return "<unknown>";
  }
}
function $e(e2, t2) {
  const n2 = e2, r2 = [];
  if (!n2 || !n2.tagName) return "";
  if (Ve.HTMLElement && n2 instanceof HTMLElement && n2.dataset) {
    if (n2.dataset.sentryComponent) return n2.dataset.sentryComponent;
    if (n2.dataset.sentryElement) return n2.dataset.sentryElement;
  }
  r2.push(n2.tagName.toLowerCase());
  const i2 = t2 && t2.length ? t2.filter((e3) => n2.getAttribute(e3)).map((e3) => [e3, n2.getAttribute(e3)]) : null;
  if (i2 && i2.length) i2.forEach((e3) => {
    r2.push(`[${e3[0]}="${e3[1]}"]`);
  });
  else {
    n2.id && r2.push(`#${n2.id}`);
    const e3 = n2.className;
    if (e3 && xe(e3)) {
      const t3 = e3.split(/\s+/);
      for (const e4 of t3) r2.push(`.${e4}`);
    }
  }
  const o2 = ["aria-label", "type", "name", "title", "alt"];
  for (const e3 of o2) {
    const t3 = n2.getAttribute(e3);
    t3 && r2.push(`[${e3}="${t3}"]`);
  }
  return r2.join("");
}
function qe(e2, t2 = 0) {
  return "string" != typeof e2 || 0 === t2 || e2.length <= t2 ? e2 : `${e2.slice(0, t2)}...`;
}
function ze(e2, t2) {
  if (!Array.isArray(e2)) return "";
  const n2 = [];
  for (let t3 = 0; t3 < e2.length; t3++) {
    const r2 = e2[t3];
    try {
      Ue(r2) ? n2.push("[VueViewModel]") : n2.push(String(r2));
    } catch (e3) {
      n2.push("[value cannot be serialized]");
    }
  }
  return n2.join(t2);
}
function We(e2, t2, n2 = false) {
  return !!xe(e2) && (Ae(t2, "RegExp") ? t2.test(e2) : !!xe(t2) && (n2 ? e2 === t2 : e2.includes(t2)));
}
function He(e2, t2 = [], n2 = false) {
  return t2.some((t3) => We(e2, t3, n2));
}
function Ge(e2, t2, n2) {
  if (!(t2 in e2)) return;
  const r2 = e2[t2], i2 = n2(r2);
  "function" == typeof i2 && Ke(i2, r2);
  try {
    e2[t2] = i2;
  } catch (n3) {
    oe && le.log(`Failed to replace method "${t2}" in object`, e2);
  }
}
function Qe(e2, t2, n2) {
  try {
    Object.defineProperty(e2, t2, { value: n2, writable: true, configurable: true });
  } catch (n3) {
    oe && le.log(`Failed to add non-enumerable property "${t2}" to object`, e2);
  }
}
function Ke(e2, t2) {
  try {
    const n2 = t2.prototype || {};
    e2.prototype = t2.prototype = n2, Qe(e2, "__sentry_original__", t2);
  } catch (e3) {
  }
}
function Ye(e2) {
  return e2.__sentry_original__;
}
function Xe(e2) {
  if (Pe(e2)) return { message: e2.message, name: e2.name, stack: e2.stack, ...et(e2) };
  if (Re(e2)) {
    const t2 = { type: e2.type, target: Ze(e2.target), currentTarget: Ze(e2.currentTarget), ...et(e2) };
    return "undefined" != typeof CustomEvent && Be(e2, CustomEvent) && (t2.detail = e2.detail), t2;
  }
  return e2;
}
function Ze(e2) {
  try {
    return t2 = e2, "undefined" != typeof Element && Be(t2, Element) ? Je(e2) : Object.prototype.toString.call(e2);
  } catch (e3) {
    return "<unknown>";
  }
  var t2;
}
function et(e2) {
  if ("object" == typeof e2 && null !== e2) {
    const t2 = {};
    for (const n2 in e2) Object.prototype.hasOwnProperty.call(e2, n2) && (t2[n2] = e2[n2]);
    return t2;
  }
  return {};
}
function tt(e2) {
  return nt(e2, /* @__PURE__ */ new Map());
}
function nt(e2, t2) {
  if (function(e3) {
    if (!Ne(e3)) return false;
    try {
      const t3 = Object.getPrototypeOf(e3).constructor.name;
      return !t3 || "Object" === t3;
    } catch (e4) {
      return true;
    }
  }(e2)) {
    const n2 = t2.get(e2);
    if (void 0 !== n2) return n2;
    const r2 = {};
    t2.set(e2, r2);
    for (const n3 of Object.getOwnPropertyNames(e2)) void 0 !== e2[n3] && (r2[n3] = nt(e2[n3], t2));
    return r2;
  }
  if (Array.isArray(e2)) {
    const n2 = t2.get(e2);
    if (void 0 !== n2) return n2;
    const r2 = [];
    return t2.set(e2, r2), e2.forEach((e3) => {
      r2.push(nt(e3, t2));
    }), r2;
  }
  return e2;
}
function rt() {
  return Date.now() / 1e3;
}
var it = function() {
  const { performance: e2 } = re;
  if (!e2 || !e2.now) return rt;
  const t2 = Date.now() - e2.now(), n2 = null == e2.timeOrigin ? t2 : e2.timeOrigin;
  return () => (n2 + e2.now()) / 1e3;
}();
function ot() {
  const e2 = re, t2 = e2.crypto || e2.msCrypto;
  let n2 = () => 16 * Math.random();
  try {
    if (t2 && t2.randomUUID) return t2.randomUUID().replace(/-/g, "");
    t2 && t2.getRandomValues && (n2 = () => {
      const e3 = new Uint8Array(1);
      return t2.getRandomValues(e3), e3[0];
    });
  } catch (e3) {
  }
  return ("10000000100040008000" + 1e11).replace(/[018]/g, (e3) => (e3 ^ (15 & n2()) >> e3 / 4).toString(16));
}
function st(e2) {
  return e2.exception && e2.exception.values ? e2.exception.values[0] : void 0;
}
function at(e2) {
  const { message: t2, event_id: n2 } = e2;
  if (t2) return t2;
  const r2 = st(e2);
  return r2 ? r2.type && r2.value ? `${r2.type}: ${r2.value}` : r2.type || r2.value || n2 || "<unknown>" : n2 || "<unknown>";
}
function ct(e2, t2, n2) {
  const r2 = e2.exception = e2.exception || {}, i2 = r2.values = r2.values || [], o2 = i2[0] = i2[0] || {};
  o2.value || (o2.value = t2 || ""), o2.type || (o2.type = n2 || "Error");
}
function lt(e2, t2) {
  const n2 = st(e2);
  if (!n2) return;
  const r2 = n2.mechanism;
  if (n2.mechanism = { type: "generic", handled: true, ...r2, ...t2 }, t2 && "data" in t2) {
    const e3 = { ...r2 && r2.data, ...t2.data };
    n2.mechanism.data = e3;
  }
}
function ut(e2) {
  if (function(e3) {
    try {
      return e3.__sentry_captured__;
    } catch (e4) {
    }
  }(e2)) return true;
  try {
    Qe(e2, "__sentry_captured__", true);
  } catch (e3) {
  }
  return false;
}
var dt;
function pt(e2) {
  return new ft((t2) => {
    t2(e2);
  });
}
function ht(e2) {
  return new ft((t2, n2) => {
    n2(e2);
  });
}
(() => {
  const { performance: e2 } = re;
  if (!e2 || !e2.now) return;
  const t2 = 36e5, n2 = e2.now(), r2 = Date.now(), i2 = e2.timeOrigin ? Math.abs(e2.timeOrigin + n2 - r2) : t2, o2 = i2 < t2, s2 = e2.timing && e2.timing.navigationStart, a2 = "number" == typeof s2 ? Math.abs(s2 + n2 - r2) : t2;
  (o2 || a2 < t2) && (i2 <= a2 && e2.timeOrigin);
})(), function(e2) {
  e2[e2.PENDING = 0] = "PENDING";
  e2[e2.RESOLVED = 1] = "RESOLVED";
  e2[e2.REJECTED = 2] = "REJECTED";
}(dt || (dt = {}));
var ft = class _ft {
  constructor(e2) {
    _ft.prototype.__init.call(this), _ft.prototype.__init2.call(this), _ft.prototype.__init3.call(this), _ft.prototype.__init4.call(this), this._state = dt.PENDING, this._handlers = [];
    try {
      e2(this._resolve, this._reject);
    } catch (e3) {
      this._reject(e3);
    }
  }
  then(e2, t2) {
    return new _ft((n2, r2) => {
      this._handlers.push([false, (t3) => {
        if (e2) try {
          n2(e2(t3));
        } catch (e3) {
          r2(e3);
        }
        else n2(t3);
      }, (e3) => {
        if (t2) try {
          n2(t2(e3));
        } catch (e4) {
          r2(e4);
        }
        else r2(e3);
      }]), this._executeHandlers();
    });
  }
  catch(e2) {
    return this.then((e3) => e3, e2);
  }
  finally(e2) {
    return new _ft((t2, n2) => {
      let r2, i2;
      return this.then((t3) => {
        i2 = false, r2 = t3, e2 && e2();
      }, (t3) => {
        i2 = true, r2 = t3, e2 && e2();
      }).then(() => {
        i2 ? n2(r2) : t2(r2);
      });
    });
  }
  __init() {
    this._resolve = (e2) => {
      this._setResult(dt.RESOLVED, e2);
    };
  }
  __init2() {
    this._reject = (e2) => {
      this._setResult(dt.REJECTED, e2);
    };
  }
  __init3() {
    this._setResult = (e2, t2) => {
      this._state === dt.PENDING && (Fe(t2) ? t2.then(this._resolve, this._reject) : (this._state = e2, this._value = t2, this._executeHandlers()));
    };
  }
  __init4() {
    this._executeHandlers = () => {
      if (this._state === dt.PENDING) return;
      const e2 = this._handlers.slice();
      this._handlers = [], e2.forEach((e3) => {
        e3[0] || (this._state === dt.RESOLVED && e3[1](this._value), this._state === dt.REJECTED && e3[2](this._value), e3[0] = true);
      });
    };
  }
};
function vt(e2) {
  const t2 = it(), n2 = { sid: ot(), init: true, timestamp: t2, started: t2, duration: 0, status: "ok", errors: 0, ignoreDuration: false, toJSON: () => function(e3) {
    return tt({ sid: `${e3.sid}`, init: e3.init, started: new Date(1e3 * e3.started).toISOString(), timestamp: new Date(1e3 * e3.timestamp).toISOString(), status: e3.status, errors: e3.errors, did: "number" == typeof e3.did || "string" == typeof e3.did ? `${e3.did}` : void 0, duration: e3.duration, abnormal_mechanism: e3.abnormal_mechanism, attrs: { release: e3.release, environment: e3.environment, ip_address: e3.ipAddress, user_agent: e3.userAgent } });
  }(n2) };
  return e2 && gt(n2, e2), n2;
}
function gt(e2, t2 = {}) {
  if (t2.user && (!e2.ipAddress && t2.user.ip_address && (e2.ipAddress = t2.user.ip_address), e2.did || t2.did || (e2.did = t2.user.id || t2.user.email || t2.user.username)), e2.timestamp = t2.timestamp || it(), t2.abnormal_mechanism && (e2.abnormal_mechanism = t2.abnormal_mechanism), t2.ignoreDuration && (e2.ignoreDuration = t2.ignoreDuration), t2.sid && (e2.sid = 32 === t2.sid.length ? t2.sid : ot()), void 0 !== t2.init && (e2.init = t2.init), !e2.did && t2.did && (e2.did = `${t2.did}`), "number" == typeof t2.started && (e2.started = t2.started), e2.ignoreDuration) e2.duration = void 0;
  else if ("number" == typeof t2.duration) e2.duration = t2.duration;
  else {
    const t3 = e2.timestamp - e2.started;
    e2.duration = t3 >= 0 ? t3 : 0;
  }
  t2.release && (e2.release = t2.release), t2.environment && (e2.environment = t2.environment), !e2.ipAddress && t2.ipAddress && (e2.ipAddress = t2.ipAddress), !e2.userAgent && t2.userAgent && (e2.userAgent = t2.userAgent), "number" == typeof t2.errors && (e2.errors = t2.errors), t2.status && (e2.status = t2.status);
}
function mt() {
  return ot();
}
function yt() {
  return ot().substring(16);
}
function bt(e2, t2, n2 = 2) {
  if (!t2 || "object" != typeof t2 || n2 <= 0) return t2;
  if (e2 && t2 && 0 === Object.keys(t2).length) return e2;
  const r2 = { ...e2 };
  for (const e3 in t2) Object.prototype.hasOwnProperty.call(t2, e3) && (r2[e3] = bt(r2[e3], t2[e3], n2 - 1));
  return r2;
}
var _t = "_sentrySpan";
function wt(e2, t2) {
  t2 ? Qe(e2, _t, t2) : delete e2[_t];
}
function St(e2) {
  return e2[_t];
}
var kt = class _kt {
  constructor() {
    this._notifyingListeners = false, this._scopeListeners = [], this._eventProcessors = [], this._breadcrumbs = [], this._attachments = [], this._user = {}, this._tags = {}, this._extra = {}, this._contexts = {}, this._sdkProcessingMetadata = {}, this._propagationContext = { traceId: mt(), spanId: yt() };
  }
  clone() {
    const e2 = new _kt();
    return e2._breadcrumbs = [...this._breadcrumbs], e2._tags = { ...this._tags }, e2._extra = { ...this._extra }, e2._contexts = { ...this._contexts }, this._contexts.flags && (e2._contexts.flags = { values: [...this._contexts.flags.values] }), e2._user = this._user, e2._level = this._level, e2._session = this._session, e2._transactionName = this._transactionName, e2._fingerprint = this._fingerprint, e2._eventProcessors = [...this._eventProcessors], e2._requestSession = this._requestSession, e2._attachments = [...this._attachments], e2._sdkProcessingMetadata = { ...this._sdkProcessingMetadata }, e2._propagationContext = { ...this._propagationContext }, e2._client = this._client, e2._lastEventId = this._lastEventId, wt(e2, St(this)), e2;
  }
  setClient(e2) {
    this._client = e2;
  }
  setLastEventId(e2) {
    this._lastEventId = e2;
  }
  getClient() {
    return this._client;
  }
  lastEventId() {
    return this._lastEventId;
  }
  addScopeListener(e2) {
    this._scopeListeners.push(e2);
  }
  addEventProcessor(e2) {
    return this._eventProcessors.push(e2), this;
  }
  setUser(e2) {
    return this._user = e2 || { email: void 0, id: void 0, ip_address: void 0, username: void 0 }, this._session && gt(this._session, { user: e2 }), this._notifyScopeListeners(), this;
  }
  getUser() {
    return this._user;
  }
  getRequestSession() {
    return this._requestSession;
  }
  setRequestSession(e2) {
    return this._requestSession = e2, this;
  }
  setTags(e2) {
    return this._tags = { ...this._tags, ...e2 }, this._notifyScopeListeners(), this;
  }
  setTag(e2, t2) {
    return this._tags = { ...this._tags, [e2]: t2 }, this._notifyScopeListeners(), this;
  }
  setExtras(e2) {
    return this._extra = { ...this._extra, ...e2 }, this._notifyScopeListeners(), this;
  }
  setExtra(e2, t2) {
    return this._extra = { ...this._extra, [e2]: t2 }, this._notifyScopeListeners(), this;
  }
  setFingerprint(e2) {
    return this._fingerprint = e2, this._notifyScopeListeners(), this;
  }
  setLevel(e2) {
    return this._level = e2, this._notifyScopeListeners(), this;
  }
  setTransactionName(e2) {
    return this._transactionName = e2, this._notifyScopeListeners(), this;
  }
  setContext(e2, t2) {
    return null === t2 ? delete this._contexts[e2] : this._contexts[e2] = t2, this._notifyScopeListeners(), this;
  }
  setSession(e2) {
    return e2 ? this._session = e2 : delete this._session, this._notifyScopeListeners(), this;
  }
  getSession() {
    return this._session;
  }
  update(e2) {
    if (!e2) return this;
    const t2 = "function" == typeof e2 ? e2(this) : e2, [n2, r2] = t2 instanceof Mt ? [t2.getScopeData(), t2.getRequestSession()] : Ne(t2) ? [e2, e2.requestSession] : [], { tags: i2, extra: o2, user: s2, contexts: a2, level: c2, fingerprint: l2 = [], propagationContext: u2 } = n2 || {};
    return this._tags = { ...this._tags, ...i2 }, this._extra = { ...this._extra, ...o2 }, this._contexts = { ...this._contexts, ...a2 }, s2 && Object.keys(s2).length && (this._user = s2), c2 && (this._level = c2), l2.length && (this._fingerprint = l2), u2 && (this._propagationContext = u2), r2 && (this._requestSession = r2), this;
  }
  clear() {
    return this._breadcrumbs = [], this._tags = {}, this._extra = {}, this._user = {}, this._contexts = {}, this._level = void 0, this._transactionName = void 0, this._fingerprint = void 0, this._requestSession = void 0, this._session = void 0, wt(this, void 0), this._attachments = [], this.setPropagationContext({ traceId: mt() }), this._notifyScopeListeners(), this;
  }
  addBreadcrumb(e2, t2) {
    const n2 = "number" == typeof t2 ? t2 : 100;
    if (n2 <= 0) return this;
    const r2 = { timestamp: rt(), ...e2 };
    return this._breadcrumbs.push(r2), this._breadcrumbs.length > n2 && (this._breadcrumbs = this._breadcrumbs.slice(-n2), this._client && this._client.recordDroppedEvent("buffer_overflow", "log_item")), this._notifyScopeListeners(), this;
  }
  getLastBreadcrumb() {
    return this._breadcrumbs[this._breadcrumbs.length - 1];
  }
  clearBreadcrumbs() {
    return this._breadcrumbs = [], this._notifyScopeListeners(), this;
  }
  addAttachment(e2) {
    return this._attachments.push(e2), this;
  }
  clearAttachments() {
    return this._attachments = [], this;
  }
  getScopeData() {
    return { breadcrumbs: this._breadcrumbs, attachments: this._attachments, contexts: this._contexts, tags: this._tags, extra: this._extra, user: this._user, level: this._level, fingerprint: this._fingerprint || [], eventProcessors: this._eventProcessors, propagationContext: this._propagationContext, sdkProcessingMetadata: this._sdkProcessingMetadata, transactionName: this._transactionName, span: St(this) };
  }
  setSDKProcessingMetadata(e2) {
    return this._sdkProcessingMetadata = bt(this._sdkProcessingMetadata, e2, 2), this;
  }
  setPropagationContext(e2) {
    return this._propagationContext = { spanId: yt(), ...e2 }, this;
  }
  getPropagationContext() {
    return this._propagationContext;
  }
  captureException(e2, t2) {
    const n2 = t2 && t2.event_id ? t2.event_id : ot();
    if (!this._client) return le.warn("No client configured on scope - will not capture exception!"), n2;
    const r2 = new Error("Sentry syntheticException");
    return this._client.captureException(e2, { originalException: e2, syntheticException: r2, ...t2, event_id: n2 }, this), n2;
  }
  captureMessage(e2, t2, n2) {
    const r2 = n2 && n2.event_id ? n2.event_id : ot();
    if (!this._client) return le.warn("No client configured on scope - will not capture message!"), r2;
    const i2 = new Error(e2);
    return this._client.captureMessage(e2, t2, { originalException: e2, syntheticException: i2, ...n2, event_id: r2 }, this), r2;
  }
  captureEvent(e2, t2) {
    const n2 = t2 && t2.event_id ? t2.event_id : ot();
    return this._client ? (this._client.captureEvent(e2, { ...t2, event_id: n2 }, this), n2) : (le.warn("No client configured on scope - will not capture event!"), n2);
  }
  _notifyScopeListeners() {
    this._notifyingListeners || (this._notifyingListeners = true, this._scopeListeners.forEach((e2) => {
      e2(this);
    }), this._notifyingListeners = false);
  }
};
var Mt = kt;
var Ct = class {
  constructor(e2, t2) {
    let n2, r2;
    n2 = e2 || new Mt(), r2 = t2 || new Mt(), this._stack = [{ scope: n2 }], this._isolationScope = r2;
  }
  withScope(e2) {
    const t2 = this._pushScope();
    let n2;
    try {
      n2 = e2(t2);
    } catch (e3) {
      throw this._popScope(), e3;
    }
    return Fe(n2) ? n2.then((e3) => (this._popScope(), e3), (e3) => {
      throw this._popScope(), e3;
    }) : (this._popScope(), n2);
  }
  getClient() {
    return this.getStackTop().client;
  }
  getScope() {
    return this.getStackTop().scope;
  }
  getIsolationScope() {
    return this._isolationScope;
  }
  getStackTop() {
    return this._stack[this._stack.length - 1];
  }
  _pushScope() {
    const e2 = this.getScope().clone();
    return this._stack.push({ client: this.getClient(), scope: e2 }), e2;
  }
  _popScope() {
    return !(this._stack.length <= 1) && !!this._stack.pop();
  }
};
function Et() {
  const e2 = Te(Ee());
  return e2.stack = e2.stack || new Ct(ie("defaultCurrentScope", () => new Mt()), ie("defaultIsolationScope", () => new Mt()));
}
function Tt(e2) {
  return Et().withScope(e2);
}
function Ot(e2, t2) {
  const n2 = Et();
  return n2.withScope(() => (n2.getStackTop().scope = e2, t2(e2)));
}
function Pt(e2) {
  return Et().withScope(() => e2(Et().getIsolationScope()));
}
function At(e2) {
  const t2 = Te(e2);
  return t2.acs ? t2.acs : { withIsolationScope: Pt, withScope: Tt, withSetScope: Ot, withSetIsolationScope: (e3, t3) => Pt(t3), getCurrentScope: () => Et().getScope(), getIsolationScope: () => Et().getIsolationScope() };
}
function jt() {
  return At(Ee()).getCurrentScope();
}
function It() {
  return At(Ee()).getIsolationScope();
}
function xt() {
  return jt().getClient();
}
function Lt(e2) {
  const t2 = e2.getPropagationContext(), { traceId: n2, spanId: r2, parentSpanId: i2 } = t2;
  return tt({ trace_id: n2, span_id: r2, parent_span_id: i2 });
}
function Dt(e2) {
  const t2 = e2._sentryMetrics;
  if (!t2) return;
  const n2 = {};
  for (const [, [e3, r2]] of t2) {
    (n2[e3] || (n2[e3] = [])).push(tt(r2));
  }
  return n2;
}
var Nt = /^sentry-/;
function Rt(e2) {
  const t2 = function(e3) {
    if (!e3 || !xe(e3) && !Array.isArray(e3)) return;
    if (Array.isArray(e3)) return e3.reduce((e4, t3) => {
      const n3 = Ft(t3);
      return Object.entries(n3).forEach(([t4, n4]) => {
        e4[t4] = n4;
      }), e4;
    }, {});
    return Ft(e3);
  }(e2);
  if (!t2) return;
  const n2 = Object.entries(t2).reduce((e3, [t3, n3]) => {
    if (t3.match(Nt)) {
      e3[t3.slice(7)] = n3;
    }
    return e3;
  }, {});
  return Object.keys(n2).length > 0 ? n2 : void 0;
}
function Ft(e2) {
  return e2.split(",").map((e3) => e3.split("=").map((e4) => decodeURIComponent(e4.trim()))).reduce((e3, [t2, n2]) => (t2 && n2 && (e3[t2] = n2), e3), {});
}
var Bt = false;
function Ut(e2) {
  const { spanId: t2, traceId: n2, isRemote: r2 } = e2.spanContext();
  return tt({ parent_span_id: r2 ? t2 : $t(e2).parent_span_id, span_id: r2 ? yt() : t2, trace_id: n2 });
}
function Vt(e2) {
  return "number" == typeof e2 ? Jt(e2) : Array.isArray(e2) ? e2[0] + e2[1] / 1e9 : e2 instanceof Date ? Jt(e2.getTime()) : it();
}
function Jt(e2) {
  return e2 > 9999999999 ? e2 / 1e3 : e2;
}
function $t(e2) {
  if (function(e3) {
    return "function" == typeof e3.getSpanJSON;
  }(e2)) return e2.getSpanJSON();
  try {
    const { spanId: t2, traceId: n2 } = e2.spanContext();
    if (function(e3) {
      const t3 = e3;
      return !!(t3.attributes && t3.startTime && t3.name && t3.endTime && t3.status);
    }(e2)) {
      const { attributes: r2, startTime: i2, name: o2, endTime: s2, parentSpanId: a2, status: c2 } = e2;
      return tt({ span_id: t2, trace_id: n2, data: r2, description: o2, parent_span_id: a2, start_timestamp: Vt(i2), timestamp: Vt(s2) || void 0, status: qt(c2), op: r2["sentry.op"], origin: r2["sentry.origin"], _metrics_summary: Dt(e2) });
    }
    return { span_id: t2, trace_id: n2 };
  } catch (e3) {
    return {};
  }
}
function qt(e2) {
  if (e2 && 0 !== e2.code) return 1 === e2.code ? "ok" : e2.message || "unknown_error";
}
function zt(e2) {
  return e2._sentryRootSpan || e2;
}
function Wt() {
  Bt || (ce(() => {
    console.warn("[Sentry] Deprecation warning: Returning null from `beforeSendSpan` will be disallowed from SDK version 9.0.0 onwards. The callback will only support mutating spans. To drop certain spans, configure the respective integrations directly.");
  }), Bt = true);
}
var Ht = "production";
function Gt(e2, t2) {
  const n2 = t2.getOptions(), { publicKey: r2 } = t2.getDsn() || {}, i2 = tt({ environment: n2.environment || Ht, release: n2.release, public_key: r2, trace_id: e2 });
  return t2.emit("createDsc", i2), i2;
}
function Qt(e2) {
  const t2 = xt();
  if (!t2) return {};
  const n2 = zt(e2), r2 = n2._frozenDsc;
  if (r2) return r2;
  const i2 = n2.spanContext().traceState, o2 = i2 && i2.get("sentry.dsc"), s2 = o2 && Rt(o2);
  if (s2) return s2;
  const a2 = Gt(e2.spanContext().traceId, t2), c2 = $t(n2), l2 = c2.data || {}, u2 = l2["sentry.sample_rate"];
  null != u2 && (a2.sample_rate = `${u2}`);
  const d2 = l2["sentry.source"], p2 = c2.description;
  return "url" !== d2 && p2 && (a2.transaction = p2), function(e3) {
    if ("boolean" == typeof __SENTRY_TRACING__ && !__SENTRY_TRACING__) return false;
    const t3 = xt(), n3 = e3 || t3 && t3.getOptions();
    return !!n3 && (n3.enableTracing || "tracesSampleRate" in n3 || "tracesSampler" in n3);
  }() && (a2.sampled = String(function(e3) {
    const { traceFlags: t3 } = e3.spanContext();
    return 1 === t3;
  }(n2))), t2.emit("createDsc", a2, n2), a2;
}
var Kt = /^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;
function Yt(e2, t2 = false) {
  const { host: n2, path: r2, pass: i2, port: o2, projectId: s2, protocol: a2, publicKey: c2 } = e2;
  return `${a2}://${c2}${t2 && i2 ? `:${i2}` : ""}@${n2}${o2 ? `:${o2}` : ""}/${r2 ? `${r2}/` : r2}${s2}`;
}
function Xt(e2) {
  return { protocol: e2.protocol, publicKey: e2.publicKey || "", pass: e2.pass || "", host: e2.host, port: e2.port || "", path: e2.path || "", projectId: e2.projectId };
}
function Zt(e2) {
  const t2 = "string" == typeof e2 ? function(e3) {
    const t3 = Kt.exec(e3);
    if (!t3) return void ce(() => {
      console.error(`Invalid Sentry Dsn: ${e3}`);
    });
    const [n2, r2, i2 = "", o2 = "", s2 = "", a2 = ""] = t3.slice(1);
    let c2 = "", l2 = a2;
    const u2 = l2.split("/");
    if (u2.length > 1 && (c2 = u2.slice(0, -1).join("/"), l2 = u2.pop()), l2) {
      const e4 = l2.match(/^\d+/);
      e4 && (l2 = e4[0]);
    }
    return Xt({ host: o2, pass: i2, path: c2, projectId: l2, port: s2, protocol: n2, publicKey: r2 });
  }(e2) : Xt(e2);
  if (t2 && function(e3) {
    if (!oe) return true;
    const { port: t3, projectId: n2, protocol: r2 } = e3;
    return !(["protocol", "publicKey", "host", "projectId"].find((t4) => !e3[t4] && (le.error(`Invalid Sentry Dsn: ${t4} missing`), true)) || (n2.match(/^\d+$/) ? /* @__PURE__ */ function(e4) {
      return "http" === e4 || "https" === e4;
    }(r2) ? t3 && isNaN(parseInt(t3, 10)) && (le.error(`Invalid Sentry Dsn: Invalid port ${t3}`), 1) : (le.error(`Invalid Sentry Dsn: Invalid protocol ${r2}`), 1) : (le.error(`Invalid Sentry Dsn: Invalid projectId ${n2}`), 1)));
  }(t2)) return t2;
}
function en(e2, t2 = 100, n2 = 1 / 0) {
  try {
    return nn("", e2, t2, n2);
  } catch (e3) {
    return { ERROR: `**non-serializable** (${e3})` };
  }
}
function tn(e2, t2 = 3, n2 = 102400) {
  const r2 = en(e2, t2);
  return i2 = r2, function(e3) {
    return ~-encodeURI(e3).split(/%..|./).length;
  }(JSON.stringify(i2)) > n2 ? tn(e2, t2 - 1, n2) : r2;
  var i2;
}
function nn(e2, t2, n2 = 1 / 0, r2 = 1 / 0, i2 = /* @__PURE__ */ function() {
  const e3 = "function" == typeof WeakSet, t3 = e3 ? /* @__PURE__ */ new WeakSet() : [];
  return [function(n3) {
    if (e3) return !!t3.has(n3) || (t3.add(n3), false);
    for (let e4 = 0; e4 < t3.length; e4++) if (t3[e4] === n3) return true;
    return t3.push(n3), false;
  }, function(n3) {
    if (e3) t3.delete(n3);
    else for (let e4 = 0; e4 < t3.length; e4++) if (t3[e4] === n3) {
      t3.splice(e4, 1);
      break;
    }
  }];
}()) {
  const [o2, s2] = i2;
  if (null == t2 || ["boolean", "string"].includes(typeof t2) || "number" == typeof t2 && Number.isFinite(t2)) return t2;
  const a2 = function(e3, t3) {
    try {
      if ("domain" === e3 && t3 && "object" == typeof t3 && t3._events) return "[Domain]";
      if ("domainEmitter" === e3) return "[DomainEmitter]";
      if ("undefined" != typeof global && t3 === global) return "[Global]";
      if ("undefined" != typeof window && t3 === window) return "[Window]";
      if ("undefined" != typeof document && t3 === document) return "[Document]";
      if (Ue(t3)) return "[VueViewModel]";
      if (Ne(n3 = t3) && "nativeEvent" in n3 && "preventDefault" in n3 && "stopPropagation" in n3) return "[SyntheticEvent]";
      if ("number" == typeof t3 && !Number.isFinite(t3)) return `[${t3}]`;
      if ("function" == typeof t3) return `[Function: ${ve(t3)}]`;
      if ("symbol" == typeof t3) return `[${String(t3)}]`;
      if ("bigint" == typeof t3) return `[BigInt: ${String(t3)}]`;
      const r3 = function(e4) {
        const t4 = Object.getPrototypeOf(e4);
        return t4 ? t4.constructor.name : "null prototype";
      }(t3);
      return /^HTML(\w*)Element$/.test(r3) ? `[HTMLElement: ${r3}]` : `[object ${r3}]`;
    } catch (e4) {
      return `**non-serializable** (${e4})`;
    }
    var n3;
  }(e2, t2);
  if (!a2.startsWith("[object ")) return a2;
  if (t2.__sentry_skip_normalization__) return t2;
  const c2 = "number" == typeof t2.__sentry_override_normalization_depth__ ? t2.__sentry_override_normalization_depth__ : n2;
  if (0 === c2) return a2.replace("object ", "");
  if (o2(t2)) return "[Circular ~]";
  const l2 = t2;
  if (l2 && "function" == typeof l2.toJSON) try {
    return nn("", l2.toJSON(), c2 - 1, r2, i2);
  } catch (e3) {
  }
  const u2 = Array.isArray(t2) ? [] : {};
  let d2 = 0;
  const p2 = Xe(t2);
  for (const e3 in p2) {
    if (!Object.prototype.hasOwnProperty.call(p2, e3)) continue;
    if (d2 >= r2) {
      u2[e3] = "[MaxProperties ~]";
      break;
    }
    const t3 = p2[e3];
    u2[e3] = nn(e3, t3, c2 - 1, r2, i2), d2++;
  }
  return s2(t2), u2;
}
function rn(e2, t2 = []) {
  return [e2, t2];
}
function on(e2, t2) {
  const [n2, r2] = e2;
  return [n2, [...r2, t2]];
}
function sn(e2, t2) {
  const n2 = e2[1];
  for (const e3 of n2) {
    if (t2(e3, e3[0].type)) return true;
  }
  return false;
}
function an(e2) {
  return re.__SENTRY__ && re.__SENTRY__.encodePolyfill ? re.__SENTRY__.encodePolyfill(e2) : new TextEncoder().encode(e2);
}
function cn(e2) {
  const [t2, n2] = e2;
  let r2 = JSON.stringify(t2);
  function i2(e3) {
    "string" == typeof r2 ? r2 = "string" == typeof e3 ? r2 + e3 : [an(r2), e3] : r2.push("string" == typeof e3 ? an(e3) : e3);
  }
  for (const e3 of n2) {
    const [t3, n3] = e3;
    if (i2(`
${JSON.stringify(t3)}
`), "string" == typeof n3 || n3 instanceof Uint8Array) i2(n3);
    else {
      let e4;
      try {
        e4 = JSON.stringify(n3);
      } catch (t4) {
        e4 = JSON.stringify(en(n3));
      }
      i2(e4);
    }
  }
  return "string" == typeof r2 ? r2 : function(e3) {
    const t3 = e3.reduce((e4, t4) => e4 + t4.length, 0), n3 = new Uint8Array(t3);
    let r3 = 0;
    for (const t4 of e3) n3.set(t4, r3), r3 += t4.length;
    return n3;
  }(r2);
}
function ln(e2) {
  const t2 = "string" == typeof e2.data ? an(e2.data) : e2.data;
  return [tt({ type: "attachment", length: t2.length, filename: e2.filename, content_type: e2.contentType, attachment_type: e2.attachmentType }), t2];
}
var un = { session: "session", sessions: "session", attachment: "attachment", transaction: "transaction", event: "error", client_report: "internal", user_report: "default", profile: "profile", profile_chunk: "profile", replay_event: "replay", replay_recording: "replay", check_in: "monitor", feedback: "feedback", span: "span", statsd: "metric_bucket", raw_security: "security" };
function dn(e2) {
  return un[e2];
}
function pn(e2) {
  if (!e2 || !e2.sdk) return;
  const { name: t2, version: n2 } = e2.sdk;
  return { name: t2, version: n2 };
}
function hn(e2, t2, n2, r2) {
  const i2 = pn(n2), o2 = e2.type && "replay_event" !== e2.type ? e2.type : "event";
  !function(e3, t3) {
    t3 && (e3.sdk = e3.sdk || {}, e3.sdk.name = e3.sdk.name || t3.name, e3.sdk.version = e3.sdk.version || t3.version, e3.sdk.integrations = [...e3.sdk.integrations || [], ...t3.integrations || []], e3.sdk.packages = [...e3.sdk.packages || [], ...t3.packages || []]);
  }(e2, n2 && n2.sdk);
  const s2 = function(e3, t3, n3, r3) {
    const i3 = e3.sdkProcessingMetadata && e3.sdkProcessingMetadata.dynamicSamplingContext;
    return { event_id: e3.event_id, sent_at: (/* @__PURE__ */ new Date()).toISOString(), ...t3 && { sdk: t3 }, ...!!n3 && r3 && { dsn: Yt(r3) }, ...i3 && { trace: tt({ ...i3 }) } };
  }(e2, i2, r2, t2);
  delete e2.sdkProcessingMetadata;
  return rn(s2, [[{ type: o2 }, e2]]);
}
function fn(e2, t2, n2, r2 = 0) {
  return new ft((i2, o2) => {
    const s2 = e2[r2];
    if (null === t2 || "function" != typeof s2) i2(t2);
    else {
      const a2 = s2({ ...t2 }, n2);
      te && s2.id && null === a2 && le.log(`Event processor "${s2.id}" dropped event`), Fe(a2) ? a2.then((t3) => fn(e2, t3, n2, r2 + 1).then(i2)).then(null, o2) : fn(e2, a2, n2, r2 + 1).then(i2).then(null, o2);
    }
  });
}
var vn;
var gn;
var mn;
function yn(e2, t2) {
  const { fingerprint: n2, span: r2, breadcrumbs: i2, sdkProcessingMetadata: o2 } = t2;
  !function(e3, t3) {
    const { extra: n3, tags: r3, user: i3, contexts: o3, level: s2, transactionName: a2 } = t3, c2 = tt(n3);
    c2 && Object.keys(c2).length && (e3.extra = { ...c2, ...e3.extra });
    const l2 = tt(r3);
    l2 && Object.keys(l2).length && (e3.tags = { ...l2, ...e3.tags });
    const u2 = tt(i3);
    u2 && Object.keys(u2).length && (e3.user = { ...u2, ...e3.user });
    const d2 = tt(o3);
    d2 && Object.keys(d2).length && (e3.contexts = { ...d2, ...e3.contexts });
    s2 && (e3.level = s2);
    a2 && "transaction" !== e3.type && (e3.transaction = a2);
  }(e2, t2), r2 && function(e3, t3) {
    e3.contexts = { trace: Ut(t3), ...e3.contexts }, e3.sdkProcessingMetadata = { dynamicSamplingContext: Qt(t3), ...e3.sdkProcessingMetadata };
    const n3 = zt(t3), r3 = $t(n3).description;
    r3 && !e3.transaction && "transaction" === e3.type && (e3.transaction = r3);
  }(e2, r2), function(e3, t3) {
    e3.fingerprint = e3.fingerprint ? Array.isArray(e3.fingerprint) ? e3.fingerprint : [e3.fingerprint] : [], t3 && (e3.fingerprint = e3.fingerprint.concat(t3));
    e3.fingerprint && !e3.fingerprint.length && delete e3.fingerprint;
  }(e2, n2), function(e3, t3) {
    const n3 = [...e3.breadcrumbs || [], ...t3];
    e3.breadcrumbs = n3.length ? n3 : void 0;
  }(e2, i2), function(e3, t3) {
    e3.sdkProcessingMetadata = { ...e3.sdkProcessingMetadata, ...t3 };
  }(e2, o2);
}
function bn(e2, t2) {
  const { extra: n2, tags: r2, user: i2, contexts: o2, level: s2, sdkProcessingMetadata: a2, breadcrumbs: c2, fingerprint: l2, eventProcessors: u2, attachments: d2, propagationContext: p2, transactionName: h2, span: f2 } = t2;
  _n(e2, "extra", n2), _n(e2, "tags", r2), _n(e2, "user", i2), _n(e2, "contexts", o2), e2.sdkProcessingMetadata = bt(e2.sdkProcessingMetadata, a2, 2), s2 && (e2.level = s2), h2 && (e2.transactionName = h2), f2 && (e2.span = f2), c2.length && (e2.breadcrumbs = [...e2.breadcrumbs, ...c2]), l2.length && (e2.fingerprint = [...e2.fingerprint, ...l2]), u2.length && (e2.eventProcessors = [...e2.eventProcessors, ...u2]), d2.length && (e2.attachments = [...e2.attachments, ...d2]), e2.propagationContext = { ...e2.propagationContext, ...p2 };
}
function _n(e2, t2, n2) {
  e2[t2] = bt(e2[t2], n2, 1);
}
function wn(e2, t2, n2, r2, i2, o2) {
  const { normalizeDepth: s2 = 3, normalizeMaxBreadth: a2 = 1e3 } = e2, c2 = { ...t2, event_id: t2.event_id || n2.event_id || ot(), timestamp: t2.timestamp || rt() }, l2 = n2.integrations || e2.integrations.map((e3) => e3.name);
  !function(e3, t3) {
    const { environment: n3, release: r3, dist: i3, maxValueLength: o3 = 250 } = t3;
    e3.environment = e3.environment || n3 || Ht, !e3.release && r3 && (e3.release = r3);
    !e3.dist && i3 && (e3.dist = i3);
    e3.message && (e3.message = qe(e3.message, o3));
    const s3 = e3.exception && e3.exception.values && e3.exception.values[0];
    s3 && s3.value && (s3.value = qe(s3.value, o3));
    const a3 = e3.request;
    a3 && a3.url && (a3.url = qe(a3.url, o3));
  }(c2, e2), function(e3, t3) {
    t3.length > 0 && (e3.sdk = e3.sdk || {}, e3.sdk.integrations = [...e3.sdk.integrations || [], ...t3]);
  }(c2, l2), i2 && i2.emit("applyFrameMetadata", t2), void 0 === t2.type && function(e3, t3) {
    const n3 = function(e4) {
      const t4 = re._sentryDebugIds;
      if (!t4) return {};
      const n4 = Object.keys(t4);
      return mn && n4.length === gn || (gn = n4.length, mn = n4.reduce((n5, r3) => {
        vn || (vn = {});
        const i3 = vn[r3];
        if (i3) n5[i3[0]] = i3[1];
        else {
          const i4 = e4(r3);
          for (let e5 = i4.length - 1; e5 >= 0; e5--) {
            const o3 = i4[e5], s3 = o3 && o3.filename, a3 = t4[r3];
            if (s3 && a3) {
              n5[s3] = a3, vn[r3] = [s3, a3];
              break;
            }
          }
        }
        return n5;
      }, {})), mn;
    }(t3);
    try {
      e3.exception.values.forEach((e4) => {
        e4.stacktrace.frames.forEach((e5) => {
          n3 && e5.filename && (e5.debug_id = n3[e5.filename]);
        });
      });
    } catch (e4) {
    }
  }(c2, e2.stackParser);
  const u2 = function(e3, t3) {
    if (!t3) return e3;
    const n3 = e3 ? e3.clone() : new Mt();
    return n3.update(t3), n3;
  }(r2, n2.captureContext);
  n2.mechanism && lt(c2, n2.mechanism);
  const d2 = i2 ? i2.getEventProcessors() : [], p2 = ie("globalScope", () => new Mt()).getScopeData();
  if (o2) {
    bn(p2, o2.getScopeData());
  }
  if (u2) {
    bn(p2, u2.getScopeData());
  }
  const h2 = [...n2.attachments || [], ...p2.attachments];
  h2.length && (n2.attachments = h2), yn(c2, p2);
  return fn([...d2, ...p2.eventProcessors], c2, n2).then((e3) => (e3 && function(e4) {
    const t3 = {};
    try {
      e4.exception.values.forEach((e5) => {
        e5.stacktrace.frames.forEach((e6) => {
          e6.debug_id && (e6.abs_path ? t3[e6.abs_path] = e6.debug_id : e6.filename && (t3[e6.filename] = e6.debug_id), delete e6.debug_id);
        });
      });
    } catch (e5) {
    }
    if (0 === Object.keys(t3).length) return;
    e4.debug_meta = e4.debug_meta || {}, e4.debug_meta.images = e4.debug_meta.images || [];
    const n3 = e4.debug_meta.images;
    Object.entries(t3).forEach(([e5, t4]) => {
      n3.push({ type: "sourcemap", code_file: e5, debug_id: t4 });
    });
  }(e3), "number" == typeof s2 && s2 > 0 ? function(e4, t3, n3) {
    if (!e4) return null;
    const r3 = { ...e4, ...e4.breadcrumbs && { breadcrumbs: e4.breadcrumbs.map((e5) => ({ ...e5, ...e5.data && { data: en(e5.data, t3, n3) } })) }, ...e4.user && { user: en(e4.user, t3, n3) }, ...e4.contexts && { contexts: en(e4.contexts, t3, n3) }, ...e4.extra && { extra: en(e4.extra, t3, n3) } };
    e4.contexts && e4.contexts.trace && r3.contexts && (r3.contexts.trace = e4.contexts.trace, e4.contexts.trace.data && (r3.contexts.trace.data = en(e4.contexts.trace.data, t3, n3)));
    e4.spans && (r3.spans = e4.spans.map((e5) => ({ ...e5, ...e5.data && { data: en(e5.data, t3, n3) } })));
    e4.contexts && e4.contexts.flags && r3.contexts && (r3.contexts.flags = en(e4.contexts.flags, 3, n3));
    return r3;
  }(e3, s2, a2) : e3));
}
function Sn(e2) {
  if (e2) return function(e3) {
    return e3 instanceof Mt || "function" == typeof e3;
  }(e2) || function(e3) {
    return Object.keys(e3).some((e4) => kn.includes(e4));
  }(e2) ? { captureContext: e2 } : e2;
}
var kn = ["user", "level", "extra", "contexts", "tags", "fingerprint", "requestSession", "propagationContext"];
function Mn(e2, t2) {
  return jt().captureEvent(e2, t2);
}
function Cn(e2) {
  const t2 = xt(), n2 = It(), r2 = jt(), { release: i2, environment: o2 = Ht } = t2 && t2.getOptions() || {}, { userAgent: s2 } = re.navigator || {}, a2 = vt({ release: i2, environment: o2, user: r2.getUser() || n2.getUser(), ...s2 && { userAgent: s2 }, ...e2 }), c2 = n2.getSession();
  return c2 && "ok" === c2.status && gt(c2, { status: "exited" }), En(), n2.setSession(a2), r2.setSession(a2), a2;
}
function En() {
  const e2 = It(), t2 = jt(), n2 = t2.getSession() || e2.getSession();
  n2 && function(e3, t3) {
    let n3 = {};
    t3 ? n3 = { status: t3 } : "ok" === e3.status && (n3 = { status: "exited" }), gt(e3, n3);
  }(n2), Tn(), e2.setSession(), t2.setSession();
}
function Tn() {
  const e2 = It(), t2 = jt(), n2 = xt(), r2 = t2.getSession() || e2.getSession();
  r2 && n2 && n2.captureSession(r2);
}
function On(e2 = false) {
  e2 ? En() : Tn();
}
function Pn(e2, t2, n2) {
  return t2 || `${function(e3) {
    return `${function(e4) {
      const t3 = e4.protocol ? `${e4.protocol}:` : "", n3 = e4.port ? `:${e4.port}` : "";
      return `${t3}//${e4.host}${n3}${e4.path ? `/${e4.path}` : ""}/api/`;
    }(e3)}${e3.projectId}/envelope/`;
  }(e2)}?${function(e3, t3) {
    const n3 = { sentry_version: "7" };
    return e3.publicKey && (n3.sentry_key = e3.publicKey), t3 && (n3.sentry_client = `${t3.name}/${t3.version}`), new URLSearchParams(n3).toString();
  }(e2, n2)}`;
}
var An = [];
function jn(e2, t2) {
  for (const n2 of t2) n2 && n2.afterAllSetup && n2.afterAllSetup(e2);
}
function In(e2, t2, n2) {
  if (n2[t2.name]) te && le.log(`Integration skipped because it was already installed: ${t2.name}`);
  else {
    if (n2[t2.name] = t2, -1 === An.indexOf(t2.name) && "function" == typeof t2.setupOnce && (t2.setupOnce(), An.push(t2.name)), t2.setup && "function" == typeof t2.setup && t2.setup(e2), "function" == typeof t2.preprocessEvent) {
      const n3 = t2.preprocessEvent.bind(t2);
      e2.on("preprocessEvent", (t3, r2) => n3(t3, r2, e2));
    }
    if ("function" == typeof t2.processEvent) {
      const n3 = t2.processEvent.bind(t2), r2 = Object.assign((t3, r3) => n3(t3, r3, e2), { id: t2.name });
      e2.addEventProcessor(r2);
    }
    te && le.log(`Integration installed: ${t2.name}`);
  }
}
var xn = class extends Error {
  constructor(e2, t2 = "warn") {
    super(e2), this.message = e2, this.logLevel = t2;
  }
};
var Ln = "Not capturing exception because it's already been captured.";
var Dn = class {
  constructor(e2) {
    if (this._options = e2, this._integrations = {}, this._numProcessing = 0, this._outcomes = {}, this._hooks = {}, this._eventProcessors = [], e2.dsn ? this._dsn = Zt(e2.dsn) : te && le.warn("No DSN provided, client will not send events."), this._dsn) {
      const t3 = Pn(this._dsn, e2.tunnel, e2._metadata ? e2._metadata.sdk : void 0);
      this._transport = e2.transport({ tunnel: this._options.tunnel, recordDroppedEvent: this.recordDroppedEvent.bind(this), ...e2.transportOptions, url: t3 });
    }
    const t2 = ["enableTracing", "tracesSampleRate", "tracesSampler"].find((t3) => t3 in e2 && null == e2[t3]);
    t2 && ce(() => {
      console.warn(`[Sentry] Deprecation warning: \`${t2}\` is set to undefined, which leads to tracing being enabled. In v9, a value of \`undefined\` will result in tracing being disabled.`);
    });
  }
  captureException(e2, t2, n2) {
    const r2 = ot();
    if (ut(e2)) return te && le.log(Ln), r2;
    const i2 = { event_id: r2, ...t2 };
    return this._process(this.eventFromException(e2, i2).then((e3) => this._captureEvent(e3, i2, n2))), i2.event_id;
  }
  captureMessage(e2, t2, n2, r2) {
    const i2 = { event_id: ot(), ...n2 }, o2 = Le(e2) ? e2 : String(e2), s2 = De(e2) ? this.eventFromMessage(o2, t2, i2) : this.eventFromException(e2, i2);
    return this._process(s2.then((e3) => this._captureEvent(e3, i2, r2))), i2.event_id;
  }
  captureEvent(e2, t2, n2) {
    const r2 = ot();
    if (t2 && t2.originalException && ut(t2.originalException)) return te && le.log(Ln), r2;
    const i2 = { event_id: r2, ...t2 }, o2 = (e2.sdkProcessingMetadata || {}).capturedSpanScope;
    return this._process(this._captureEvent(e2, i2, o2 || n2)), i2.event_id;
  }
  captureSession(e2) {
    "string" != typeof e2.release ? te && le.warn("Discarded session because of missing or non-string release") : (this.sendSession(e2), gt(e2, { init: false }));
  }
  getDsn() {
    return this._dsn;
  }
  getOptions() {
    return this._options;
  }
  getSdkMetadata() {
    return this._options._metadata;
  }
  getTransport() {
    return this._transport;
  }
  flush(e2) {
    const t2 = this._transport;
    return t2 ? (this.emit("flush"), this._isClientDoneProcessing(e2).then((n2) => t2.flush(e2).then((e3) => n2 && e3))) : pt(true);
  }
  close(e2) {
    return this.flush(e2).then((e3) => (this.getOptions().enabled = false, this.emit("close"), e3));
  }
  getEventProcessors() {
    return this._eventProcessors;
  }
  addEventProcessor(e2) {
    this._eventProcessors.push(e2);
  }
  init() {
    (this._isEnabled() || this._options.integrations.some(({ name: e2 }) => e2.startsWith("Spotlight"))) && this._setupIntegrations();
  }
  getIntegrationByName(e2) {
    return this._integrations[e2];
  }
  addIntegration(e2) {
    const t2 = this._integrations[e2.name];
    In(this, e2, this._integrations), t2 || jn(this, [e2]);
  }
  sendEvent(e2, t2 = {}) {
    this.emit("beforeSendEvent", e2, t2);
    let n2 = hn(e2, this._dsn, this._options._metadata, this._options.tunnel);
    for (const e3 of t2.attachments || []) n2 = on(n2, ln(e3));
    const r2 = this.sendEnvelope(n2);
    r2 && r2.then((t3) => this.emit("afterSendEvent", e2, t3), null);
  }
  sendSession(e2) {
    const t2 = function(e3, t3, n2, r2) {
      const i2 = pn(n2);
      return rn({ sent_at: (/* @__PURE__ */ new Date()).toISOString(), ...i2 && { sdk: i2 }, ...!!r2 && t3 && { dsn: Yt(t3) } }, ["aggregates" in e3 ? [{ type: "sessions" }, e3] : [{ type: "session" }, e3.toJSON()]]);
    }(e2, this._dsn, this._options._metadata, this._options.tunnel);
    this.sendEnvelope(t2);
  }
  recordDroppedEvent(e2, t2, n2) {
    if (this._options.sendClientReports) {
      const r2 = "number" == typeof n2 ? n2 : 1, i2 = `${e2}:${t2}`;
      te && le.log(`Recording outcome: "${i2}"${r2 > 1 ? ` (${r2} times)` : ""}`), this._outcomes[i2] = (this._outcomes[i2] || 0) + r2;
    }
  }
  on(e2, t2) {
    const n2 = this._hooks[e2] = this._hooks[e2] || [];
    return n2.push(t2), () => {
      const e3 = n2.indexOf(t2);
      e3 > -1 && n2.splice(e3, 1);
    };
  }
  emit(e2, ...t2) {
    const n2 = this._hooks[e2];
    n2 && n2.forEach((e3) => e3(...t2));
  }
  sendEnvelope(e2) {
    return this.emit("beforeEnvelope", e2), this._isEnabled() && this._transport ? this._transport.send(e2).then(null, (e3) => (te && le.error("Error while sending envelope:", e3), e3)) : (te && le.error("Transport disabled"), pt({}));
  }
  _setupIntegrations() {
    const { integrations: e2 } = this._options;
    this._integrations = function(e3, t2) {
      const n2 = {};
      return t2.forEach((t3) => {
        t3 && In(e3, t3, n2);
      }), n2;
    }(this, e2), jn(this, e2);
  }
  _updateSessionFromEvent(e2, t2) {
    let n2 = "fatal" === t2.level, r2 = false;
    const i2 = t2.exception && t2.exception.values;
    if (i2) {
      r2 = true;
      for (const e3 of i2) {
        const t3 = e3.mechanism;
        if (t3 && false === t3.handled) {
          n2 = true;
          break;
        }
      }
    }
    const o2 = "ok" === e2.status;
    (o2 && 0 === e2.errors || o2 && n2) && (gt(e2, { ...n2 && { status: "crashed" }, errors: e2.errors || Number(r2 || n2) }), this.captureSession(e2));
  }
  _isClientDoneProcessing(e2) {
    return new ft((t2) => {
      let n2 = 0;
      const r2 = setInterval(() => {
        0 == this._numProcessing ? (clearInterval(r2), t2(true)) : (n2 += 1, e2 && n2 >= e2 && (clearInterval(r2), t2(false)));
      }, 1);
    });
  }
  _isEnabled() {
    return false !== this.getOptions().enabled && void 0 !== this._transport;
  }
  _prepareEvent(e2, t2, n2 = jt(), r2 = It()) {
    const i2 = this.getOptions(), o2 = Object.keys(this._integrations);
    return !t2.integrations && o2.length > 0 && (t2.integrations = o2), this.emit("preprocessEvent", e2, t2), e2.type || r2.setLastEventId(e2.event_id || t2.event_id), wn(i2, e2, t2, n2, this, r2).then((e3) => {
      if (null === e3) return e3;
      e3.contexts = { trace: Lt(n2), ...e3.contexts };
      const t3 = function(e4, t4) {
        const n3 = t4.getPropagationContext();
        return n3.dsc || Gt(n3.traceId, e4);
      }(this, n2);
      return e3.sdkProcessingMetadata = { dynamicSamplingContext: t3, ...e3.sdkProcessingMetadata }, e3;
    });
  }
  _captureEvent(e2, t2 = {}, n2) {
    return this._processEvent(e2, t2, n2).then((e3) => e3.event_id, (e3) => {
      te && (e3 instanceof xn && "log" === e3.logLevel ? le.log(e3.message) : le.warn(e3));
    });
  }
  _processEvent(e2, t2, n2) {
    const r2 = this.getOptions(), { sampleRate: i2 } = r2, o2 = Rn(e2), s2 = Nn(e2), a2 = e2.type || "error", c2 = `before send for type \`${a2}\``, l2 = void 0 === i2 ? void 0 : function(e3) {
      if ("boolean" == typeof e3) return Number(e3);
      const t3 = "string" == typeof e3 ? parseFloat(e3) : e3;
      if (!("number" != typeof t3 || isNaN(t3) || t3 < 0 || t3 > 1)) return t3;
      te && le.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(e3)} of type ${JSON.stringify(typeof e3)}.`);
    }(i2);
    if (s2 && "number" == typeof l2 && Math.random() > l2) return this.recordDroppedEvent("sample_rate", "error", e2), ht(new xn(`Discarding event because it's not included in the random sample (sampling rate = ${i2})`, "log"));
    const u2 = "replay_event" === a2 ? "replay" : a2, d2 = (e2.sdkProcessingMetadata || {}).capturedSpanIsolationScope;
    return this._prepareEvent(e2, t2, n2, d2).then((n3) => {
      if (null === n3) throw this.recordDroppedEvent("event_processor", u2, e2), new xn("An event processor returned `null`, will not send event.", "log");
      if (t2.data && true === t2.data.__sentry__) return n3;
      const i3 = function(e3, t3, n4, r3) {
        const { beforeSend: i4, beforeSendTransaction: o3, beforeSendSpan: s3 } = t3;
        if (Nn(n4) && i4) return i4(n4, r3);
        if (Rn(n4)) {
          if (n4.spans && s3) {
            const t4 = [];
            for (const r4 of n4.spans) {
              const n5 = s3(r4);
              n5 ? t4.push(n5) : (Wt(), e3.recordDroppedEvent("before_send", "span"));
            }
            n4.spans = t4;
          }
          if (o3) {
            if (n4.spans) {
              const e4 = n4.spans.length;
              n4.sdkProcessingMetadata = { ...n4.sdkProcessingMetadata, spanCountBeforeProcessing: e4 };
            }
            return o3(n4, r3);
          }
        }
        return n4;
      }(this, r2, n3, t2);
      return function(e3, t3) {
        const n4 = `${t3} must return \`null\` or a valid event.`;
        if (Fe(e3)) return e3.then((e4) => {
          if (!Ne(e4) && null !== e4) throw new xn(n4);
          return e4;
        }, (e4) => {
          throw new xn(`${t3} rejected with ${e4}`);
        });
        if (!Ne(e3) && null !== e3) throw new xn(n4);
        return e3;
      }(i3, c2);
    }).then((r3) => {
      if (null === r3) {
        if (this.recordDroppedEvent("before_send", u2, e2), o2) {
          const t3 = 1 + (e2.spans || []).length;
          this.recordDroppedEvent("before_send", "span", t3);
        }
        throw new xn(`${c2} returned \`null\`, will not send event.`, "log");
      }
      const i3 = n2 && n2.getSession();
      if (!o2 && i3 && this._updateSessionFromEvent(i3, r3), o2) {
        const e3 = (r3.sdkProcessingMetadata && r3.sdkProcessingMetadata.spanCountBeforeProcessing || 0) - (r3.spans ? r3.spans.length : 0);
        e3 > 0 && this.recordDroppedEvent("before_send", "span", e3);
      }
      const s3 = r3.transaction_info;
      if (o2 && s3 && r3.transaction !== e2.transaction) {
        const e3 = "custom";
        r3.transaction_info = { ...s3, source: e3 };
      }
      return this.sendEvent(r3, t2), r3;
    }).then(null, (e3) => {
      if (e3 instanceof xn) throw e3;
      throw this.captureException(e3, { data: { __sentry__: true }, originalException: e3 }), new xn(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${e3}`);
    });
  }
  _process(e2) {
    this._numProcessing++, e2.then((e3) => (this._numProcessing--, e3), (e3) => (this._numProcessing--, e3));
  }
  _clearOutcomes() {
    const e2 = this._outcomes;
    return this._outcomes = {}, Object.entries(e2).map(([e3, t2]) => {
      const [n2, r2] = e3.split(":");
      return { reason: n2, category: r2, quantity: t2 };
    });
  }
  _flushOutcomes() {
    te && le.log("Flushing outcomes...");
    const e2 = this._clearOutcomes();
    if (0 === e2.length) return void (te && le.log("No outcomes to send"));
    if (!this._dsn) return void (te && le.log("No dsn provided, will not send outcomes"));
    te && le.log("Sending outcomes:", e2);
    const t2 = (n2 = e2, rn((r2 = this._options.tunnel && Yt(this._dsn)) ? { dsn: r2 } : {}, [[{ type: "client_report" }, { timestamp: i2 || rt(), discarded_events: n2 }]]));
    var n2, r2, i2;
    this.sendEnvelope(t2);
  }
};
function Nn(e2) {
  return void 0 === e2.type;
}
function Rn(e2) {
  return "transaction" === e2.type;
}
function Fn(e2) {
  const t2 = [];
  function n2(e3) {
    return t2.splice(t2.indexOf(e3), 1)[0] || Promise.resolve(void 0);
  }
  return { $: t2, add: function(r2) {
    if (!(void 0 === e2 || t2.length < e2)) return ht(new xn("Not adding Promise because buffer limit was reached."));
    const i2 = r2();
    return -1 === t2.indexOf(i2) && t2.push(i2), i2.then(() => n2(i2)).then(null, () => n2(i2).then(null, () => {
    })), i2;
  }, drain: function(e3) {
    return new ft((n3, r2) => {
      let i2 = t2.length;
      if (!i2) return n3(true);
      const o2 = setTimeout(() => {
        e3 && e3 > 0 && n3(false);
      }, e3);
      t2.forEach((e4) => {
        pt(e4).then(() => {
          --i2 || (clearTimeout(o2), n3(true));
        }, r2);
      });
    });
  } };
}
function Bn(e2, { statusCode: t2, headers: n2 }, r2 = Date.now()) {
  const i2 = { ...e2 }, o2 = n2 && n2["x-sentry-rate-limits"], s2 = n2 && n2["retry-after"];
  if (o2) for (const e3 of o2.trim().split(",")) {
    const [t3, n3, , , o3] = e3.split(":", 5), s3 = parseInt(t3, 10), a2 = 1e3 * (isNaN(s3) ? 60 : s3);
    if (n3) for (const e4 of n3.split(";")) "metric_bucket" === e4 && o3 && !o3.split(";").includes("custom") || (i2[e4] = r2 + a2);
    else i2.all = r2 + a2;
  }
  else s2 ? i2.all = r2 + function(e3, t3 = Date.now()) {
    const n3 = parseInt(`${e3}`, 10);
    if (!isNaN(n3)) return 1e3 * n3;
    const r3 = Date.parse(`${e3}`);
    return isNaN(r3) ? 6e4 : r3 - t3;
  }(s2, r2) : 429 === t2 && (i2.all = r2 + 6e4);
  return i2;
}
function Un(e2, t2, n2 = Fn(e2.bufferSize || 64)) {
  let r2 = {};
  return { send: function(i2) {
    const o2 = [];
    if (sn(i2, (t3, n3) => {
      const i3 = dn(n3);
      if (function(e3, t4, n4 = Date.now()) {
        return function(e4, t5) {
          return e4[t5] || e4.all || 0;
        }(e3, t4) > n4;
      }(r2, i3)) {
        const r3 = Vn(t3, n3);
        e2.recordDroppedEvent("ratelimit_backoff", i3, r3);
      } else o2.push(t3);
    }), 0 === o2.length) return pt({});
    const s2 = rn(i2[0], o2), a2 = (t3) => {
      sn(s2, (n3, r3) => {
        const i3 = Vn(n3, r3);
        e2.recordDroppedEvent(t3, dn(r3), i3);
      });
    };
    return n2.add(() => t2({ body: cn(s2) }).then((e3) => (void 0 !== e3.statusCode && (e3.statusCode < 200 || e3.statusCode >= 300) && te && le.warn(`Sentry responded with status code ${e3.statusCode} to sent event.`), r2 = Bn(r2, e3), e3), (e3) => {
      throw a2("network_error"), e3;
    })).then((e3) => e3, (e3) => {
      if (e3 instanceof xn) return te && le.error("Skipped sending event because buffer is full."), a2("queue_overflow"), pt({});
      throw e3;
    });
  }, flush: (e3) => n2.drain(e3) };
}
function Vn(e2, t2) {
  if ("event" === t2 || "transaction" === t2) return Array.isArray(e2) ? e2[1] : void 0;
}
var Jn = 100;
function $n(e2, t2) {
  const n2 = xt(), r2 = It();
  if (!n2) return;
  const { beforeBreadcrumb: i2 = null, maxBreadcrumbs: o2 = Jn } = n2.getOptions();
  if (o2 <= 0) return;
  const s2 = { timestamp: rt(), ...e2 }, a2 = i2 ? ce(() => i2(s2, t2)) : s2;
  null !== a2 && (n2.emit && n2.emit("beforeAddBreadcrumb", a2, t2), r2.addBreadcrumb(a2, o2));
}
var qn;
var zn = /* @__PURE__ */ new WeakMap();
var Wn = () => ({ name: "FunctionToString", setupOnce() {
  qn = Function.prototype.toString;
  try {
    Function.prototype.toString = function(...e2) {
      const t2 = Ye(this), n2 = zn.has(xt()) && void 0 !== t2 ? t2 : this;
      return qn.apply(n2, e2);
    };
  } catch (e2) {
  }
}, setup(e2) {
  zn.set(e2, true);
} });
var Hn = [/^Script error\.?$/, /^Javascript error: Script error\.? on line 0$/, /^ResizeObserver loop completed with undelivered notifications.$/, /^Cannot redefine property: googletag$/, "undefined is not an object (evaluating 'a.L')", `can't redefine non-configurable property "solana"`, "vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)", "Can't find variable: _AutofillCallbackHandler", /^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/];
var Gn = (e2 = {}) => ({ name: "InboundFilters", processEvent(t2, n2, r2) {
  const i2 = r2.getOptions(), o2 = function(e3 = {}, t3 = {}) {
    return { allowUrls: [...e3.allowUrls || [], ...t3.allowUrls || []], denyUrls: [...e3.denyUrls || [], ...t3.denyUrls || []], ignoreErrors: [...e3.ignoreErrors || [], ...t3.ignoreErrors || [], ...e3.disableErrorDefaults ? [] : Hn], ignoreTransactions: [...e3.ignoreTransactions || [], ...t3.ignoreTransactions || []], ignoreInternal: void 0 === e3.ignoreInternal || e3.ignoreInternal };
  }(e2, i2);
  return function(e3, t3) {
    if (t3.ignoreInternal && function(e4) {
      try {
        return "SentryError" === e4.exception.values[0].type;
      } catch (e5) {
      }
      return false;
    }(e3)) return te && le.warn(`Event dropped due to being internal Sentry Error.
Event: ${at(e3)}`), true;
    if (function(e4, t4) {
      if (e4.type || !t4 || !t4.length) return false;
      return function(e5) {
        const t5 = [];
        e5.message && t5.push(e5.message);
        let n3;
        try {
          n3 = e5.exception.values[e5.exception.values.length - 1];
        } catch (e6) {
        }
        n3 && n3.value && (t5.push(n3.value), n3.type && t5.push(`${n3.type}: ${n3.value}`));
        return t5;
      }(e4).some((e5) => He(e5, t4));
    }(e3, t3.ignoreErrors)) return te && le.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${at(e3)}`), true;
    if (function(e4) {
      if (e4.type) return false;
      if (!e4.exception || !e4.exception.values || 0 === e4.exception.values.length) return false;
      return !e4.message && !e4.exception.values.some((e5) => e5.stacktrace || e5.type && "Error" !== e5.type || e5.value);
    }(e3)) return te && le.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${at(e3)}`), true;
    if (function(e4, t4) {
      if ("transaction" !== e4.type || !t4 || !t4.length) return false;
      const n3 = e4.transaction;
      return !!n3 && He(n3, t4);
    }(e3, t3.ignoreTransactions)) return te && le.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${at(e3)}`), true;
    if (function(e4, t4) {
      if (!t4 || !t4.length) return false;
      const n3 = Qn(e4);
      return !!n3 && He(n3, t4);
    }(e3, t3.denyUrls)) return te && le.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${at(e3)}.
Url: ${Qn(e3)}`), true;
    if (!function(e4, t4) {
      if (!t4 || !t4.length) return true;
      const n3 = Qn(e4);
      return !n3 || He(n3, t4);
    }(e3, t3.allowUrls)) return te && le.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${at(e3)}.
Url: ${Qn(e3)}`), true;
    return false;
  }(t2, o2) ? null : t2;
} });
function Qn(e2) {
  try {
    let t2;
    try {
      t2 = e2.exception.values[0].stacktrace.frames;
    } catch (e3) {
    }
    return t2 ? function(e3 = []) {
      for (let t3 = e3.length - 1; t3 >= 0; t3--) {
        const n2 = e3[t3];
        if (n2 && "<anonymous>" !== n2.filename && "[native code]" !== n2.filename) return n2.filename || null;
      }
      return null;
    }(t2) : null;
  } catch (t2) {
    return te && le.error(`Cannot extract url for event ${at(e2)}`), null;
  }
}
function Kn(e2, t2, n2 = 250, r2, i2, o2, s2) {
  if (!(o2.exception && o2.exception.values && s2 && Be(s2.originalException, Error))) return;
  const a2 = o2.exception.values.length > 0 ? o2.exception.values[o2.exception.values.length - 1] : void 0;
  var c2, l2;
  a2 && (o2.exception.values = (c2 = Yn(e2, t2, i2, s2.originalException, r2, o2.exception.values, a2, 0), l2 = n2, c2.map((e3) => (e3.value && (e3.value = qe(e3.value, l2)), e3))));
}
function Yn(e2, t2, n2, r2, i2, o2, s2, a2) {
  if (o2.length >= n2 + 1) return o2;
  let c2 = [...o2];
  if (Be(r2[i2], Error)) {
    Xn(s2, a2);
    const o3 = e2(t2, r2[i2]), l2 = c2.length;
    Zn(o3, i2, l2, a2), c2 = Yn(e2, t2, n2, r2[i2], i2, [o3, ...c2], o3, l2);
  }
  return Array.isArray(r2.errors) && r2.errors.forEach((r3, o3) => {
    if (Be(r3, Error)) {
      Xn(s2, a2);
      const l2 = e2(t2, r3), u2 = c2.length;
      Zn(l2, `errors[${o3}]`, u2, a2), c2 = Yn(e2, t2, n2, r3, i2, [l2, ...c2], l2, u2);
    }
  }), c2;
}
function Xn(e2, t2) {
  e2.mechanism = e2.mechanism || { type: "generic", handled: true }, e2.mechanism = { ...e2.mechanism, ..."AggregateError" === e2.type && { is_exception_group: true }, exception_id: t2 };
}
function Zn(e2, t2, n2, r2) {
  e2.mechanism = e2.mechanism || { type: "generic", handled: true }, e2.mechanism = { ...e2.mechanism, type: "chained", source: t2, exception_id: n2, parent_id: r2 };
}
function er(e2) {
  if (!e2) return {};
  const t2 = e2.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);
  if (!t2) return {};
  const n2 = t2[6] || "", r2 = t2[8] || "";
  return { host: t2[4], path: t2[5], protocol: t2[2], search: n2, hash: r2, relative: t2[5] + n2 + r2 };
}
function tr() {
  "console" in re && se.forEach(function(e2) {
    e2 in re.console && Ge(re.console, e2, function(t2) {
      return ae[e2] = t2, function(...t3) {
        we("console", { args: t3, level: e2 });
        const n2 = ae[e2];
        n2 && n2.apply(re.console, t3);
      };
    });
  });
}
function nr(e2) {
  return "warn" === e2 ? "warning" : ["fatal", "error", "warning", "log", "info", "debug"].includes(e2) ? e2 : "log";
}
var rr = () => {
  let e2;
  return { name: "Dedupe", processEvent(t2) {
    if (t2.type) return t2;
    try {
      if (function(e3, t3) {
        if (!t3) return false;
        if (function(e4, t4) {
          const n2 = e4.message, r2 = t4.message;
          if (!n2 && !r2) return false;
          if (n2 && !r2 || !n2 && r2) return false;
          if (n2 !== r2) return false;
          if (!or(e4, t4)) return false;
          if (!ir(e4, t4)) return false;
          return true;
        }(e3, t3)) return true;
        if (function(e4, t4) {
          const n2 = sr(t4), r2 = sr(e4);
          if (!n2 || !r2) return false;
          if (n2.type !== r2.type || n2.value !== r2.value) return false;
          if (!or(e4, t4)) return false;
          if (!ir(e4, t4)) return false;
          return true;
        }(e3, t3)) return true;
        return false;
      }(t2, e2)) return te && le.warn("Event dropped due to being a duplicate of previously captured event."), null;
    } catch (e3) {
    }
    return e2 = t2;
  } };
};
function ir(e2, t2) {
  let n2 = ge(e2), r2 = ge(t2);
  if (!n2 && !r2) return true;
  if (n2 && !r2 || !n2 && r2) return false;
  if (r2.length !== n2.length) return false;
  for (let e3 = 0; e3 < r2.length; e3++) {
    const t3 = r2[e3], i2 = n2[e3];
    if (t3.filename !== i2.filename || t3.lineno !== i2.lineno || t3.colno !== i2.colno || t3.function !== i2.function) return false;
  }
  return true;
}
function or(e2, t2) {
  let n2 = e2.fingerprint, r2 = t2.fingerprint;
  if (!n2 && !r2) return true;
  if (n2 && !r2 || !n2 && r2) return false;
  try {
    return !(n2.join("") !== r2.join(""));
  } catch (e3) {
    return false;
  }
}
function sr(e2) {
  return e2.exception && e2.exception.values && e2.exception.values[0];
}
function ar(e2) {
  return void 0 === e2 ? void 0 : e2 >= 400 && e2 < 500 ? "warning" : e2 >= 500 ? "error" : void 0;
}
var cr = re;
function lr(e2) {
  return e2 && /^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e2.toString());
}
function ur() {
  if ("string" == typeof EdgeRuntime) return true;
  if (!function() {
    if (!("fetch" in cr)) return false;
    try {
      return new Headers(), new Request("http://www.example.com"), new Response(), true;
    } catch (e3) {
      return false;
    }
  }()) return false;
  if (lr(cr.fetch)) return true;
  let e2 = false;
  const t2 = cr.document;
  if (t2 && "function" == typeof t2.createElement) try {
    const n2 = t2.createElement("iframe");
    n2.hidden = true, t2.head.appendChild(n2), n2.contentWindow && n2.contentWindow.fetch && (e2 = lr(n2.contentWindow.fetch)), t2.head.removeChild(n2);
  } catch (e3) {
    oe && le.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ", e3);
  }
  return e2;
}
function dr(e2, t2) {
  const n2 = "fetch";
  be(n2, e2), _e(n2, () => function(e3, t3 = false) {
    if (t3 && !ur()) return;
    Ge(re, "fetch", function(t4) {
      return function(...n3) {
        const r2 = new Error(), { method: i2, url: o2 } = function(e4) {
          if (0 === e4.length) return { method: "GET", url: "" };
          if (2 === e4.length) {
            const [t6, n4] = e4;
            return { url: hr(t6), method: pr(n4, "method") ? String(n4.method).toUpperCase() : "GET" };
          }
          const t5 = e4[0];
          return { url: hr(t5), method: pr(t5, "method") ? String(t5.method).toUpperCase() : "GET" };
        }(n3), s2 = { args: n3, fetchData: { method: i2, url: o2 }, startTimestamp: 1e3 * it(), virtualError: r2 };
        return e3 || we("fetch", { ...s2 }), t4.apply(re, n3).then(async (t5) => (e3 ? e3(t5) : we("fetch", { ...s2, endTimestamp: 1e3 * it(), response: t5 }), t5), (e4) => {
          throw we("fetch", { ...s2, endTimestamp: 1e3 * it(), error: e4 }), Pe(e4) && void 0 === e4.stack && (e4.stack = r2.stack, Qe(e4, "framesToPop", 1)), e4;
        });
      };
    });
  }(void 0, t2));
}
function pr(e2, t2) {
  return !!e2 && "object" == typeof e2 && !!e2[t2];
}
function hr(e2) {
  return "string" == typeof e2 ? e2 : e2 ? pr(e2, "url") ? e2.url : e2.toString ? e2.toString() : "" : "";
}
var fr = re;
var vr = re;
var gr = 0;
function mr() {
  return gr > 0;
}
function yr(e2, t2 = {}) {
  if (!/* @__PURE__ */ function(e3) {
    return "function" == typeof e3;
  }(e2)) return e2;
  try {
    const t3 = e2.__sentry_wrapped__;
    if (t3) return "function" == typeof t3 ? t3 : e2;
    if (Ye(e2)) return e2;
  } catch (t3) {
    return e2;
  }
  const n2 = function(...n3) {
    try {
      const r2 = n3.map((e3) => yr(e3, t2));
      return e2.apply(this, r2);
    } catch (e3) {
      throw gr++, setTimeout(() => {
        gr--;
      }), function(...e4) {
        const t3 = At(Ee());
        if (2 === e4.length) {
          const [n4, r2] = e4;
          return n4 ? t3.withSetScope(n4, r2) : t3.withScope(r2);
        }
        t3.withScope(e4[0]);
      }((r2) => {
        var i2, o2;
        r2.addEventProcessor((e4) => (t2.mechanism && (ct(e4, void 0, void 0), lt(e4, t2.mechanism)), e4.extra = { ...e4.extra, arguments: n3 }, e4)), i2 = e3, jt().captureException(i2, Sn(o2));
      }), e3;
    }
  };
  try {
    for (const t3 in e2) Object.prototype.hasOwnProperty.call(e2, t3) && (n2[t3] = e2[t3]);
  } catch (e3) {
  }
  Ke(n2, e2), Qe(e2, "__sentry_wrapped__", n2);
  try {
    Object.getOwnPropertyDescriptor(n2, "name").configurable && Object.defineProperty(n2, "name", { get: () => e2.name });
  } catch (e3) {
  }
  return n2;
}
var br = "undefined" == typeof __SENTRY_DEBUG__ || __SENTRY_DEBUG__;
function _r(e2, t2) {
  const n2 = kr(e2, t2), r2 = { type: Er(t2), value: Tr(t2) };
  return n2.length && (r2.stacktrace = { frames: n2 }), void 0 === r2.type && "" === r2.value && (r2.value = "Unrecoverable error caught"), r2;
}
function wr(e2, t2, n2, r2) {
  const i2 = xt(), o2 = i2 && i2.getOptions().normalizeDepth, s2 = function(e3) {
    for (const t3 in e3) if (Object.prototype.hasOwnProperty.call(e3, t3)) {
      const n3 = e3[t3];
      if (n3 instanceof Error) return n3;
    }
    return;
  }(t2), a2 = { __serialized__: tn(t2, o2) };
  if (s2) return { exception: { values: [_r(e2, s2)] }, extra: a2 };
  const c2 = { exception: { values: [{ type: Re(t2) ? t2.constructor.name : r2 ? "UnhandledRejection" : "Error", value: Ar(t2, { isUnhandledRejection: r2 }) }] }, extra: a2 };
  if (n2) {
    const t3 = kr(e2, n2);
    t3.length && (c2.exception.values[0].stacktrace = { frames: t3 });
  }
  return c2;
}
function Sr(e2, t2) {
  return { exception: { values: [_r(e2, t2)] } };
}
function kr(e2, t2) {
  const n2 = t2.stacktrace || t2.stack || "", r2 = function(e3) {
    if (e3 && Mr.test(e3.message)) return 1;
    return 0;
  }(t2), i2 = function(e3) {
    if ("number" == typeof e3.framesToPop) return e3.framesToPop;
    return 0;
  }(t2);
  try {
    return e2(n2, r2, i2);
  } catch (e3) {
  }
  return [];
}
var Mr = /Minified React error #\d+;/i;
function Cr(e2) {
  return "undefined" != typeof WebAssembly && void 0 !== WebAssembly.Exception && e2 instanceof WebAssembly.Exception;
}
function Er(e2) {
  const t2 = e2 && e2.name;
  if (!t2 && Cr(e2)) {
    return e2.message && Array.isArray(e2.message) && 2 == e2.message.length ? e2.message[0] : "WebAssembly.Exception";
  }
  return t2;
}
function Tr(e2) {
  const t2 = e2 && e2.message;
  return t2 ? t2.error && "string" == typeof t2.error.message ? t2.error.message : Cr(e2) && Array.isArray(e2.message) && 2 == e2.message.length ? e2.message[1] : t2 : "No error message";
}
function Or(e2, t2, n2, r2, i2) {
  let o2;
  if (je(t2) && t2.error) {
    return Sr(e2, t2.error);
  }
  if (Ie(t2) || Ae(t2, "DOMException")) {
    const i3 = t2;
    if ("stack" in t2) o2 = Sr(e2, t2);
    else {
      const t3 = i3.name || (Ie(i3) ? "DOMError" : "DOMException"), s2 = i3.message ? `${t3}: ${i3.message}` : t3;
      o2 = Pr(e2, s2, n2, r2), ct(o2, s2);
    }
    return "code" in i3 && (o2.tags = { ...o2.tags, "DOMException.code": `${i3.code}` }), o2;
  }
  if (Pe(t2)) return Sr(e2, t2);
  if (Ne(t2) || Re(t2)) {
    return o2 = wr(e2, t2, n2, i2), lt(o2, { synthetic: true }), o2;
  }
  return o2 = Pr(e2, t2, n2, r2), ct(o2, `${t2}`, void 0), lt(o2, { synthetic: true }), o2;
}
function Pr(e2, t2, n2, r2) {
  const i2 = {};
  if (r2 && n2) {
    const r3 = kr(e2, n2);
    r3.length && (i2.exception = { values: [{ value: t2, stacktrace: { frames: r3 } }] }), lt(i2, { synthetic: true });
  }
  if (Le(t2)) {
    const { __sentry_template_string__: e3, __sentry_template_values__: n3 } = t2;
    return i2.logentry = { message: e3, params: n3 }, i2;
  }
  return i2.message = t2, i2;
}
function Ar(e2, { isUnhandledRejection: t2 }) {
  const n2 = function(e3, t3 = 40) {
    const n3 = Object.keys(Xe(e3));
    n3.sort();
    const r3 = n3[0];
    if (!r3) return "[object has no keys]";
    if (r3.length >= t3) return qe(r3, t3);
    for (let e4 = n3.length; e4 > 0; e4--) {
      const r4 = n3.slice(0, e4).join(", ");
      if (!(r4.length > t3)) return e4 === n3.length ? r4 : qe(r4, t3);
    }
    return "";
  }(e2), r2 = t2 ? "promise rejection" : "exception";
  if (je(e2)) return `Event \`ErrorEvent\` captured as ${r2} with message \`${e2.message}\``;
  if (Re(e2)) {
    return `Event \`${function(e3) {
      try {
        const t3 = Object.getPrototypeOf(e3);
        return t3 ? t3.constructor.name : void 0;
      } catch (e4) {
      }
    }(e2)}\` (type=${e2.type}) captured as ${r2}`;
  }
  return `Object captured as ${r2} with keys: ${n2}`;
}
var jr = class extends Dn {
  constructor(e2) {
    const t2 = { parentSpanIsAlwaysRootSpan: true, ...e2 };
    !function(e3, t3, n2 = [t3], r2 = "npm") {
      const i2 = e3._metadata || {};
      i2.sdk || (i2.sdk = { name: `sentry.javascript.${t3}`, packages: n2.map((e4) => ({ name: `${r2}:@sentry/${e4}`, version: ne })), version: ne }), e3._metadata = i2;
    }(t2, "browser", ["browser"], vr.SENTRY_SDK_SOURCE || "npm"), super(t2), t2.sendClientReports && vr.document && vr.document.addEventListener("visibilitychange", () => {
      "hidden" === vr.document.visibilityState && this._flushOutcomes();
    });
  }
  eventFromException(e2, t2) {
    return function(e3, t3, n2, r2) {
      const i2 = Or(e3, t3, n2 && n2.syntheticException || void 0, r2);
      return lt(i2), i2.level = "error", n2 && n2.event_id && (i2.event_id = n2.event_id), pt(i2);
    }(this._options.stackParser, e2, t2, this._options.attachStacktrace);
  }
  eventFromMessage(e2, t2 = "info", n2) {
    return function(e3, t3, n3 = "info", r2, i2) {
      const o2 = Pr(e3, t3, r2 && r2.syntheticException || void 0, i2);
      return o2.level = n3, r2 && r2.event_id && (o2.event_id = r2.event_id), pt(o2);
    }(this._options.stackParser, e2, t2, n2, this._options.attachStacktrace);
  }
  captureUserFeedback(e2) {
    if (!this._isEnabled()) return void (br && le.warn("SDK not enabled, will not capture user feedback."));
    const t2 = function(e3, { metadata: t3, tunnel: n2, dsn: r2 }) {
      const i2 = { event_id: e3.event_id, sent_at: (/* @__PURE__ */ new Date()).toISOString(), ...t3 && t3.sdk && { sdk: { name: t3.sdk.name, version: t3.sdk.version } }, ...!!n2 && !!r2 && { dsn: Yt(r2) } }, o2 = /* @__PURE__ */ function(e4) {
        return [{ type: "user_report" }, e4];
      }(e3);
      return rn(i2, [o2]);
    }(e2, { metadata: this.getSdkMetadata(), dsn: this.getDsn(), tunnel: this.getOptions().tunnel });
    this.sendEnvelope(t2);
  }
  _prepareEvent(e2, t2, n2) {
    return e2.platform = e2.platform || "javascript", super._prepareEvent(e2, t2, n2);
  }
};
var Ir = "undefined" == typeof __SENTRY_DEBUG__ || __SENTRY_DEBUG__;
var xr = re;
var Lr;
var Dr;
var Nr;
var Rr;
function Fr() {
  if (!xr.document) return;
  const e2 = we.bind(null, "dom"), t2 = Br(e2, true);
  xr.document.addEventListener("click", t2, false), xr.document.addEventListener("keypress", t2, false), ["EventTarget", "Node"].forEach((t3) => {
    const n2 = xr[t3], r2 = n2 && n2.prototype;
    r2 && r2.hasOwnProperty && r2.hasOwnProperty("addEventListener") && (Ge(r2, "addEventListener", function(t4) {
      return function(n3, r3, i2) {
        if ("click" === n3 || "keypress" == n3) try {
          const r4 = this.__sentry_instrumentation_handlers__ = this.__sentry_instrumentation_handlers__ || {}, o2 = r4[n3] = r4[n3] || { refCount: 0 };
          if (!o2.handler) {
            const r5 = Br(e2);
            o2.handler = r5, t4.call(this, n3, r5, i2);
          }
          o2.refCount++;
        } catch (e3) {
        }
        return t4.call(this, n3, r3, i2);
      };
    }), Ge(r2, "removeEventListener", function(e3) {
      return function(t4, n3, r3) {
        if ("click" === t4 || "keypress" == t4) try {
          const n4 = this.__sentry_instrumentation_handlers__ || {}, i2 = n4[t4];
          i2 && (i2.refCount--, i2.refCount <= 0 && (e3.call(this, t4, i2.handler, r3), i2.handler = void 0, delete n4[t4]), 0 === Object.keys(n4).length && delete this.__sentry_instrumentation_handlers__);
        } catch (e4) {
        }
        return e3.call(this, t4, n3, r3);
      };
    }));
  });
}
function Br(e2, t2 = false) {
  return (n2) => {
    if (!n2 || n2._sentryCaptured) return;
    const r2 = function(e3) {
      try {
        return e3.target;
      } catch (e4) {
        return null;
      }
    }(n2);
    if (function(e3, t3) {
      return "keypress" === e3 && (!t3 || !t3.tagName || "INPUT" !== t3.tagName && "TEXTAREA" !== t3.tagName && !t3.isContentEditable);
    }(n2.type, r2)) return;
    Qe(n2, "_sentryCaptured", true), r2 && !r2._sentryId && Qe(r2, "_sentryId", ot());
    const i2 = "keypress" === n2.type ? "input" : n2.type;
    if (!function(e3) {
      if (e3.type !== Dr) return false;
      try {
        if (!e3.target || e3.target._sentryId !== Nr) return false;
      } catch (e4) {
      }
      return true;
    }(n2)) {
      e2({ event: n2, name: i2, global: t2 }), Dr = n2.type, Nr = r2 ? r2._sentryId : void 0;
    }
    clearTimeout(Lr), Lr = xr.setTimeout(() => {
      Nr = void 0, Dr = void 0;
    }, 1e3);
  };
}
function Ur(e2) {
  const t2 = "history";
  be(t2, e2), _e(t2, Vr);
}
function Vr() {
  if (!function() {
    const e3 = fr.chrome, t3 = e3 && e3.app && e3.app.runtime, n2 = "history" in fr && !!fr.history.pushState && !!fr.history.replaceState;
    return !t3 && n2;
  }()) return;
  const e2 = xr.onpopstate;
  function t2(e3) {
    return function(...t3) {
      const n2 = t3.length > 2 ? t3[2] : void 0;
      if (n2) {
        const e4 = Rr, t4 = String(n2);
        Rr = t4;
        we("history", { from: e4, to: t4 });
      }
      return e3.apply(this, t3);
    };
  }
  xr.onpopstate = function(...t3) {
    const n2 = xr.location.href, r2 = Rr;
    Rr = n2;
    if (we("history", { from: r2, to: n2 }), e2) try {
      return e2.apply(this, t3);
    } catch (e3) {
    }
  }, Ge(xr.history, "pushState", t2), Ge(xr.history, "replaceState", t2);
}
var Jr = {};
function $r(e2) {
  Jr[e2] = void 0;
}
var qr = "__sentry_xhr_v3__";
function zr() {
  if (!xr.XMLHttpRequest) return;
  const e2 = XMLHttpRequest.prototype;
  e2.open = new Proxy(e2.open, { apply(e3, t2, n2) {
    const r2 = new Error(), i2 = 1e3 * it(), o2 = xe(n2[0]) ? n2[0].toUpperCase() : void 0, s2 = function(e4) {
      if (xe(e4)) return e4;
      try {
        return e4.toString();
      } catch (e5) {
      }
      return;
    }(n2[1]);
    if (!o2 || !s2) return e3.apply(t2, n2);
    t2[qr] = { method: o2, url: s2, request_headers: {} }, "POST" === o2 && s2.match(/sentry_key/) && (t2.__sentry_own_request__ = true);
    const a2 = () => {
      const e4 = t2[qr];
      if (e4 && 4 === t2.readyState) {
        try {
          e4.status_code = t2.status;
        } catch (e5) {
        }
        we("xhr", { endTimestamp: 1e3 * it(), startTimestamp: i2, xhr: t2, virtualError: r2 });
      }
    };
    return "onreadystatechange" in t2 && "function" == typeof t2.onreadystatechange ? t2.onreadystatechange = new Proxy(t2.onreadystatechange, { apply: (e4, t3, n3) => (a2(), e4.apply(t3, n3)) }) : t2.addEventListener("readystatechange", a2), t2.setRequestHeader = new Proxy(t2.setRequestHeader, { apply(e4, t3, n3) {
      const [r3, i3] = n3, o3 = t3[qr];
      return o3 && xe(r3) && xe(i3) && (o3.request_headers[r3.toLowerCase()] = i3), e4.apply(t3, n3);
    } }), e3.apply(t2, n2);
  } }), e2.send = new Proxy(e2.send, { apply(e3, t2, n2) {
    const r2 = t2[qr];
    if (!r2) return e3.apply(t2, n2);
    void 0 !== n2[0] && (r2.body = n2[0]);
    return we("xhr", { startTimestamp: 1e3 * it(), xhr: t2 }), e3.apply(t2, n2);
  } });
}
function Wr(e2, t2 = function(e3) {
  const t3 = Jr[e3];
  if (t3) return t3;
  let n2 = xr[e3];
  if (lr(n2)) return Jr[e3] = n2.bind(xr);
  const r2 = xr.document;
  if (r2 && "function" == typeof r2.createElement) try {
    const t4 = r2.createElement("iframe");
    t4.hidden = true, r2.head.appendChild(t4);
    const i2 = t4.contentWindow;
    i2 && i2[e3] && (n2 = i2[e3]), r2.head.removeChild(t4);
  } catch (t4) {
    Ir && le.warn(`Could not create sandbox iframe for ${e3} check, bailing to window.${e3}: `, t4);
  }
  return n2 ? Jr[e3] = n2.bind(xr) : n2;
}("fetch")) {
  let n2 = 0, r2 = 0;
  return Un(e2, function(i2) {
    const o2 = i2.body.length;
    n2 += o2, r2++;
    const s2 = { body: i2.body, method: "POST", referrerPolicy: "origin", headers: e2.headers, keepalive: n2 <= 6e4 && r2 < 15, ...e2.fetchOptions };
    if (!t2) return $r("fetch"), ht("No fetch implementation available");
    try {
      return t2(e2.url, s2).then((e3) => (n2 -= o2, r2--, { statusCode: e3.status, headers: { "x-sentry-rate-limits": e3.headers.get("X-Sentry-Rate-Limits"), "retry-after": e3.headers.get("Retry-After") } }));
    } catch (e3) {
      return $r("fetch"), n2 -= o2, r2--, ht(e3);
    }
  });
}
function Hr(e2, t2, n2, r2) {
  const i2 = { filename: e2, function: "<anonymous>" === t2 ? ue : t2, in_app: true };
  return void 0 !== n2 && (i2.lineno = n2), void 0 !== r2 && (i2.colno = r2), i2;
}
var Gr = /^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i;
var Qr = /^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i;
var Kr = /\((\S*)(?::(\d+))(?::(\d+))\)/;
var Yr = /^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i;
var Xr = /(\S+) line (\d+)(?: > eval line \d+)* > eval/i;
var Zr = function(...e2) {
  const t2 = e2.sort((e3, t3) => e3[0] - t3[0]).map((e3) => e3[1]);
  return (e3, n2 = 0, r2 = 0) => {
    const i2 = [], o2 = e3.split("\n");
    for (let e4 = n2; e4 < o2.length; e4++) {
      const n3 = o2[e4];
      if (n3.length > 1024) continue;
      const s2 = de.test(n3) ? n3.replace(de, "$1") : n3;
      if (!s2.match(/\S*Error: /)) {
        for (const e5 of t2) {
          const t3 = e5(s2);
          if (t3) {
            i2.push(t3);
            break;
          }
        }
        if (i2.length >= 50 + r2) break;
      }
    }
    return function(e4) {
      if (!e4.length) return [];
      const t3 = Array.from(e4);
      /sentryWrapped/.test(he(t3).function || "") && t3.pop();
      t3.reverse(), pe.test(he(t3).function || "") && (t3.pop(), pe.test(he(t3).function || "") && t3.pop());
      return t3.slice(0, 50).map((e5) => ({ ...e5, filename: e5.filename || he(t3).filename, function: e5.function || ue }));
    }(i2.slice(r2));
  };
}(...[[30, (e2) => {
  const t2 = Gr.exec(e2);
  if (t2) {
    const [, e3, n3, r2] = t2;
    return Hr(e3, ue, +n3, +r2);
  }
  const n2 = Qr.exec(e2);
  if (n2) {
    if (n2[2] && 0 === n2[2].indexOf("eval")) {
      const e4 = Kr.exec(n2[2]);
      e4 && (n2[2] = e4[1], n2[3] = e4[2], n2[4] = e4[3]);
    }
    const [e3, t3] = ei(n2[1] || ue, n2[2]);
    return Hr(t3, e3, n2[3] ? +n2[3] : void 0, n2[4] ? +n2[4] : void 0);
  }
}], [50, (e2) => {
  const t2 = Yr.exec(e2);
  if (t2) {
    if (t2[3] && t2[3].indexOf(" > eval") > -1) {
      const e4 = Xr.exec(t2[3]);
      e4 && (t2[1] = t2[1] || "eval", t2[3] = e4[1], t2[4] = e4[2], t2[5] = "");
    }
    let e3 = t2[3], n2 = t2[1] || ue;
    return [n2, e3] = ei(n2, e3), Hr(e3, n2, t2[4] ? +t2[4] : void 0, t2[5] ? +t2[5] : void 0);
  }
}]]);
var ei = (e2, t2) => {
  const n2 = -1 !== e2.indexOf("safari-extension"), r2 = -1 !== e2.indexOf("safari-web-extension");
  return n2 || r2 ? [-1 !== e2.indexOf("@") ? e2.split("@")[0] : ue, n2 ? `safari-extension:${t2}` : `safari-web-extension:${t2}`] : [e2, t2];
};
var ti = 1024;
var ni = (e2 = {}) => {
  const t2 = { console: true, dom: true, fetch: true, history: true, sentry: true, xhr: true, ...e2 };
  return { name: "Breadcrumbs", setup(e3) {
    var n2;
    t2.console && function(e4) {
      const t3 = "console";
      be(t3, e4), _e(t3, tr);
    }(/* @__PURE__ */ function(e4) {
      return function(t3) {
        if (xt() !== e4) return;
        const n3 = { category: "console", data: { arguments: t3.args, logger: "console" }, level: nr(t3.level), message: ze(t3.args, " ") };
        if ("assert" === t3.level) {
          if (false !== t3.args[0]) return;
          n3.message = `Assertion failed: ${ze(t3.args.slice(1), " ") || "console.assert"}`, n3.data.arguments = t3.args.slice(1);
        }
        $n(n3, { input: t3.args, level: t3.level });
      };
    }(e3)), t2.dom && (n2 = /* @__PURE__ */ function(e4, t3) {
      return function(n3) {
        if (xt() !== e4) return;
        let r2, i2, o2 = "object" == typeof t3 ? t3.serializeAttribute : void 0, s2 = "object" == typeof t3 && "number" == typeof t3.maxStringLength ? t3.maxStringLength : void 0;
        s2 && s2 > ti && (br && le.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${s2} was configured. Sentry will use 1024 instead.`), s2 = ti), "string" == typeof o2 && (o2 = [o2]);
        try {
          const e5 = n3.event, t4 = function(e6) {
            return !!e6 && !!e6.target;
          }(e5) ? e5.target : e5;
          r2 = Je(t4, { keyAttrs: o2, maxStringLength: s2 }), i2 = function(e6) {
            if (!Ve.HTMLElement) return null;
            let t5 = e6;
            for (let e7 = 0; e7 < 5; e7++) {
              if (!t5) return null;
              if (t5 instanceof HTMLElement) {
                if (t5.dataset.sentryComponent) return t5.dataset.sentryComponent;
                if (t5.dataset.sentryElement) return t5.dataset.sentryElement;
              }
              t5 = t5.parentNode;
            }
            return null;
          }(t4);
        } catch (e5) {
          r2 = "<unknown>";
        }
        if (0 === r2.length) return;
        const a2 = { category: `ui.${n3.name}`, message: r2 };
        i2 && (a2.data = { "ui.component_name": i2 }), $n(a2, { event: n3.event, name: n3.name, global: n3.global });
      };
    }(e3, t2.dom), be("dom", n2), _e("dom", Fr)), t2.xhr && function(e4) {
      be("xhr", e4), _e("xhr", zr);
    }(/* @__PURE__ */ function(e4) {
      return function(t3) {
        if (xt() !== e4) return;
        const { startTimestamp: n3, endTimestamp: r2 } = t3, i2 = t3.xhr[qr];
        if (!n3 || !r2 || !i2) return;
        const { method: o2, url: s2, status_code: a2, body: c2 } = i2, l2 = { method: o2, url: s2, status_code: a2 }, u2 = { xhr: t3.xhr, input: c2, startTimestamp: n3, endTimestamp: r2 };
        $n({ category: "xhr", data: l2, type: "http", level: ar(a2) }, u2);
      };
    }(e3)), t2.fetch && dr(/* @__PURE__ */ function(e4) {
      return function(t3) {
        if (xt() !== e4) return;
        const { startTimestamp: n3, endTimestamp: r2 } = t3;
        if (r2 && (!t3.fetchData.url.match(/sentry_key/) || "POST" !== t3.fetchData.method)) if (t3.error) {
          $n({ category: "fetch", data: t3.fetchData, level: "error", type: "http" }, { data: t3.error, input: t3.args, startTimestamp: n3, endTimestamp: r2 });
        } else {
          const e5 = t3.response, i2 = { ...t3.fetchData, status_code: e5 && e5.status }, o2 = { input: t3.args, response: e5, startTimestamp: n3, endTimestamp: r2 };
          $n({ category: "fetch", data: i2, type: "http", level: ar(i2.status_code) }, o2);
        }
      };
    }(e3)), t2.history && Ur(/* @__PURE__ */ function(e4) {
      return function(t3) {
        if (xt() !== e4) return;
        let n3 = t3.from, r2 = t3.to;
        const i2 = er(vr.location.href);
        let o2 = n3 ? er(n3) : void 0;
        const s2 = er(r2);
        o2 && o2.path || (o2 = i2), i2.protocol === s2.protocol && i2.host === s2.host && (r2 = s2.relative), i2.protocol === o2.protocol && i2.host === o2.host && (n3 = o2.relative), $n({ category: "navigation", data: { from: n3, to: r2 } });
      };
    }(e3)), t2.sentry && e3.on("beforeSendEvent", /* @__PURE__ */ function(e4) {
      return function(t3) {
        xt() === e4 && $n({ category: "sentry." + ("transaction" === t3.type ? "transaction" : "event"), event_id: t3.event_id, level: t3.level, message: at(t3) }, { event: t3 });
      };
    }(e3));
  } };
};
var ri = ["EventTarget", "Window", "Node", "ApplicationCache", "AudioTrackList", "BroadcastChannel", "ChannelMergerNode", "CryptoOperation", "EventSource", "FileReader", "HTMLUnknownElement", "IDBDatabase", "IDBRequest", "IDBTransaction", "KeyOperation", "MediaController", "MessagePort", "ModalWindow", "Notification", "SVGElementInstance", "Screen", "SharedWorker", "TextTrack", "TextTrackCue", "TextTrackList", "WebSocket", "WebSocketWorker", "Worker", "XMLHttpRequest", "XMLHttpRequestEventTarget", "XMLHttpRequestUpload"];
var ii = (e2 = {}) => {
  const t2 = { XMLHttpRequest: true, eventTarget: true, requestAnimationFrame: true, setInterval: true, setTimeout: true, ...e2 };
  return { name: "BrowserApiErrors", setupOnce() {
    t2.setTimeout && Ge(vr, "setTimeout", oi), t2.setInterval && Ge(vr, "setInterval", oi), t2.requestAnimationFrame && Ge(vr, "requestAnimationFrame", si), t2.XMLHttpRequest && "XMLHttpRequest" in vr && Ge(XMLHttpRequest.prototype, "send", ai);
    const e3 = t2.eventTarget;
    if (e3) {
      (Array.isArray(e3) ? e3 : ri).forEach(ci);
    }
  } };
};
function oi(e2) {
  return function(...t2) {
    const n2 = t2[0];
    return t2[0] = yr(n2, { mechanism: { data: { function: ve(e2) }, handled: false, type: "instrument" } }), e2.apply(this, t2);
  };
}
function si(e2) {
  return function(t2) {
    return e2.apply(this, [yr(t2, { mechanism: { data: { function: "requestAnimationFrame", handler: ve(e2) }, handled: false, type: "instrument" } })]);
  };
}
function ai(e2) {
  return function(...t2) {
    const n2 = this;
    return ["onload", "onerror", "onprogress", "onreadystatechange"].forEach((e3) => {
      e3 in n2 && "function" == typeof n2[e3] && Ge(n2, e3, function(t3) {
        const n3 = { mechanism: { data: { function: e3, handler: ve(t3) }, handled: false, type: "instrument" } }, r2 = Ye(t3);
        return r2 && (n3.mechanism.data.handler = ve(r2)), yr(t3, n3);
      });
    }), e2.apply(this, t2);
  };
}
function ci(e2) {
  const t2 = vr[e2], n2 = t2 && t2.prototype;
  n2 && n2.hasOwnProperty && n2.hasOwnProperty("addEventListener") && (Ge(n2, "addEventListener", function(t3) {
    return function(n3, r2, i2) {
      try {
        "function" == typeof r2.handleEvent && (r2.handleEvent = yr(r2.handleEvent, { mechanism: { data: { function: "handleEvent", handler: ve(r2), target: e2 }, handled: false, type: "instrument" } }));
      } catch (e3) {
      }
      return t3.apply(this, [n3, yr(r2, { mechanism: { data: { function: "addEventListener", handler: ve(r2), target: e2 }, handled: false, type: "instrument" } }), i2]);
    };
  }), Ge(n2, "removeEventListener", function(e3) {
    return function(t3, n3, r2) {
      try {
        const i2 = n3.__sentry_wrapped__;
        i2 && e3.call(this, t3, i2, r2);
      } catch (e4) {
      }
      return e3.call(this, t3, n3, r2);
    };
  }));
}
var li = () => ({ name: "BrowserSession", setupOnce() {
  void 0 !== vr.document ? (Cn({ ignoreDuration: true }), On(), Ur(({ from: e2, to: t2 }) => {
    void 0 !== e2 && e2 !== t2 && (Cn({ ignoreDuration: true }), On());
  })) : br && le.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.");
} });
var ui = (e2 = {}) => {
  const t2 = { onerror: true, onunhandledrejection: true, ...e2 };
  return { name: "GlobalHandlers", setupOnce() {
    Error.stackTraceLimit = 50;
  }, setup(e3) {
    t2.onerror && (!function(e4) {
      !function(e5) {
        const t3 = "error";
        be(t3, e5), _e(t3, ke);
      }((t3) => {
        const { stackParser: n2, attachStacktrace: r2 } = pi();
        if (xt() !== e4 || mr()) return;
        const { msg: i2, url: o2, line: s2, column: a2, error: c2 } = t3, l2 = function(e5, t4, n3, r3) {
          const i3 = e5.exception = e5.exception || {}, o3 = i3.values = i3.values || [], s3 = o3[0] = o3[0] || {}, a3 = s3.stacktrace = s3.stacktrace || {}, c3 = a3.frames = a3.frames || [], l3 = r3, u2 = n3, d2 = xe(t4) && t4.length > 0 ? t4 : function() {
            try {
              return Ve.document.location.href;
            } catch (e6) {
              return "";
            }
          }();
          0 === c3.length && c3.push({ colno: l3, filename: d2, function: ue, in_app: true, lineno: u2 });
          return e5;
        }(Or(n2, c2 || i2, void 0, r2, false), o2, s2, a2);
        l2.level = "error", Mn(l2, { originalException: c2, mechanism: { handled: false, type: "onerror" } });
      });
    }(e3), di("onerror")), t2.onunhandledrejection && (!function(e4) {
      !function(e5) {
        const t3 = "unhandledrejection";
        be(t3, e5), _e(t3, Ce);
      }((t3) => {
        const { stackParser: n2, attachStacktrace: r2 } = pi();
        if (xt() !== e4 || mr()) return;
        const i2 = function(e5) {
          if (De(e5)) return e5;
          try {
            if ("reason" in e5) return e5.reason;
            if ("detail" in e5 && "reason" in e5.detail) return e5.detail.reason;
          } catch (e6) {
          }
          return e5;
        }(t3), o2 = De(i2) ? { exception: { values: [{ type: "UnhandledRejection", value: `Non-Error promise rejection captured with value: ${String(i2)}` }] } } : Or(n2, i2, void 0, r2, true);
        o2.level = "error", Mn(o2, { originalException: i2, mechanism: { handled: false, type: "onunhandledrejection" } });
      });
    }(e3), di("onunhandledrejection"));
  } };
};
function di(e2) {
  br && le.log(`Global Handler attached: ${e2}`);
}
function pi() {
  const e2 = xt();
  return e2 && e2.getOptions() || { stackParser: () => [], attachStacktrace: false };
}
var hi = () => ({ name: "HttpContext", preprocessEvent(e2) {
  if (!vr.navigator && !vr.location && !vr.document) return;
  const t2 = e2.request && e2.request.url || vr.location && vr.location.href, { referrer: n2 } = vr.document || {}, { userAgent: r2 } = vr.navigator || {}, i2 = { ...e2.request && e2.request.headers, ...n2 && { Referer: n2 }, ...r2 && { "User-Agent": r2 } }, o2 = { ...e2.request, ...t2 && { url: t2 }, headers: i2 };
  e2.request = o2;
} });
var fi = (e2 = {}) => {
  const t2 = e2.limit || 5, n2 = e2.key || "cause";
  return { name: "LinkedErrors", preprocessEvent(e3, r2, i2) {
    const o2 = i2.getOptions();
    Kn(_r, o2.stackParser, o2.maxValueLength, n2, t2, e3, r2);
  } };
};
var vi = "new";
var gi = "loading";
var mi = "loaded";
var yi = "joining-meeting";
var bi = "joined-meeting";
var _i = "left-meeting";
var wi = "error";
var Ti = "playable";
var Oi = "unknown";
var Pi = "full";
var Ii = "base";
var Bi = "no-room";
var Vi = "end-of-life";
var $i = "connection-error";
var Xi = "iframe-ready-for-launch-config";
var Zi = "iframe-launch-config";
var eo = "theme-updated";
var to = "loading";
var no = "load-attempt-failed";
var ro = "loaded";
var io = "started-camera";
var oo = "camera-error";
var so = "joining-meeting";
var ao = "joined-meeting";
var co = "left-meeting";
var lo = "participant-joined";
var uo = "participant-updated";
var po = "participant-left";
var ho = "participant-counts-updated";
var fo = "access-state-updated";
var vo = "meeting-session-summary-updated";
var go = "meeting-session-state-updated";
var yo = "waiting-participant-added";
var bo = "waiting-participant-updated";
var _o = "waiting-participant-removed";
var wo = "track-started";
var So = "track-stopped";
var ko = "transcription-started";
var Mo = "transcription-stopped";
var Co = "transcription-error";
var Eo = "recording-started";
var To = "recording-stopped";
var Oo = "recording-stats";
var Po = "recording-error";
var Ao = "recording-upload-completed";
var jo = "recording-data";
var Io = "app-message";
var xo = "transcription-message";
var Lo = "remote-media-player-started";
var Do = "remote-media-player-updated";
var No = "remote-media-player-stopped";
var Ro = "local-screen-share-started";
var Fo = "local-screen-share-stopped";
var Bo = "local-screen-share-canceled";
var Uo = "active-speaker-change";
var Vo = "active-speaker-mode-change";
var Jo = "network-quality-change";
var $o = "network-connection";
var qo = "cpu-load-change";
var zo = "face-counts-updated";
var Wo = "fullscreen";
var Ho = "exited-fullscreen";
var Go = "live-streaming-started";
var Qo = "live-streaming-updated";
var Ko = "live-streaming-stopped";
var Yo = "live-streaming-error";
var Xo = "lang-updated";
var Zo = "receive-settings-updated";
var es = "input-settings-updated";
var ts = "nonfatal-error";
var ns = "error";
var rs = 4096;
var is = 102400;
var os = "iframe-call-message";
var ss = "local-screen-start";
var as = "daily-method-update-live-streaming-endpoints";
var cs = "transmit-log";
var ls = "daily-custom-track";
var us = { NONE: "none", BGBLUR: "background-blur", BGIMAGE: "background-image", FACE_DETECTION: "face-detection" };
var ds = { NONE: "none", NOISE_CANCELLATION: "noise-cancellation" };
var ps = { PLAY: "play", PAUSE: "pause" };
var hs = ["jpg", "png", "jpeg"];
var fs = "add-endpoints";
var vs = "remove-endpoints";
var gs = "sip-call-transfer";
function ms() {
  return !ys() && "undefined" != typeof window && window.navigator && window.navigator.userAgent ? window.navigator.userAgent : "";
}
function ys() {
  return "undefined" != typeof navigator && navigator.product && "ReactNative" === navigator.product;
}
function bs() {
  return navigator && navigator.mediaDevices && navigator.mediaDevices.getUserMedia;
}
function _s() {
  return !!(navigator && navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) && (function(e2, t2) {
    if (!e2 || !t2) return true;
    switch (e2) {
      case "Chrome":
        return t2.major >= 75;
      case "Safari":
        return RTCRtpTransceiver.prototype.hasOwnProperty("currentDirection") && !(13 === t2.major && 0 === t2.minor && 0 === t2.point);
      case "Firefox":
        return t2.major >= 67;
    }
    return true;
  }(Os(), Ps()) || ys());
}
function ws() {
  if (ys()) return false;
  if (!document) return false;
  var e2 = document.createElement("iframe");
  return !!e2.requestFullscreen || !!e2.webkitRequestFullscreen;
}
var Ss = function() {
  try {
    var e2 = document.createElement("canvas"), t2 = null != (navigator.webdriver ? e2.getContext("webgl2") : e2.getContext("webgl2", { failIfMajorPerformanceCaveat: true }));
    return e2.remove(), t2;
  } catch (e3) {
    return false;
  }
}();
function ks() {
  var e2 = arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
  return !ys() && (!!Ss && (e2 ? function() {
    if (Ts()) return false;
    return ["Chrome", "Firefox"].includes(Os());
  }() : function() {
    if (Ts()) return false;
    var e3 = Os();
    if ("Safari" === e3) {
      var t2 = xs();
      if (t2.major < 15 || 15 === t2.major && t2.minor < 4) return false;
    }
    if ("Chrome" === e3) {
      return As().major >= 77;
    }
    if ("Firefox" === e3) {
      return Ls().major >= 97;
    }
    return ["Chrome", "Firefox", "Safari"].includes(e3);
  }()));
}
function Ms() {
  if (ys()) return false;
  if (Es()) return false;
  if ("undefined" == typeof AudioWorkletNode) return false;
  switch (Os()) {
    case "Chrome":
    case "Firefox":
      return true;
    case "Safari":
      var e2 = Ps();
      return e2.major > 17 || 17 === e2.major && e2.minor >= 4;
  }
  return false;
}
function Cs() {
  return bs() && !function() {
    var e2, t2 = Os();
    if (!ms()) return true;
    switch (t2) {
      case "Chrome":
        return (e2 = As()).major && e2.major > 0 && e2.major < 75;
      case "Firefox":
        return (e2 = Ls()).major < 91;
      case "Safari":
        return (e2 = xs()).major < 13 || 13 === e2.major && e2.minor < 1;
      default:
        return true;
    }
  }();
}
function Es() {
  return ms().match(/Linux; Android/);
}
function Ts() {
  var e2, t2 = ms(), n2 = t2.match(/Mac/) && (!ys() && "undefined" != typeof window && null !== (e2 = window) && void 0 !== e2 && null !== (e2 = e2.navigator) && void 0 !== e2 && e2.maxTouchPoints ? window.navigator.maxTouchPoints : 0) >= 5;
  return !!(t2.match(/Mobi/) || t2.match(/Android/) || n2) || (!!ms().match(/DailyAnd\//) || void 0);
}
function Os() {
  if ("undefined" != typeof window) {
    var e2 = ms();
    return js() ? "Safari" : e2.indexOf("Edge") > -1 ? "Edge" : e2.match(/Chrome\//) ? "Chrome" : e2.indexOf("Safari") > -1 || Is() ? "Safari" : e2.indexOf("Firefox") > -1 ? "Firefox" : e2.indexOf("MSIE") > -1 || e2.indexOf(".NET") > -1 ? "IE" : "Unknown Browser";
  }
}
function Ps() {
  switch (Os()) {
    case "Chrome":
      return As();
    case "Safari":
      return xs();
    case "Firefox":
      return Ls();
    case "Edge":
      return function() {
        var e2 = 0, t2 = 0;
        if ("undefined" != typeof window) {
          var n2 = ms().match(/Edge\/(\d+).(\d+)/);
          if (n2) try {
            e2 = parseInt(n2[1]), t2 = parseInt(n2[2]);
          } catch (e3) {
          }
        }
        return { major: e2, minor: t2 };
      }();
  }
}
function As() {
  var e2 = 0, t2 = 0, n2 = 0, r2 = 0, i2 = false;
  if ("undefined" != typeof window) {
    var o2 = ms(), s2 = o2.match(/Chrome\/(\d+).(\d+).(\d+).(\d+)/);
    if (s2) try {
      e2 = parseInt(s2[1]), t2 = parseInt(s2[2]), n2 = parseInt(s2[3]), r2 = parseInt(s2[4]), i2 = o2.indexOf("OPR/") > -1;
    } catch (e3) {
    }
  }
  return { major: e2, minor: t2, build: n2, patch: r2, opera: i2 };
}
function js() {
  return !!ms().match(/iPad|iPhone|iPod/i) && bs();
}
function Is() {
  return ms().indexOf("AppleWebKit/605.1.15") > -1;
}
function xs() {
  var e2 = 0, t2 = 0, n2 = 0;
  if ("undefined" != typeof window) {
    var r2 = ms().match(/Version\/(\d+).(\d+)(.(\d+))?/);
    if (r2) try {
      e2 = parseInt(r2[1]), t2 = parseInt(r2[2]), n2 = parseInt(r2[4]);
    } catch (e3) {
    }
    else (js() || Is()) && (e2 = 14, t2 = 0, n2 = 3);
  }
  return { major: e2, minor: t2, point: n2 };
}
function Ls() {
  var e2 = 0, t2 = 0;
  if ("undefined" != typeof window) {
    var n2 = ms().match(/Firefox\/(\d+).(\d+)/);
    if (n2) try {
      e2 = parseInt(n2[1]), t2 = parseInt(n2[2]);
    } catch (e3) {
    }
  }
  return { major: e2, minor: t2 };
}
var Ds = function() {
  return o(function e2() {
    t(this, e2);
  }, [{ key: "addListenerForMessagesFromCallMachine", value: function(e2, t2, n2) {
    Y();
  } }, { key: "addListenerForMessagesFromDailyJs", value: function(e2, t2, n2) {
    Y();
  } }, { key: "sendMessageToCallMachine", value: function(e2, t2, n2, r2) {
    Y();
  } }, { key: "sendMessageToDailyJs", value: function(e2, t2) {
    Y();
  } }, { key: "removeListener", value: function(e2) {
    Y();
  } }]);
}();
function Ns(e2, t2) {
  var n2 = Object.keys(e2);
  if (Object.getOwnPropertySymbols) {
    var r2 = Object.getOwnPropertySymbols(e2);
    t2 && (r2 = r2.filter(function(t3) {
      return Object.getOwnPropertyDescriptor(e2, t3).enumerable;
    })), n2.push.apply(n2, r2);
  }
  return n2;
}
function Rs(e2) {
  for (var t2 = 1; t2 < arguments.length; t2++) {
    var n2 = null != arguments[t2] ? arguments[t2] : {};
    t2 % 2 ? Ns(Object(n2), true).forEach(function(t3) {
      u(e2, t3, n2[t3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e2, Object.getOwnPropertyDescriptors(n2)) : Ns(Object(n2)).forEach(function(t3) {
      Object.defineProperty(e2, t3, Object.getOwnPropertyDescriptor(n2, t3));
    });
  }
  return e2;
}
function Fs() {
  try {
    var e2 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (e3) {
  }
  return (Fs = function() {
    return !!e2;
  })();
}
var Bs = function() {
  function e2() {
    var n2, r2, i2, o2;
    return t(this, e2), r2 = this, i2 = a(i2 = e2), (n2 = s(r2, Fs() ? Reflect.construct(i2, o2 || [], a(r2).constructor) : i2.apply(r2, o2)))._wrappedListeners = {}, n2._messageCallbacks = {}, n2;
  }
  return l(e2, Ds), o(e2, [{ key: "addListenerForMessagesFromCallMachine", value: function(e3, t2, n2) {
    var r2 = this, i2 = function(i3) {
      if (i3.data && "iframe-call-message" === i3.data.what && (!i3.data.callClientId || i3.data.callClientId === t2) && (!i3.data.from || "module" !== i3.data.from)) {
        var o2 = Rs({}, i3.data);
        if (delete o2.from, o2.callbackStamp && r2._messageCallbacks[o2.callbackStamp]) {
          var s2 = o2.callbackStamp;
          r2._messageCallbacks[s2].call(n2, o2), delete r2._messageCallbacks[s2];
        }
        delete o2.what, delete o2.callbackStamp, e3.call(n2, o2);
      }
    };
    this._wrappedListeners[e3] = i2, window.addEventListener("message", i2);
  } }, { key: "addListenerForMessagesFromDailyJs", value: function(e3, t2, n2) {
    var r2 = function(r3) {
      var i2;
      if (!(!r3.data || r3.data.what !== os || !r3.data.action || r3.data.from && "module" !== r3.data.from || r3.data.callClientId && t2 && r3.data.callClientId !== t2 || null != r3 && null !== (i2 = r3.data) && void 0 !== i2 && i2.callFrameId)) {
        var o2 = r3.data;
        e3.call(n2, o2);
      }
    };
    this._wrappedListeners[e3] = r2, window.addEventListener("message", r2);
  } }, { key: "sendMessageToCallMachine", value: function(e3, t2, n2, r2) {
    if (!n2) throw new Error("undefined callClientId. Are you trying to use a DailyCall instance previously destroyed?");
    var i2 = Rs({}, e3);
    if (i2.what = os, i2.from = "module", i2.callClientId = n2, t2) {
      var o2 = K();
      this._messageCallbacks[o2] = t2, i2.callbackStamp = o2;
    }
    var s2 = r2 ? r2.contentWindow : window, a2 = this._callMachineTargetOrigin(r2);
    a2 && s2.postMessage(i2, a2);
  } }, { key: "sendMessageToDailyJs", value: function(e3, t2) {
    e3.what = os, e3.callClientId = t2, e3.from = "embedded", window.postMessage(e3, this._targetOriginFromWindowLocation());
  } }, { key: "removeListener", value: function(e3) {
    var t2 = this._wrappedListeners[e3];
    t2 && (window.removeEventListener("message", t2), delete this._wrappedListeners[e3]);
  } }, { key: "forwardPackagedMessageToCallMachine", value: function(e3, t2, n2) {
    var r2 = Rs({}, e3);
    r2.callClientId = n2;
    var i2 = t2 ? t2.contentWindow : window, o2 = this._callMachineTargetOrigin(t2);
    o2 && i2.postMessage(r2, o2);
  } }, { key: "addListenerForPackagedMessagesFromCallMachine", value: function(e3, t2) {
    var n2 = function(n3) {
      if (n3.data && "iframe-call-message" === n3.data.what && (!n3.data.callClientId || n3.data.callClientId === t2) && (!n3.data.from || "module" !== n3.data.from)) {
        var r2 = n3.data;
        e3(r2);
      }
    };
    return this._wrappedListeners[e3] = n2, window.addEventListener("message", n2), e3;
  } }, { key: "removeListenerForPackagedMessagesFromCallMachine", value: function(e3) {
    var t2 = this._wrappedListeners[e3];
    t2 && (window.removeEventListener("message", t2), delete this._wrappedListeners[e3]);
  } }, { key: "_callMachineTargetOrigin", value: function(e3) {
    return e3 ? e3.src ? new URL(e3.src).origin : void 0 : this._targetOriginFromWindowLocation();
  } }, { key: "_targetOriginFromWindowLocation", value: function() {
    return "file:" === window.location.protocol ? "*" : window.location.origin;
  } }]);
}();
function Us(e2, t2) {
  var n2 = Object.keys(e2);
  if (Object.getOwnPropertySymbols) {
    var r2 = Object.getOwnPropertySymbols(e2);
    t2 && (r2 = r2.filter(function(t3) {
      return Object.getOwnPropertyDescriptor(e2, t3).enumerable;
    })), n2.push.apply(n2, r2);
  }
  return n2;
}
function Vs() {
  try {
    var e2 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (e3) {
  }
  return (Vs = function() {
    return !!e2;
  })();
}
var Js = function() {
  function e2() {
    var n2, r2, i2, o2;
    return t(this, e2), r2 = this, i2 = a(i2 = e2), n2 = s(r2, Vs() ? Reflect.construct(i2, o2 || [], a(r2).constructor) : i2.apply(r2, o2)), global.callMachineToDailyJsEmitter = global.callMachineToDailyJsEmitter || new I.EventEmitter(), global.dailyJsToCallMachineEmitter = global.dailyJsToCallMachineEmitter || new I.EventEmitter(), n2._wrappedListeners = {}, n2._messageCallbacks = {}, n2;
  }
  return l(e2, Ds), o(e2, [{ key: "addListenerForMessagesFromCallMachine", value: function(e3, t2, n2) {
    this._addListener(e3, global.callMachineToDailyJsEmitter, t2, n2, "received call machine message");
  } }, { key: "addListenerForMessagesFromDailyJs", value: function(e3, t2, n2) {
    this._addListener(e3, global.dailyJsToCallMachineEmitter, t2, n2, "received daily-js message");
  } }, { key: "sendMessageToCallMachine", value: function(e3, t2, n2) {
    this._sendMessage(e3, global.dailyJsToCallMachineEmitter, n2, t2, "sending message to call machine");
  } }, { key: "sendMessageToDailyJs", value: function(e3, t2) {
    this._sendMessage(e3, global.callMachineToDailyJsEmitter, t2, null, "sending message to daily-js");
  } }, { key: "removeListener", value: function(e3) {
    var t2 = this._wrappedListeners[e3];
    t2 && (global.callMachineToDailyJsEmitter.removeListener("message", t2), global.dailyJsToCallMachineEmitter.removeListener("message", t2), delete this._wrappedListeners[e3]);
  } }, { key: "_addListener", value: function(e3, t2, n2, r2, i2) {
    var o2 = this, s2 = function(t3) {
      if (t3.callClientId === n2) {
        if (t3.callbackStamp && o2._messageCallbacks[t3.callbackStamp]) {
          var i3 = t3.callbackStamp;
          o2._messageCallbacks[i3].call(r2, t3), delete o2._messageCallbacks[i3];
        }
        e3.call(r2, t3);
      }
    };
    this._wrappedListeners[e3] = s2, t2.addListener("message", s2);
  } }, { key: "_sendMessage", value: function(e3, t2, n2, r2, i2) {
    var o2 = function(e4) {
      for (var t3 = 1; t3 < arguments.length; t3++) {
        var n3 = null != arguments[t3] ? arguments[t3] : {};
        t3 % 2 ? Us(Object(n3), true).forEach(function(t4) {
          u(e4, t4, n3[t4]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e4, Object.getOwnPropertyDescriptors(n3)) : Us(Object(n3)).forEach(function(t4) {
          Object.defineProperty(e4, t4, Object.getOwnPropertyDescriptor(n3, t4));
        });
      }
      return e4;
    }({}, e3);
    if (o2.callClientId = n2, r2) {
      var s2 = K();
      this._messageCallbacks[s2] = r2, o2.callbackStamp = s2;
    }
    t2.emit("message", o2);
  } }]);
}();
var $s = "replace";
var qs = "shallow-merge";
var zs = [$s, qs];
var Ws = function() {
  function e2() {
    var n2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, r2 = n2.data, i2 = n2.mergeStrategy, o2 = void 0 === i2 ? $s : i2;
    t(this, e2), e2._validateMergeStrategy(o2), e2._validateData(r2, o2), this.mergeStrategy = o2, this.data = r2;
  }
  return o(e2, [{ key: "isNoOp", value: function() {
    return e2.isNoOpUpdate(this.data, this.mergeStrategy);
  } }], [{ key: "isNoOpUpdate", value: function(e3, t2) {
    return 0 === Object.keys(e3).length && t2 === qs;
  } }, { key: "_validateMergeStrategy", value: function(e3) {
    if (!zs.includes(e3)) throw Error("Unrecognized mergeStrategy provided. Options are: [".concat(zs, "]"));
  } }, { key: "_validateData", value: function(e3, t2) {
    if (!function(e4) {
      if (null == e4 || "object" !== n(e4)) return false;
      var t3 = Object.getPrototypeOf(e4);
      return null == t3 || t3 === Object.prototype;
    }(e3)) throw Error("Meeting session data must be a plain (map-like) object");
    var r2;
    try {
      if (r2 = JSON.stringify(e3), t2 === $s) {
        var i2 = JSON.parse(r2);
        N(i2, e3) || console.warn("The meeting session data provided will be modified when serialized.", i2, e3);
      } else if (t2 === qs) {
        for (var o2 in e3) if (Object.hasOwnProperty.call(e3, o2) && void 0 !== e3[o2]) {
          var s2 = JSON.parse(JSON.stringify(e3[o2]));
          N(e3[o2], s2) || console.warn("At least one key in the meeting session data provided will be modified when serialized.", s2, e3[o2]);
        }
      }
    } catch (e4) {
      throw Error("Meeting session data must be serializable to JSON: ".concat(e4));
    }
    if (r2.length > is) throw Error("Meeting session data is too large (".concat(r2.length, " characters). Maximum size suppported is ").concat(is, "."));
  } }]);
}();
function Hs() {
  try {
    var e2 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (e3) {
  }
  return (Hs = function() {
    return !!e2;
  })();
}
function Gs(e2) {
  var t2 = "function" == typeof Map ? /* @__PURE__ */ new Map() : void 0;
  return Gs = function(e3) {
    if (null === e3 || !function(e4) {
      try {
        return -1 !== Function.toString.call(e4).indexOf("[native code]");
      } catch (t3) {
        return "function" == typeof e4;
      }
    }(e3)) return e3;
    if ("function" != typeof e3) throw new TypeError("Super expression must either be null or a function");
    if (void 0 !== t2) {
      if (t2.has(e3)) return t2.get(e3);
      t2.set(e3, n2);
    }
    function n2() {
      return function(e4, t3, n3) {
        if (Hs()) return Reflect.construct.apply(null, arguments);
        var r2 = [null];
        r2.push.apply(r2, t3);
        var i2 = new (e4.bind.apply(e4, r2))();
        return n3 && c(i2, n3.prototype), i2;
      }(e3, arguments, a(this).constructor);
    }
    return n2.prototype = Object.create(e3.prototype, { constructor: { value: n2, enumerable: false, writable: true, configurable: true } }), c(n2, e3);
  }, Gs(e2);
}
function Qs() {
  try {
    var e2 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (e3) {
  }
  return (Qs = function() {
    return !!e2;
  })();
}
function Ks(e2) {
  var t2, n2 = null === (t2 = window._daily) || void 0 === t2 ? void 0 : t2.pendings;
  if (n2) {
    var r2 = n2.indexOf(e2);
    -1 !== r2 && n2.splice(r2, 1);
  }
}
var Ys = function() {
  return o(function e2(n2) {
    t(this, e2), this._currentLoad = null, this._callClientId = n2;
  }, [{ key: "load", value: function() {
    var e2, t2 = this, n2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, r2 = arguments.length > 1 ? arguments[1] : void 0, i2 = arguments.length > 2 ? arguments[2] : void 0;
    if (this.loaded) return window._daily.instances[this._callClientId].callMachine.reset(), void r2(true);
    e2 = this._callClientId, window._daily.pendings.push(e2), this._currentLoad && this._currentLoad.cancel(), this._currentLoad = new Xs(n2, function() {
      r2(false);
    }, function(e3, n3) {
      n3 || Ks(t2._callClientId), i2(e3, n3);
    }), this._currentLoad.start();
  } }, { key: "cancel", value: function() {
    this._currentLoad && this._currentLoad.cancel(), Ks(this._callClientId);
  } }, { key: "loaded", get: function() {
    return this._currentLoad && this._currentLoad.succeeded;
  } }]);
}();
var Xs = function() {
  return o(function e2() {
    var n2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, r2 = arguments.length > 1 ? arguments[1] : void 0, i2 = arguments.length > 2 ? arguments[2] : void 0;
    t(this, e2), this._attemptsRemaining = 3, this._currentAttempt = null, this._dailyConfig = n2, this._successCallback = r2, this._failureCallback = i2;
  }, [{ key: "start", value: function() {
    var e2 = this;
    if (!this._currentAttempt) {
      var t2 = function(n2) {
        e2._currentAttempt.cancelled || (e2._attemptsRemaining--, e2._failureCallback(n2, e2._attemptsRemaining > 0), e2._attemptsRemaining <= 0 || setTimeout(function() {
          e2._currentAttempt.cancelled || (e2._currentAttempt = new ta(e2._dailyConfig, e2._successCallback, t2), e2._currentAttempt.start());
        }, 3e3));
      };
      this._currentAttempt = new ta(this._dailyConfig, this._successCallback, t2), this._currentAttempt.start();
    }
  } }, { key: "cancel", value: function() {
    this._currentAttempt && this._currentAttempt.cancel();
  } }, { key: "cancelled", get: function() {
    return this._currentAttempt && this._currentAttempt.cancelled;
  } }, { key: "succeeded", get: function() {
    return this._currentAttempt && this._currentAttempt.succeeded;
  } }]);
}();
var Zs = function() {
  function e2() {
    return t(this, e2), n2 = this, i2 = arguments, r2 = a(r2 = e2), s(n2, Qs() ? Reflect.construct(r2, i2 || [], a(n2).constructor) : r2.apply(n2, i2));
    var n2, r2, i2;
  }
  return l(e2, Gs(Error)), o(e2);
}();
var ea = 2e4;
var ta = function() {
  return o(function e3(n2, r2, i2) {
    t(this, e3), this._loadAttemptImpl = ys() || !n2.avoidEval ? new na(n2, r2, i2) : new ra(n2, r2, i2);
  }, [{ key: "start", value: (e2 = p(function* () {
    return this._loadAttemptImpl.start();
  }), function() {
    return e2.apply(this, arguments);
  }) }, { key: "cancel", value: function() {
    this._loadAttemptImpl.cancel();
  } }, { key: "cancelled", get: function() {
    return this._loadAttemptImpl.cancelled;
  } }, { key: "succeeded", get: function() {
    return this._loadAttemptImpl.succeeded;
  } }]);
  var e2;
}();
var na = function() {
  return o(function e3(n3, r3, i3) {
    t(this, e3), this.cancelled = false, this.succeeded = false, this._networkTimedOut = false, this._networkTimeout = null, this._iosCache = "undefined" != typeof iOSCallObjectBundleCache && iOSCallObjectBundleCache, this._refetchHeaders = null, this._dailyConfig = n3, this._successCallback = r3, this._failureCallback = i3;
  }, [{ key: "start", value: (i2 = p(function* () {
    var e3 = Z(this._dailyConfig);
    !(yield this._tryLoadFromIOSCache(e3)) && this._loadFromNetwork(e3);
  }), function() {
    return i2.apply(this, arguments);
  }) }, { key: "cancel", value: function() {
    clearTimeout(this._networkTimeout), this.cancelled = true;
  } }, { key: "_tryLoadFromIOSCache", value: (r2 = p(function* (e3) {
    if (!this._iosCache) return false;
    try {
      var t2 = yield this._iosCache.get(e3);
      return !!this.cancelled || !!t2 && (t2.code ? (Function('"use strict";' + t2.code)(), this.succeeded = true, this._successCallback(), true) : (this._refetchHeaders = t2.refetchHeaders, false));
    } catch (e4) {
      return false;
    }
  }), function(e3) {
    return r2.apply(this, arguments);
  }) }, { key: "_loadFromNetwork", value: (n2 = p(function* (e3) {
    var t2 = this;
    this._networkTimeout = setTimeout(function() {
      t2._networkTimedOut = true, t2._failureCallback({ msg: "Timed out (>".concat(ea, " ms) when loading call object bundle ").concat(e3), type: "timeout" });
    }, ea);
    try {
      var n3 = this._refetchHeaders ? { headers: this._refetchHeaders } : {}, r3 = yield fetch(e3, n3);
      if (clearTimeout(this._networkTimeout), this.cancelled || this._networkTimedOut) throw new Zs();
      var i3 = yield this._getBundleCodeFromResponse(e3, r3);
      if (this.cancelled) throw new Zs();
      Function('"use strict";' + i3)(), this._iosCache && this._iosCache.set(e3, i3, r3.headers), this.succeeded = true, this._successCallback();
    } catch (t3) {
      if (clearTimeout(this._networkTimeout), t3 instanceof Zs || this.cancelled || this._networkTimedOut) return;
      this._failureCallback({ msg: "Failed to load call object bundle ".concat(e3, ": ").concat(t3), type: t3.message });
    }
  }), function(e3) {
    return n2.apply(this, arguments);
  }) }, { key: "_getBundleCodeFromResponse", value: (e2 = p(function* (e3, t2) {
    if (t2.ok) return yield t2.text();
    if (this._iosCache && 304 === t2.status) return (yield this._iosCache.renew(e3, t2.headers)).code;
    throw new Error("Received ".concat(t2.status, " response"));
  }), function(t2, n3) {
    return e2.apply(this, arguments);
  }) }]);
  var e2, n2, r2, i2;
}();
var ra = function() {
  return o(function e2(n2, r2, i2) {
    t(this, e2), this.cancelled = false, this.succeeded = false, this._dailyConfig = n2, this._successCallback = r2, this._failureCallback = i2, this._attemptId = K(), this._networkTimeout = null, this._scriptElement = null;
  }, [{ key: "start", value: function() {
    window._dailyCallMachineLoadWaitlist || (window._dailyCallMachineLoadWaitlist = /* @__PURE__ */ new Set());
    var e2 = Z(this._dailyConfig);
    "object" === ("undefined" == typeof document ? "undefined" : n(document)) ? this._startLoading(e2) : this._failureCallback({ msg: "Call object bundle must be loaded in a DOM/web context", type: "missing context" });
  } }, { key: "cancel", value: function() {
    this._stopLoading(), this.cancelled = true;
  } }, { key: "_startLoading", value: function(e2) {
    var t2 = this;
    this._signUpForCallMachineLoadWaitlist(), this._networkTimeout = setTimeout(function() {
      t2._stopLoading(), t2._failureCallback({ msg: "Timed out (>".concat(ea, " ms) when loading call object bundle ").concat(e2), type: "timeout" });
    }, ea);
    var n2 = document.getElementsByTagName("head")[0], r2 = document.createElement("script");
    this._scriptElement = r2, r2.onload = function() {
      t2._stopLoading(), t2.succeeded = true, t2._successCallback();
    }, r2.onerror = function(e3) {
      t2._stopLoading(), t2._failureCallback({ msg: "Failed to load call object bundle ".concat(e3.target.src), type: e3.message });
    }, r2.src = e2, n2.appendChild(r2);
  } }, { key: "_stopLoading", value: function() {
    this._withdrawFromCallMachineLoadWaitlist(), clearTimeout(this._networkTimeout), this._scriptElement && (this._scriptElement.onload = null, this._scriptElement.onerror = null);
  } }, { key: "_signUpForCallMachineLoadWaitlist", value: function() {
    window._dailyCallMachineLoadWaitlist.add(this._attemptId);
  } }, { key: "_withdrawFromCallMachineLoadWaitlist", value: function() {
    window._dailyCallMachineLoadWaitlist.delete(this._attemptId);
  } }]);
}();
var ia = function(e2, t2, n2) {
  return true === aa(e2.local, t2, n2);
};
var oa = function(e2, t2, n2) {
  return e2.local.streams && e2.local.streams[t2] && e2.local.streams[t2].stream && e2.local.streams[t2].stream["get".concat("video" === n2 ? "Video" : "Audio", "Tracks")]()[0];
};
var sa = function(e2, t2, n2, r2) {
  var i2 = ca(e2, t2, n2, r2);
  return i2 && i2.pendingTrack;
};
var aa = function(e2, t2, n2) {
  if (!e2) return false;
  var r2 = function(e3) {
    switch (e3) {
      case "avatar":
        return true;
      case "staged":
        return e3;
      default:
        return !!e3;
    }
  }, i2 = e2.public.subscribedTracks;
  return i2 && i2[t2] ? -1 === ["cam-audio", "cam-video", "screen-video", "screen-audio", "rmpAudio", "rmpVideo"].indexOf(n2) && i2[t2].custom ? [true, "staged"].includes(i2[t2].custom) ? r2(i2[t2].custom) : r2(i2[t2].custom[n2]) : r2(i2[t2][n2]) : !i2 || r2(i2.ALL);
};
var ca = function(e2, t2, n2, r2) {
  var i2 = Object.values(e2.streams || {}).filter(function(e3) {
    return e3.participantId === t2 && e3.type === n2 && e3.pendingTrack && e3.pendingTrack.kind === r2;
  }).sort(function(e3, t3) {
    return new Date(t3.starttime) - new Date(e3.starttime);
  });
  return i2 && i2[0];
};
var la = function(e2, t2) {
  var n2 = e2.local.public.customTracks;
  if (n2 && n2[t2]) return n2[t2].track;
};
function ua(e2, t2) {
  for (var n2 = t2.getState(), r2 = 0, i2 = ["cam", "screen"]; r2 < i2.length; r2++) for (var o2 = i2[r2], s2 = 0, a2 = ["video", "audio"]; s2 < a2.length; s2++) {
    var c2 = a2[s2], l2 = "cam" === o2 ? c2 : "screen".concat(c2.charAt(0).toUpperCase() + c2.slice(1)), u2 = e2.tracks[l2];
    if (u2) {
      var d2 = e2.local ? oa(n2, o2, c2) : sa(n2, e2.session_id, o2, c2);
      "playable" === u2.state && (u2.track = d2), u2.persistentTrack = d2;
    }
  }
}
function da(e2, t2) {
  try {
    var n2 = t2.getState();
    for (var r2 in e2.tracks) if (!pa(r2)) {
      var i2 = e2.tracks[r2].kind;
      if (i2) {
        var o2 = e2.tracks[r2];
        if (o2) {
          var s2 = e2.local ? la(n2, r2) : sa(n2, e2.session_id, r2, i2);
          "playable" === o2.state && (e2.tracks[r2].track = s2), o2.persistentTrack = s2;
        }
      } else console.error("unknown type for custom track");
    }
  } catch (e3) {
    console.error(e3);
  }
}
function pa(e2) {
  return ["video", "audio", "screenVideo", "screenAudio"].includes(e2);
}
function ha(e2, t2, n2) {
  var r2 = n2.getState();
  if (e2.local) {
    if (e2.audio) try {
      e2.audioTrack = r2.local.streams.cam.stream.getAudioTracks()[0], e2.audioTrack || (e2.audio = false);
    } catch (e3) {
    }
    if (e2.video) try {
      e2.videoTrack = r2.local.streams.cam.stream.getVideoTracks()[0], e2.videoTrack || (e2.video = false);
    } catch (e3) {
    }
    if (e2.screen) try {
      e2.screenVideoTrack = r2.local.streams.screen.stream.getVideoTracks()[0], e2.screenAudioTrack = r2.local.streams.screen.stream.getAudioTracks()[0], e2.screenVideoTrack || e2.screenAudioTrack || (e2.screen = false);
    } catch (e3) {
    }
  } else {
    var i2 = true;
    try {
      var o2 = r2.participants[e2.session_id];
      o2 && o2.public && o2.public.rtcType && "peer-to-peer" === o2.public.rtcType.impl && o2.private && !["connected", "completed"].includes(o2.private.peeringState) && (i2 = false);
    } catch (e3) {
      console.error(e3);
    }
    if (!i2) return e2.audio = false, e2.audioTrack = false, e2.video = false, e2.videoTrack = false, e2.screen = false, void (e2.screenTrack = false);
    try {
      r2.streams;
      if (e2.audio && ia(r2, e2.session_id, "cam-audio")) {
        var s2 = sa(r2, e2.session_id, "cam", "audio");
        s2 && (t2 && t2.audioTrack && t2.audioTrack.id === s2.id ? e2.audioTrack = s2 : s2.muted || (e2.audioTrack = s2)), e2.audioTrack || (e2.audio = false);
      }
      if (e2.video && ia(r2, e2.session_id, "cam-video")) {
        var a2 = sa(r2, e2.session_id, "cam", "video");
        a2 && (t2 && t2.videoTrack && t2.videoTrack.id === a2.id ? e2.videoTrack = a2 : a2.muted || (e2.videoTrack = a2)), e2.videoTrack || (e2.video = false);
      }
      if (e2.screen && ia(r2, e2.session_id, "screen-audio")) {
        var c2 = sa(r2, e2.session_id, "screen", "audio");
        c2 && (t2 && t2.screenAudioTrack && t2.screenAudioTrack.id === c2.id ? e2.screenAudioTrack = c2 : c2.muted || (e2.screenAudioTrack = c2));
      }
      if (e2.screen && ia(r2, e2.session_id, "screen-video")) {
        var l2 = sa(r2, e2.session_id, "screen", "video");
        l2 && (t2 && t2.screenVideoTrack && t2.screenVideoTrack.id === l2.id ? e2.screenVideoTrack = l2 : l2.muted || (e2.screenVideoTrack = l2));
      }
      e2.screenVideoTrack || e2.screenAudioTrack || (e2.screen = false);
    } catch (e3) {
      console.error("unexpected error matching up tracks", e3);
    }
  }
}
function fa(e2, t2) {
  var n2 = "undefined" != typeof Symbol && e2[Symbol.iterator] || e2["@@iterator"];
  if (!n2) {
    if (Array.isArray(e2) || (n2 = function(e3, t3) {
      if (e3) {
        if ("string" == typeof e3) return va(e3, t3);
        var n3 = {}.toString.call(e3).slice(8, -1);
        return "Object" === n3 && e3.constructor && (n3 = e3.constructor.name), "Map" === n3 || "Set" === n3 ? Array.from(e3) : "Arguments" === n3 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n3) ? va(e3, t3) : void 0;
      }
    }(e2)) || t2 && e2 && "number" == typeof e2.length) {
      n2 && (e2 = n2);
      var r2 = 0, i2 = function() {
      };
      return { s: i2, n: function() {
        return r2 >= e2.length ? { done: true } : { done: false, value: e2[r2++] };
      }, e: function(e3) {
        throw e3;
      }, f: i2 };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var o2, s2 = true, a2 = false;
  return { s: function() {
    n2 = n2.call(e2);
  }, n: function() {
    var e3 = n2.next();
    return s2 = e3.done, e3;
  }, e: function(e3) {
    a2 = true, o2 = e3;
  }, f: function() {
    try {
      s2 || null == n2.return || n2.return();
    } finally {
      if (a2) throw o2;
    }
  } };
}
function va(e2, t2) {
  (null == t2 || t2 > e2.length) && (t2 = e2.length);
  for (var n2 = 0, r2 = Array(t2); n2 < t2; n2++) r2[n2] = e2[n2];
  return r2;
}
var ga = /* @__PURE__ */ new Map();
var ma = null;
function ya(e2, t2) {
  var n2 = "undefined" != typeof Symbol && e2[Symbol.iterator] || e2["@@iterator"];
  if (!n2) {
    if (Array.isArray(e2) || (n2 = function(e3, t3) {
      if (e3) {
        if ("string" == typeof e3) return ba(e3, t3);
        var n3 = {}.toString.call(e3).slice(8, -1);
        return "Object" === n3 && e3.constructor && (n3 = e3.constructor.name), "Map" === n3 || "Set" === n3 ? Array.from(e3) : "Arguments" === n3 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n3) ? ba(e3, t3) : void 0;
      }
    }(e2)) || t2 && e2 && "number" == typeof e2.length) {
      n2 && (e2 = n2);
      var r2 = 0, i2 = function() {
      };
      return { s: i2, n: function() {
        return r2 >= e2.length ? { done: true } : { done: false, value: e2[r2++] };
      }, e: function(e3) {
        throw e3;
      }, f: i2 };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var o2, s2 = true, a2 = false;
  return { s: function() {
    n2 = n2.call(e2);
  }, n: function() {
    var e3 = n2.next();
    return s2 = e3.done, e3;
  }, e: function(e3) {
    a2 = true, o2 = e3;
  }, f: function() {
    try {
      s2 || null == n2.return || n2.return();
    } finally {
      if (a2) throw o2;
    }
  } };
}
function ba(e2, t2) {
  (null == t2 || t2 > e2.length) && (t2 = e2.length);
  for (var n2 = 0, r2 = Array(t2); n2 < t2; n2++) r2[n2] = e2[n2];
  return r2;
}
var _a = /* @__PURE__ */ new Map();
var wa = null;
function Sa(e2) {
  Ma() ? function(e3) {
    ga.has(e3) || (ga.set(e3, {}), navigator.mediaDevices.enumerateDevices().then(function(t2) {
      ga.has(e3) && (ga.get(e3).lastDevicesString = JSON.stringify(t2), ma || (ma = function() {
        var e4 = p(function* () {
          var e5, t3 = yield navigator.mediaDevices.enumerateDevices(), n2 = fa(ga.keys());
          try {
            for (n2.s(); !(e5 = n2.n()).done; ) {
              var r2 = e5.value, i2 = JSON.stringify(t3);
              i2 !== ga.get(r2).lastDevicesString && (ga.get(r2).lastDevicesString = i2, r2(t3));
            }
          } catch (e6) {
            n2.e(e6);
          } finally {
            n2.f();
          }
        });
        return function() {
          return e4.apply(this, arguments);
        };
      }(), navigator.mediaDevices.addEventListener("devicechange", ma)));
    }).catch(function() {
    }));
  }(e2) : function(e3) {
    _a.has(e3) || (_a.set(e3, {}), navigator.mediaDevices.enumerateDevices().then(function(t2) {
      _a.has(e3) && (_a.get(e3).lastDevicesString = JSON.stringify(t2), wa || (wa = setInterval(p(function* () {
        var e4, t3 = yield navigator.mediaDevices.enumerateDevices(), n2 = ya(_a.keys());
        try {
          for (n2.s(); !(e4 = n2.n()).done; ) {
            var r2 = e4.value, i2 = JSON.stringify(t3);
            i2 !== _a.get(r2).lastDevicesString && (_a.get(r2).lastDevicesString = i2, r2(t3));
          }
        } catch (e5) {
          n2.e(e5);
        } finally {
          n2.f();
        }
      }), 3e3)));
    }));
  }(e2);
}
function ka(e2) {
  Ma() ? function(e3) {
    ga.has(e3) && (ga.delete(e3), 0 === ga.size && ma && (navigator.mediaDevices.removeEventListener("devicechange", ma), ma = null));
  }(e2) : function(e3) {
    _a.has(e3) && (_a.delete(e3), 0 === _a.size && wa && (clearInterval(wa), wa = null));
  }(e2);
}
function Ma() {
  var e2;
  return ys() || void 0 !== (null === (e2 = navigator.mediaDevices) || void 0 === e2 ? void 0 : e2.ondevicechange);
}
var Ca = /* @__PURE__ */ new Set();
function Ea(e2, t2) {
  var n2 = t2.isLocalScreenVideo;
  return e2 && "live" === e2.readyState && !function(e3, t3) {
    return (!t3.isLocalScreenVideo || "Chrome" !== Os()) && e3.muted && !Ca.has(e3.id);
  }(e2, { isLocalScreenVideo: n2 });
}
function Ta(e2, t2) {
  var n2 = Object.keys(e2);
  if (Object.getOwnPropertySymbols) {
    var r2 = Object.getOwnPropertySymbols(e2);
    t2 && (r2 = r2.filter(function(t3) {
      return Object.getOwnPropertyDescriptor(e2, t3).enumerable;
    })), n2.push.apply(n2, r2);
  }
  return n2;
}
function Oa(e2) {
  for (var t2 = 1; t2 < arguments.length; t2++) {
    var n2 = null != arguments[t2] ? arguments[t2] : {};
    t2 % 2 ? Ta(Object(n2), true).forEach(function(t3) {
      u(e2, t3, n2[t3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e2, Object.getOwnPropertyDescriptors(n2)) : Ta(Object(n2)).forEach(function(t3) {
      Object.defineProperty(e2, t3, Object.getOwnPropertyDescriptor(n2, t3));
    });
  }
  return e2;
}
var Pa = Object.freeze({ VIDEO: "video", AUDIO: "audio", SCREEN_VIDEO: "screenVideo", SCREEN_AUDIO: "screenAudio", CUSTOM_VIDEO: "customVideo", CUSTOM_AUDIO: "customAudio" });
var Aa = Object.freeze({ PARTICIPANTS: "participants", STREAMING: "streaming", TRANSCRIPTION: "transcription" });
var ja = Object.values(Pa);
var Ia = ["v", "a", "sv", "sa", "cv", "ca"];
Object.freeze(ja.reduce(function(e2, t2, n2) {
  return e2[t2] = Ia[n2], e2;
}, {})), Object.freeze(Ia.reduce(function(e2, t2, n2) {
  return e2[t2] = ja[n2], e2;
}, {}));
var xa = [Pa.VIDEO, Pa.AUDIO, Pa.SCREEN_VIDEO, Pa.SCREEN_AUDIO];
var La = Object.values(Aa);
var Da = ["p", "s", "t"];
Object.freeze(La.reduce(function(e2, t2, n2) {
  return e2[t2] = Da[n2], e2;
}, {})), Object.freeze(Da.reduce(function(e2, t2, n2) {
  return e2[t2] = La[n2], e2;
}, {}));
var Na = function() {
  function e2() {
    var n2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, r2 = n2.base, i2 = n2.byUserId, o2 = n2.byParticipantId;
    t(this, e2), this.base = r2, this.byUserId = i2, this.byParticipantId = o2;
  }
  return o(e2, [{ key: "clone", value: function() {
    var t2 = new e2();
    if (this.base instanceof Ra ? t2.base = this.base.clone() : t2.base = this.base, void 0 !== this.byUserId) for (var n2 in t2.byUserId = {}, this.byUserId) {
      var r2 = this.byUserId[n2];
      t2.byUserId[n2] = r2 instanceof Ra ? r2.clone() : r2;
    }
    if (void 0 !== this.byParticipantId) for (var i2 in t2.byParticipantId = {}, this.byParticipantId) {
      var o2 = this.byParticipantId[i2];
      t2.byParticipantId[i2] = o2 instanceof Ra ? o2.clone() : o2;
    }
    return t2;
  } }, { key: "toJSONObject", value: function() {
    var e3 = {};
    if ("boolean" == typeof this.base ? e3.base = this.base : this.base instanceof Ra && (e3.base = this.base.toJSONObject()), void 0 !== this.byUserId) for (var t2 in e3.byUserId = {}, this.byUserId) {
      var n2 = this.byUserId[t2];
      e3.byUserId[t2] = n2 instanceof Ra ? n2.toJSONObject() : n2;
    }
    if (void 0 !== this.byParticipantId) for (var r2 in e3.byParticipantId = {}, this.byParticipantId) {
      var i2 = this.byParticipantId[r2];
      e3.byParticipantId[r2] = i2 instanceof Ra ? i2.toJSONObject() : i2;
    }
    return e3;
  } }, { key: "toMinifiedJSONObject", value: function() {
    var e3 = {};
    if (void 0 !== this.base && ("boolean" == typeof this.base ? e3.b = this.base : e3.b = this.base.toMinifiedJSONObject()), void 0 !== this.byUserId) for (var t2 in e3.u = {}, this.byUserId) {
      var n2 = this.byUserId[t2];
      e3.u[t2] = "boolean" == typeof n2 ? n2 : n2.toMinifiedJSONObject();
    }
    if (void 0 !== this.byParticipantId) for (var r2 in e3.p = {}, this.byParticipantId) {
      var i2 = this.byParticipantId[r2];
      e3.p[r2] = "boolean" == typeof i2 ? i2 : i2.toMinifiedJSONObject();
    }
    return e3;
  } }, { key: "normalize", value: function() {
    return this.base instanceof Ra && (this.base = this.base.normalize()), this.byUserId && (this.byUserId = Object.fromEntries(Object.entries(this.byUserId).map(function(e3) {
      var t2 = f(e3, 2), n2 = t2[0], r2 = t2[1];
      return [n2, r2 instanceof Ra ? r2.normalize() : r2];
    }))), this.byParticipantId && (this.byParticipantId = Object.fromEntries(Object.entries(this.byParticipantId).map(function(e3) {
      var t2 = f(e3, 2), n2 = t2[0], r2 = t2[1];
      return [n2, r2 instanceof Ra ? r2.normalize() : r2];
    }))), this;
  } }], [{ key: "fromJSONObject", value: function(t2) {
    var n2, r2, i2;
    if (void 0 !== t2.base && (n2 = "boolean" == typeof t2.base ? t2.base : Ra.fromJSONObject(t2.base)), void 0 !== t2.byUserId) for (var o2 in r2 = {}, t2.byUserId) {
      var s2 = t2.byUserId[o2];
      r2[o2] = "boolean" == typeof s2 ? s2 : Ra.fromJSONObject(s2);
    }
    if (void 0 !== t2.byParticipantId) for (var a2 in i2 = {}, t2.byParticipantId) {
      var c2 = t2.byParticipantId[a2];
      i2[a2] = "boolean" == typeof c2 ? c2 : Ra.fromJSONObject(c2);
    }
    return new e2({ base: n2, byUserId: r2, byParticipantId: i2 });
  } }, { key: "fromMinifiedJSONObject", value: function(t2) {
    var n2, r2, i2;
    if (void 0 !== t2.b && (n2 = "boolean" == typeof t2.b ? t2.b : Ra.fromMinifiedJSONObject(t2.b)), void 0 !== t2.u) for (var o2 in r2 = {}, t2.u) {
      var s2 = t2.u[o2];
      r2[o2] = "boolean" == typeof s2 ? s2 : Ra.fromMinifiedJSONObject(s2);
    }
    if (void 0 !== t2.p) for (var a2 in i2 = {}, t2.p) {
      var c2 = t2.p[a2];
      i2[a2] = "boolean" == typeof c2 ? c2 : Ra.fromMinifiedJSONObject(c2);
    }
    return new e2({ base: n2, byUserId: r2, byParticipantId: i2 });
  } }, { key: "validateJSONObject", value: function(e3) {
    if ("object" !== n(e3)) return [false, "canReceive must be an object"];
    for (var t2 = ["base", "byUserId", "byParticipantId"], r2 = 0, i2 = Object.keys(e3); r2 < i2.length; r2++) {
      var o2 = i2[r2];
      if (!t2.includes(o2)) return [false, "canReceive can only contain keys (".concat(t2.join(", "), ")")];
      if ("base" === o2) {
        var s2 = f(Ra.validateJSONObject(e3.base, true), 2), a2 = s2[0], c2 = s2[1];
        if (!a2) return [false, c2];
      } else {
        if ("object" !== n(e3[o2])) return [false, "invalid (non-object) value for field '".concat(o2, "' in canReceive")];
        for (var l2 = 0, u2 = Object.values(e3[o2]); l2 < u2.length; l2++) {
          var d2 = u2[l2], p2 = f(Ra.validateJSONObject(d2), 2), h2 = p2[0], v2 = p2[1];
          if (!h2) return [false, v2];
        }
      }
    }
    return [true];
  } }]);
}();
var Ra = function() {
  function e2() {
    var n2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, r2 = n2.video, i2 = n2.audio, o2 = n2.screenVideo, s2 = n2.screenAudio, a2 = n2.customVideo, c2 = n2.customAudio;
    t(this, e2), this.video = r2, this.audio = i2, this.screenVideo = o2, this.screenAudio = s2, this.customVideo = a2, this.customAudio = c2;
  }
  return o(e2, [{ key: "clone", value: function() {
    var t2 = new e2();
    return void 0 !== this.video && (t2.video = this.video), void 0 !== this.audio && (t2.audio = this.audio), void 0 !== this.screenVideo && (t2.screenVideo = this.screenVideo), void 0 !== this.screenAudio && (t2.screenAudio = this.screenAudio), void 0 !== this.customVideo && (t2.customVideo = Oa({}, this.customVideo)), void 0 !== this.customAudio && (t2.customAudio = Oa({}, this.customAudio)), t2;
  } }, { key: "toJSONObject", value: function() {
    var e3 = {};
    return void 0 !== this.video && (e3.video = this.video), void 0 !== this.audio && (e3.audio = this.audio), void 0 !== this.screenVideo && (e3.screenVideo = this.screenVideo), void 0 !== this.screenAudio && (e3.screenAudio = this.screenAudio), void 0 !== this.customVideo && (e3.customVideo = Oa({}, this.customVideo)), void 0 !== this.customAudio && (e3.customAudio = Oa({}, this.customAudio)), e3;
  } }, { key: "toMinifiedJSONObject", value: function() {
    var e3 = {};
    return void 0 !== this.video && (e3.v = this.video), void 0 !== this.audio && (e3.a = this.audio), void 0 !== this.screenVideo && (e3.sv = this.screenVideo), void 0 !== this.screenAudio && (e3.sa = this.screenAudio), void 0 !== this.customVideo && (e3.cv = Oa({}, this.customVideo)), void 0 !== this.customAudio && (e3.ca = Oa({}, this.customAudio)), e3;
  } }, { key: "normalize", value: function() {
    function e3(e4, t2) {
      return e4 && 1 === Object.keys(e4).length && e4["*"] === t2;
    }
    return !(true !== this.video || true !== this.audio || true !== this.screenVideo || true !== this.screenAudio || !e3(this.customVideo, true) || !e3(this.customAudio, true)) || (false !== this.video || false !== this.audio || false !== this.screenVideo || false !== this.screenAudio || !e3(this.customVideo, false) || !e3(this.customAudio, false)) && this;
  } }], [{ key: "fromBoolean", value: function(t2) {
    return new e2({ video: t2, audio: t2, screenVideo: t2, screenAudio: t2, customVideo: { "*": t2 }, customAudio: { "*": t2 } });
  } }, { key: "fromJSONObject", value: function(t2) {
    return new e2({ video: t2.video, audio: t2.audio, screenVideo: t2.screenVideo, screenAudio: t2.screenAudio, customVideo: void 0 !== t2.customVideo ? Oa({}, t2.customVideo) : void 0, customAudio: void 0 !== t2.customAudio ? Oa({}, t2.customAudio) : void 0 });
  } }, { key: "fromMinifiedJSONObject", value: function(t2) {
    return new e2({ video: t2.v, audio: t2.a, screenVideo: t2.sv, screenAudio: t2.sa, customVideo: t2.cv, customAudio: t2.ca });
  } }, { key: "validateJSONObject", value: function(e3, t2) {
    if ("boolean" == typeof e3) return [true];
    if ("object" !== n(e3)) return [false, "invalid (non-object, non-boolean) value in canReceive"];
    for (var r2 = Object.keys(e3), i2 = 0, o2 = r2; i2 < o2.length; i2++) {
      var s2 = o2[i2];
      if (!ja.includes(s2)) return [false, "invalid media type '".concat(s2, "' in canReceive")];
      if (xa.includes(s2)) {
        if ("boolean" != typeof e3[s2]) return [false, "invalid (non-boolean) value for media type '".concat(s2, "' in canReceive")];
      } else {
        if ("object" !== n(e3[s2])) return [false, "invalid (non-object) value for media type '".concat(s2, "' in canReceive")];
        for (var a2 = 0, c2 = Object.values(e3[s2]); a2 < c2.length; a2++) {
          if ("boolean" != typeof c2[a2]) return [false, "invalid (non-boolean) value for entry within '".concat(s2, "' in canReceive")];
        }
        if (t2 && void 0 === e3[s2]["*"]) return [false, `canReceive "base" permission must specify "*" as an entry within '`.concat(s2, "'")];
      }
    }
    return t2 && r2.length !== ja.length ? [false, 'canReceive "base" permission must specify all media types: '.concat(ja.join(", "), " (or be set to a boolean shorthand)")] : [true];
  } }]);
}();
var Fa = ["result"];
var Ba = ["preserveIframe"];
function Ua(e2, t2) {
  var n2 = Object.keys(e2);
  if (Object.getOwnPropertySymbols) {
    var r2 = Object.getOwnPropertySymbols(e2);
    t2 && (r2 = r2.filter(function(t3) {
      return Object.getOwnPropertyDescriptor(e2, t3).enumerable;
    })), n2.push.apply(n2, r2);
  }
  return n2;
}
function Va(e2) {
  for (var t2 = 1; t2 < arguments.length; t2++) {
    var n2 = null != arguments[t2] ? arguments[t2] : {};
    t2 % 2 ? Ua(Object(n2), true).forEach(function(t3) {
      u(e2, t3, n2[t3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e2, Object.getOwnPropertyDescriptors(n2)) : Ua(Object(n2)).forEach(function(t3) {
      Object.defineProperty(e2, t3, Object.getOwnPropertyDescriptor(n2, t3));
    });
  }
  return e2;
}
function Ja() {
  try {
    var e2 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (e3) {
  }
  return (Ja = function() {
    return !!e2;
  })();
}
function $a(e2, t2) {
  var n2 = "undefined" != typeof Symbol && e2[Symbol.iterator] || e2["@@iterator"];
  if (!n2) {
    if (Array.isArray(e2) || (n2 = function(e3, t3) {
      if (e3) {
        if ("string" == typeof e3) return qa(e3, t3);
        var n3 = {}.toString.call(e3).slice(8, -1);
        return "Object" === n3 && e3.constructor && (n3 = e3.constructor.name), "Map" === n3 || "Set" === n3 ? Array.from(e3) : "Arguments" === n3 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n3) ? qa(e3, t3) : void 0;
      }
    }(e2)) || t2 && e2 && "number" == typeof e2.length) {
      n2 && (e2 = n2);
      var r2 = 0, i2 = function() {
      };
      return { s: i2, n: function() {
        return r2 >= e2.length ? { done: true } : { done: false, value: e2[r2++] };
      }, e: function(e3) {
        throw e3;
      }, f: i2 };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var o2, s2 = true, a2 = false;
  return { s: function() {
    n2 = n2.call(e2);
  }, n: function() {
    var e3 = n2.next();
    return s2 = e3.done, e3;
  }, e: function(e3) {
    a2 = true, o2 = e3;
  }, f: function() {
    try {
      s2 || null == n2.return || n2.return();
    } finally {
      if (a2) throw o2;
    }
  } };
}
function qa(e2, t2) {
  (null == t2 || t2 > e2.length) && (t2 = e2.length);
  for (var n2 = 0, r2 = Array(t2); n2 < t2; n2++) r2[n2] = e2[n2];
  return r2;
}
var za = {};
var Wa = "video";
var Ha = "voice";
var Ga = ys() ? { data: {} } : { data: {}, topology: "none" };
var Qa = { present: 0, hidden: 0 };
var Ka = { maxBitrate: { min: 1e5, max: 25e5 }, maxFramerate: { min: 1, max: 30 }, scaleResolutionDownBy: { min: 1, max: 8 } };
var Ya = Object.keys(Ka);
var Xa = ["state", "volume", "simulcastEncodings"];
var Za = { androidInCallNotification: { title: "string", subtitle: "string", iconName: "string", disableForCustomOverride: "boolean" }, disableAutoDeviceManagement: { audio: "boolean", video: "boolean" } };
var ec = { id: { iconPath: "string", iconPathDarkMode: "string", label: "string", tooltip: "string", visualState: "'default' | 'sidebar-open' | 'active'" } };
var tc = { id: { allow: "string", controlledBy: "'*' | 'owners' | string[]", csp: "string", iconURL: "string", label: "string", loading: "'eager' | 'lazy'", location: "'main' | 'sidebar'", name: "string", referrerPolicy: "string", sandbox: "string", src: "string", srcdoc: "string", shared: "string[] | 'owners' | boolean" } };
var nc = { customIntegrations: { validate: Ec, help: Mc() }, customTrayButtons: { validate: Cc, help: "customTrayButtons should be a dictionary of the type ".concat(JSON.stringify(ec)) }, url: { validate: function(e2) {
  return "string" == typeof e2;
}, help: "url should be a string" }, baseUrl: { validate: function(e2) {
  return "string" == typeof e2;
}, help: "baseUrl should be a string" }, token: { validate: function(e2) {
  return "string" == typeof e2;
}, help: "token should be a string", queryString: "t" }, dailyConfig: { validate: function(e2, t2) {
  try {
    return t2.validateDailyConfig(e2), true;
  } catch (e3) {
    console.error("Failed to validate dailyConfig", e3);
  }
  return false;
}, help: "Unsupported dailyConfig. Check error logs for detailed info." }, reactNativeConfig: { validate: function(e2) {
  return Tc(e2, Za);
}, help: "reactNativeConfig should look like ".concat(JSON.stringify(Za), ", all fields optional") }, lang: { validate: function(e2) {
  return ["da", "de", "en-us", "en", "es", "fi", "fr", "it", "jp", "ka", "nl", "no", "pl", "pt", "pt-BR", "ru", "sv", "tr", "user"].includes(e2);
}, help: "language not supported. Options are: da, de, en-us, en, es, fi, fr, it, jp, ka, nl, no, pl, pt, pt-BR, ru, sv, tr, user" }, userName: true, userData: { validate: function(e2) {
  try {
    return vc(e2), true;
  } catch (e3) {
    return console.error(e3), false;
  }
}, help: "invalid userData type provided" }, startVideoOff: true, startAudioOff: true, allowLocalVideo: true, allowLocalAudio: true, activeSpeakerMode: true, showLeaveButton: true, showLocalVideo: true, showParticipantsBar: true, showFullscreenButton: true, showUserNameChangeUI: true, iframeStyle: true, customLayout: true, cssFile: true, cssText: true, bodyClass: true, videoSource: { validate: function(e2, t2) {
  if ("boolean" == typeof e2) return t2._preloadCache.allowLocalVideo = e2, true;
  var n2;
  if (e2 instanceof MediaStreamTrack) t2._sharedTracks.videoTrack = e2, n2 = { customTrack: ls };
  else {
    if (delete t2._sharedTracks.videoTrack, "string" != typeof e2) return console.error("videoSource must be a MediaStreamTrack, boolean, or a string"), false;
    n2 = { deviceId: e2 };
  }
  return t2._updatePreloadCacheInputSettings({ video: { settings: n2 } }, false), true;
} }, audioSource: { validate: function(e2, t2) {
  if ("boolean" == typeof e2) return t2._preloadCache.allowLocalAudio = e2, true;
  var n2;
  if (e2 instanceof MediaStreamTrack) t2._sharedTracks.audioTrack = e2, n2 = { customTrack: ls };
  else {
    if (delete t2._sharedTracks.audioTrack, "string" != typeof e2) return console.error("audioSource must be a MediaStreamTrack, boolean, or a string"), false;
    n2 = { deviceId: e2 };
  }
  return t2._updatePreloadCacheInputSettings({ audio: { settings: n2 } }, false), true;
} }, subscribeToTracksAutomatically: { validate: function(e2, t2) {
  return t2._preloadCache.subscribeToTracksAutomatically = e2, true;
} }, theme: { validate: function(e2) {
  var t2 = ["accent", "accentText", "background", "backgroundAccent", "baseText", "border", "mainAreaBg", "mainAreaBgAccent", "mainAreaText", "supportiveText"], r2 = function(e3) {
    for (var n2 = 0, r3 = Object.keys(e3); n2 < r3.length; n2++) {
      var i2 = r3[n2];
      if (!t2.includes(i2)) return console.error('unsupported color "'.concat(i2, '". Valid colors: ').concat(t2.join(", "))), false;
      if (!e3[i2].match(/^#[0-9a-f]{6}|#[0-9a-f]{3}$/i)) return console.error("".concat(i2, ' theme color should be provided in valid hex color format. Received: "').concat(e3[i2], '"')), false;
    }
    return true;
  };
  return "object" === n(e2) && ("light" in e2 && "dark" in e2 || "colors" in e2) ? "light" in e2 && "dark" in e2 ? "colors" in e2.light ? "colors" in e2.dark ? r2(e2.light.colors) && r2(e2.dark.colors) : (console.error('Dark theme is missing "colors" property.', e2), false) : (console.error('Light theme is missing "colors" property.', e2), false) : r2(e2.colors) : (console.error('Theme must contain either both "light" and "dark" properties, or "colors".', e2), false);
}, help: "unsupported theme configuration. Check error logs for detailed info." }, layoutConfig: { validate: function(e2) {
  if ("grid" in e2) {
    var t2 = e2.grid;
    if ("maxTilesPerPage" in t2) {
      if (!Number.isInteger(t2.maxTilesPerPage)) return console.error("grid.maxTilesPerPage should be an integer. You passed ".concat(t2.maxTilesPerPage, ".")), false;
      if (t2.maxTilesPerPage > 49) return console.error("grid.maxTilesPerPage can't be larger than 49 without sacrificing browser performance. Please contact us at https://www.daily.co/contact to talk about your use case."), false;
    }
    if ("minTilesPerPage" in t2) {
      if (!Number.isInteger(t2.minTilesPerPage)) return console.error("grid.minTilesPerPage should be an integer. You passed ".concat(t2.minTilesPerPage, ".")), false;
      if (t2.minTilesPerPage < 1) return console.error("grid.minTilesPerPage can't be lower than 1."), false;
      if ("maxTilesPerPage" in t2 && t2.minTilesPerPage > t2.maxTilesPerPage) return console.error("grid.minTilesPerPage can't be higher than grid.maxTilesPerPage."), false;
    }
  }
  return true;
}, help: "unsupported layoutConfig. Check error logs for detailed info." }, receiveSettings: { validate: function(e2) {
  return gc(e2, { allowAllParticipantsKey: false });
}, help: kc({ allowAllParticipantsKey: false }) }, sendSettings: { validate: function(e2, t2) {
  return !!function(e3, t3) {
    try {
      return t3.validateUpdateSendSettings(e3), true;
    } catch (e4) {
      return console.error("Failed to validate send settings", e4), false;
    }
  }(e2, t2) && (t2._preloadCache.sendSettings = e2, true);
}, help: "Invalid sendSettings provided. Check error logs for detailed info." }, inputSettings: { validate: function(e2, t2) {
  var n2;
  return !!mc(e2) && (t2._inputSettings || (t2._inputSettings = {}), yc(e2, null === (n2 = t2.properties) || void 0 === n2 ? void 0 : n2.dailyConfig, t2._sharedTracks), t2._updatePreloadCacheInputSettings(e2, true), true);
}, help: Sc() }, layout: { validate: function(e2) {
  return "custom-v1" === e2 || "browser" === e2 || "none" === e2;
}, help: 'layout may only be set to "custom-v1"', queryString: "layout" }, emb: { queryString: "emb" }, embHref: { queryString: "embHref" }, dailyJsVersion: { queryString: "dailyJsVersion" }, proxy: { queryString: "proxy" }, strictMode: true, allowMultipleCallInstances: true };
var rc = { styles: { validate: function(e2) {
  for (var t2 in e2) if ("cam" !== t2 && "screen" !== t2) return false;
  if (e2.cam) {
    for (var n2 in e2.cam) if ("div" !== n2 && "video" !== n2) return false;
  }
  if (e2.screen) {
    for (var r2 in e2.screen) if ("div" !== r2 && "video" !== r2) return false;
  }
  return true;
}, help: "styles format should be a subset of: { cam: {div: {}, video: {}}, screen: {div: {}, video: {}} }" }, setSubscribedTracks: { validate: function(e2, t2) {
  if (t2._preloadCache.subscribeToTracksAutomatically) return false;
  var n2 = [true, false, "staged"];
  if (n2.includes(e2) || !ys() && "avatar" === e2) return true;
  var r2 = ["audio", "video", "screenAudio", "screenVideo", "rmpAudio", "rmpVideo"], i2 = function(e3) {
    var t3 = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
    for (var o2 in e3) if ("custom" === o2) {
      if (!n2.includes(e3[o2]) && !i2(e3[o2], true)) return false;
    } else {
      var s2 = !t3 && !r2.includes(o2), a2 = !n2.includes(e3[o2]);
      if (s2 || a2) return false;
    }
    return true;
  };
  return i2(e2);
}, help: "setSubscribedTracks cannot be used when setSubscribeToTracksAutomatically is enabled, and should be of the form: " + "true".concat(ys() ? "" : " | 'avatar'", " | false | 'staged' | { [audio: true|false|'staged'], [video: true|false|'staged'], [screenAudio: true|false|'staged'], [screenVideo: true|false|'staged'] }") }, setAudio: true, setVideo: true, setScreenShare: { validate: function(e2) {
  return false === e2;
}, help: "setScreenShare must be false, as it's only meant for stopping remote participants' screen shares" }, eject: true, updatePermissions: { validate: function(e2) {
  for (var t2 = 0, n2 = Object.entries(e2); t2 < n2.length; t2++) {
    var r2 = f(n2[t2], 2), i2 = r2[0], o2 = r2[1];
    switch (i2) {
      case "hasPresence":
        if ("boolean" != typeof o2) return false;
        break;
      case "canSend":
        if (o2 instanceof Set || o2 instanceof Array || Array.isArray(o2)) {
          var s2, a2 = ["video", "audio", "screenVideo", "screenAudio", "customVideo", "customAudio"], c2 = $a(o2);
          try {
            for (c2.s(); !(s2 = c2.n()).done; ) {
              var l2 = s2.value;
              if (!a2.includes(l2)) return false;
            }
          } catch (e3) {
            c2.e(e3);
          } finally {
            c2.f();
          }
        } else if ("boolean" != typeof o2) return false;
        (o2 instanceof Array || Array.isArray(o2)) && (e2.canSend = new Set(o2));
        break;
      case "canReceive":
        var u2 = f(Na.validateJSONObject(o2), 2), d2 = u2[0], p2 = u2[1];
        if (!d2) return console.error(p2), false;
        break;
      case "canAdmin":
        if (o2 instanceof Set || o2 instanceof Array || Array.isArray(o2)) {
          var h2, v2 = ["participants", "streaming", "transcription"], g2 = $a(o2);
          try {
            for (g2.s(); !(h2 = g2.n()).done; ) {
              var m2 = h2.value;
              if (!v2.includes(m2)) return false;
            }
          } catch (e3) {
            g2.e(e3);
          } finally {
            g2.f();
          }
        } else if ("boolean" != typeof o2) return false;
        (o2 instanceof Array || Array.isArray(o2)) && (e2.canAdmin = new Set(o2));
        break;
      default:
        return false;
    }
  }
  return true;
}, help: "updatePermissions can take hasPresence, canSend, canReceive, and canAdmin permissions. hasPresence must be a boolean. canSend can be a boolean or an Array or Set of media types (video, audio, screenVideo, screenAudio, customVideo, customAudio). canReceive must be an object specifying base, byUserId, and/or byParticipantId fields (see documentation for more details). canAdmin can be a boolean or an Array or Set of admin types (participants, streaming, transcription)." } };
Promise.any || (Promise.any = function() {
  var e2 = p(function* (e3) {
    return new Promise(function(t2, n2) {
      var r2 = [];
      e3.forEach(function(i2) {
        return Promise.resolve(i2).then(function(e4) {
          t2(e4);
        }).catch(function(t3) {
          r2.push(t3), r2.length === e3.length && n2(r2);
        });
      });
    });
  });
  return function(t2) {
    return e2.apply(this, arguments);
  };
}());
var ic = function() {
  function r2(e2) {
    var n2, i3, o2, c3, l2, d3, h3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
    if (t(this, r2), o2 = this, c3 = a(c3 = r2), u(i3 = s(o2, Ja() ? Reflect.construct(c3, l2 || [], a(o2).constructor) : c3.apply(o2, l2)), "startListeningForDeviceChanges", function() {
      Sa(i3.handleDeviceChange);
    }), u(i3, "stopListeningForDeviceChanges", function() {
      ka(i3.handleDeviceChange);
    }), u(i3, "handleDeviceChange", function(e3) {
      e3 = e3.map(function(e4) {
        return JSON.parse(JSON.stringify(e4));
      }), i3.emitDailyJSEvent({ action: "available-devices-updated", availableDevices: e3 });
    }), u(i3, "handleNativeAppStateChange", function() {
      var e3 = p(function* (e4) {
        if ("destroyed" === e4) return console.warn("App has been destroyed before leaving the meeting. Cleaning up all the resources!"), void (yield i3.destroy());
        var t2 = "active" === e4;
        i3.disableReactNativeAutoDeviceManagement("video") || (t2 ? i3.camUnmutedBeforeLosingNativeActiveState && i3.setLocalVideo(true) : (i3.camUnmutedBeforeLosingNativeActiveState = i3.localVideo(), i3.camUnmutedBeforeLosingNativeActiveState && i3.setLocalVideo(false)));
      });
      return function(t2) {
        return e3.apply(this, arguments);
      };
    }()), u(i3, "handleNativeAudioFocusChange", function(e3) {
      i3.disableReactNativeAutoDeviceManagement("audio") || (i3._hasNativeAudioFocus = e3, i3.toggleParticipantAudioBasedOnNativeAudioFocus(), i3._hasNativeAudioFocus ? i3.micUnmutedBeforeLosingNativeAudioFocus && i3.setLocalAudio(true) : (i3.micUnmutedBeforeLosingNativeAudioFocus = i3.localAudio(), i3.setLocalAudio(false)));
    }), u(i3, "handleNativeSystemScreenCaptureStop", function() {
      i3.stopScreenShare();
    }), i3.strictMode = void 0 === h3.strictMode || h3.strictMode, i3.allowMultipleCallInstances = null !== (n2 = h3.allowMultipleCallInstances) && void 0 !== n2 && n2, Object.keys(za).length && (i3._logDuplicateInstanceAttempt(), !i3.allowMultipleCallInstances)) {
      if (i3.strictMode) throw new Error("Duplicate DailyIframe instances are not allowed");
      console.warn("Using strictMode: false to allow multiple call instances is now deprecated. Set `allowMultipleCallInstances: true`");
    }
    if (window._daily || (window._daily = { pendings: [], instances: {} }), i3.callClientId = K(), za[(d3 = i3).callClientId] = d3, window._daily.instances[i3.callClientId] = {}, i3._sharedTracks = {}, window._daily.instances[i3.callClientId].tracks = i3._sharedTracks, h3.dailyJsVersion = r2.version(), i3._iframe = e2, i3._callObjectMode = "none" === h3.layout && !i3._iframe, i3._preloadCache = { subscribeToTracksAutomatically: true, outputDeviceId: null, inputSettings: null, sendSettings: null, videoTrackForNetworkConnectivityTest: null, videoTrackForConnectionQualityTest: null }, void 0 !== h3.showLocalVideo ? i3._callObjectMode ? console.error("showLocalVideo is not available in call object mode") : i3._showLocalVideo = !!h3.showLocalVideo : i3._showLocalVideo = true, void 0 !== h3.showParticipantsBar ? i3._callObjectMode ? console.error("showParticipantsBar is not available in call object mode") : i3._showParticipantsBar = !!h3.showParticipantsBar : i3._showParticipantsBar = true, void 0 !== h3.customIntegrations ? i3._callObjectMode ? console.error("customIntegrations is not available in call object mode") : i3._customIntegrations = h3.customIntegrations : i3._customIntegrations = {}, void 0 !== h3.customTrayButtons ? i3._callObjectMode ? console.error("customTrayButtons is not available in call object mode") : i3._customTrayButtons = h3.customTrayButtons : i3._customTrayButtons = {}, void 0 !== h3.activeSpeakerMode ? i3._callObjectMode ? console.error("activeSpeakerMode is not available in call object mode") : i3._activeSpeakerMode = !!h3.activeSpeakerMode : i3._activeSpeakerMode = false, h3.receiveSettings ? i3._callObjectMode ? i3._receiveSettings = h3.receiveSettings : console.error("receiveSettings is only available in call object mode") : i3._receiveSettings = {}, i3.validateProperties(h3), i3.properties = Va({}, h3), i3._inputSettings || (i3._inputSettings = {}), i3._callObjectLoader = i3._callObjectMode ? new Ys(i3.callClientId) : null, i3._callState = vi, i3._isPreparingToJoin = false, i3._accessState = { access: Oi }, i3._meetingSessionSummary = {}, i3._finalSummaryOfPrevSession = {}, i3._meetingSessionState = jc(Ga, i3._callObjectMode), i3._nativeInCallAudioMode = Wa, i3._participants = {}, i3._isScreenSharing = false, i3._participantCounts = Qa, i3._rmpPlayerState = {}, i3._waitingParticipants = {}, i3._network = { threshold: "good", quality: 100, networkState: "unknown", stats: {} }, i3._activeSpeaker = {}, i3._localAudioLevel = 0, i3._isLocalAudioLevelObserverRunning = false, i3._remoteParticipantsAudioLevel = {}, i3._isRemoteParticipantsAudioLevelObserverRunning = false, i3._maxAppMessageSize = rs, i3._messageChannel = ys() ? new Js() : new Bs(), i3._iframe && (i3._iframe.requestFullscreen ? i3._iframe.addEventListener("fullscreenchange", function() {
      document.fullscreenElement === i3._iframe ? (i3.emitDailyJSEvent({ action: Wo }), i3.sendMessageToCallMachine({ action: Wo })) : (i3.emitDailyJSEvent({ action: Ho }), i3.sendMessageToCallMachine({ action: Ho }));
    }) : i3._iframe.webkitRequestFullscreen && i3._iframe.addEventListener("webkitfullscreenchange", function() {
      document.webkitFullscreenElement === i3._iframe ? (i3.emitDailyJSEvent({ action: Wo }), i3.sendMessageToCallMachine({ action: Wo })) : (i3.emitDailyJSEvent({ action: Ho }), i3.sendMessageToCallMachine({ action: Ho }));
    })), ys()) {
      var f2 = i3.nativeUtils();
      f2.addAudioFocusChangeListener && f2.removeAudioFocusChangeListener && f2.addAppStateChangeListener && f2.removeAppStateChangeListener && f2.addSystemScreenCaptureStopListener && f2.removeSystemScreenCaptureStopListener || console.warn("expected (add|remove)(AudioFocusChange|AppActiveStateChange|SystemScreenCaptureStop)Listener to be available in React Native"), i3._hasNativeAudioFocus = true, f2.addAudioFocusChangeListener(i3.handleNativeAudioFocusChange), f2.addAppStateChangeListener(i3.handleNativeAppStateChange), f2.addSystemScreenCaptureStopListener(i3.handleNativeSystemScreenCaptureStop);
    }
    return i3._callObjectMode && i3.startListeningForDeviceChanges(), i3._messageChannel.addListenerForMessagesFromCallMachine(i3.handleMessageFromCallMachine, i3.callClientId, i3), i3;
  }
  return l(r2, x), o(r2, [{ key: "destroy", value: (ee2 = p(function* () {
    var e2;
    try {
      yield this.leave();
    } catch (e3) {
    }
    var t2 = this._iframe;
    if (t2) {
      var n2 = t2.parentElement;
      n2 && n2.removeChild(t2);
    }
    if (this._messageChannel.removeListener(this.handleMessageFromCallMachine), ys()) {
      var r3 = this.nativeUtils();
      r3.removeAudioFocusChangeListener(this.handleNativeAudioFocusChange), r3.removeAppStateChangeListener(this.handleNativeAppStateChange), r3.removeSystemScreenCaptureStopListener(this.handleNativeSystemScreenCaptureStop);
    }
    this._callObjectMode && this.stopListeningForDeviceChanges(), this.resetMeetingDependentVars(), this._destroyed = true, this.emitDailyJSEvent({ action: "call-instance-destroyed" }), delete za[this.callClientId], (null === (e2 = window) || void 0 === e2 || null === (e2 = e2._daily) || void 0 === e2 ? void 0 : e2.instances) && delete window._daily.instances[this.callClientId], this.strictMode && (this.callClientId = void 0);
  }), function() {
    return ee2.apply(this, arguments);
  }) }, { key: "isDestroyed", value: function() {
    return !!this._destroyed;
  } }, { key: "loadCss", value: function(e2) {
    var t2 = e2.bodyClass, n2 = e2.cssFile, r3 = e2.cssText;
    return hc(), this.sendMessageToCallMachine({ action: "load-css", cssFile: this.absoluteUrl(n2), bodyClass: t2, cssText: r3 }), this;
  } }, { key: "iframe", value: function() {
    return hc(), this._iframe;
  } }, { key: "meetingState", value: function() {
    return this._callState;
  } }, { key: "accessState", value: function() {
    return dc(this._callObjectMode, "accessState()"), this._accessState;
  } }, { key: "participants", value: function() {
    return this._participants;
  } }, { key: "participantCounts", value: function() {
    return this._participantCounts;
  } }, { key: "waitingParticipants", value: function() {
    return dc(this._callObjectMode, "waitingParticipants()"), this._waitingParticipants;
  } }, { key: "validateParticipantProperties", value: function(e2, t2) {
    for (var n2 in t2) {
      if (!rc[n2]) throw new Error("unrecognized updateParticipant property ".concat(n2));
      if (rc[n2].validate && !rc[n2].validate(t2[n2], this, this._participants[e2])) throw new Error(rc[n2].help);
    }
  } }, { key: "updateParticipant", value: function(e2, t2) {
    return this._participants.local && this._participants.local.session_id === e2 && (e2 = "local"), e2 && t2 && (this.validateParticipantProperties(e2, t2), this.sendMessageToCallMachine({ action: "update-participant", id: e2, properties: t2 })), this;
  } }, { key: "updateParticipants", value: function(e2) {
    var t2 = this._participants.local && this._participants.local.session_id;
    for (var n2 in e2) n2 === t2 && (n2 = "local"), n2 && e2[n2] && this.validateParticipantProperties(n2, e2[n2]);
    return this.sendMessageToCallMachine({ action: "update-participants", participants: e2 }), this;
  } }, { key: "updateWaitingParticipant", value: (Y2 = p(function* () {
    var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "", r3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
    if (dc(this._callObjectMode, "updateWaitingParticipant()"), sc(this._callState, "updateWaitingParticipant()"), "string" != typeof t2 || "object" !== n(r3)) throw new Error("updateWaitingParticipant() must take an id string and a updates object");
    return new Promise(function(n2, i3) {
      e2.sendMessageToCallMachine({ action: "daily-method-update-waiting-participant", id: t2, updates: r3 }, function(e3) {
        e3.error && i3(e3.error), e3.id || i3(new Error("unknown error in updateWaitingParticipant()")), n2({ id: e3.id });
      });
    });
  }), function() {
    return Y2.apply(this, arguments);
  }) }, { key: "updateWaitingParticipants", value: (G2 = p(function* () {
    var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
    if (dc(this._callObjectMode, "updateWaitingParticipants()"), sc(this._callState, "updateWaitingParticipants()"), "object" !== n(t2)) throw new Error("updateWaitingParticipants() must take a mapping between ids and update objects");
    return new Promise(function(n2, r3) {
      e2.sendMessageToCallMachine({ action: "daily-method-update-waiting-participants", updatesById: t2 }, function(e3) {
        e3.error && r3(e3.error), e3.ids || r3(new Error("unknown error in updateWaitingParticipants()")), n2({ ids: e3.ids });
      });
    });
  }), function() {
    return G2.apply(this, arguments);
  }) }, { key: "requestAccess", value: (H2 = p(function* () {
    var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, n2 = t2.access, r3 = void 0 === n2 ? { level: Pi } : n2, i3 = t2.name, o2 = void 0 === i3 ? "" : i3;
    return dc(this._callObjectMode, "requestAccess()"), sc(this._callState, "requestAccess()"), new Promise(function(t3, n3) {
      e2.sendMessageToCallMachine({ action: "daily-method-request-access", access: r3, name: o2 }, function(e3) {
        e3.error && n3(e3.error), e3.access || n3(new Error("unknown error in requestAccess()")), t3({ access: e3.access, granted: e3.granted });
      });
    });
  }), function() {
    return H2.apply(this, arguments);
  }) }, { key: "localAudio", value: function() {
    return this._participants.local ? !["blocked", "off"].includes(this._participants.local.tracks.audio.state) : null;
  } }, { key: "localVideo", value: function() {
    return this._participants.local ? !["blocked", "off"].includes(this._participants.local.tracks.video.state) : null;
  } }, { key: "setLocalAudio", value: function(e2) {
    var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
    return "forceDiscardTrack" in t2 && (ys() ? (console.warn("forceDiscardTrack option not supported in React Native; ignoring"), t2 = {}) : e2 && (console.warn("forceDiscardTrack option only supported when calling setLocalAudio(false); ignoring"), t2 = {})), this.sendMessageToCallMachine({ action: "local-audio", state: e2, options: t2 }), this;
  } }, { key: "localScreenAudio", value: function() {
    return this._participants.local ? !["blocked", "off"].includes(this._participants.local.tracks.screenAudio.state) : null;
  } }, { key: "localScreenVideo", value: function() {
    return this._participants.local ? !["blocked", "off"].includes(this._participants.local.tracks.screenVideo.state) : null;
  } }, { key: "updateScreenShare", value: function(e2) {
    if (this._isScreenSharing) return this.sendMessageToCallMachine({ action: "local-screen-update", options: e2 }), this;
    console.warn("There is no screen share in progress. Try calling startScreenShare first.");
  } }, { key: "setLocalVideo", value: function(e2) {
    return this.sendMessageToCallMachine({ action: "local-video", state: e2 }), this;
  } }, { key: "_setAllowLocalAudio", value: function(e2) {
    if (this._preloadCache.allowLocalAudio = e2, this._callMachineInitialized) return this.sendMessageToCallMachine({ action: "set-allow-local-audio", state: e2 }), this;
  } }, { key: "_setAllowLocalVideo", value: function(e2) {
    if (this._preloadCache.allowLocalVideo = e2, this._callMachineInitialized) return this.sendMessageToCallMachine({ action: "set-allow-local-video", state: e2 }), this;
  } }, { key: "getReceiveSettings", value: (W2 = p(function* (e2) {
    var t2 = this, r3 = (arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}).showInheritedValues, i3 = void 0 !== r3 && r3;
    if (dc(this._callObjectMode, "getReceiveSettings()"), !this._callMachineInitialized) return this._receiveSettings;
    switch (n(e2)) {
      case "string":
        return new Promise(function(n2) {
          t2.sendMessageToCallMachine({ action: "get-single-participant-receive-settings", id: e2, showInheritedValues: i3 }, function(e3) {
            n2(e3.receiveSettings);
          });
        });
      case "undefined":
        return this._receiveSettings;
      default:
        throw new Error('first argument to getReceiveSettings() must be a participant id (or "base"), or there should be no arguments');
    }
  }), function(e2) {
    return W2.apply(this, arguments);
  }) }, { key: "updateReceiveSettings", value: (z2 = p(function* (e2) {
    var t2 = this;
    if (dc(this._callObjectMode, "updateReceiveSettings()"), !gc(e2, { allowAllParticipantsKey: true })) throw new Error(kc({ allowAllParticipantsKey: true }));
    return sc(this._callState, "updateReceiveSettings()", "To specify receive settings earlier, use the receiveSettings config property."), new Promise(function(n2) {
      t2.sendMessageToCallMachine({ action: "update-receive-settings", receiveSettings: e2 }, function(e3) {
        n2({ receiveSettings: e3.receiveSettings });
      });
    });
  }), function(e2) {
    return z2.apply(this, arguments);
  }) }, { key: "_prepInputSettingsForSharing", value: function(e2, t2) {
    if (e2) {
      var n2 = {};
      if (e2.audio) {
        var r3, i3, o2;
        e2.audio.settings && (!Object.keys(e2.audio.settings).length && t2 || (n2.audio = { settings: Va({}, e2.audio.settings) })), t2 && null !== (r3 = n2.audio) && void 0 !== r3 && null !== (r3 = r3.settings) && void 0 !== r3 && r3.customTrack && (n2.audio.settings = { customTrack: this._sharedTracks.audioTrack });
        var s2 = "none" === (null === (i3 = e2.audio.processor) || void 0 === i3 ? void 0 : i3.type) && (null === (o2 = e2.audio.processor) || void 0 === o2 ? void 0 : o2._isDefaultWhenNone);
        if (e2.audio.processor && !s2) {
          var a2 = Va({}, e2.audio.processor);
          delete a2._isDefaultWhenNone, n2.audio = Va(Va({}, n2.audio), {}, { processor: a2 });
        }
      }
      if (e2.video) {
        var c3, l2, u2;
        e2.video.settings && (!Object.keys(e2.video.settings).length && t2 || (n2.video = { settings: Va({}, e2.video.settings) })), t2 && null !== (c3 = n2.video) && void 0 !== c3 && null !== (c3 = c3.settings) && void 0 !== c3 && c3.customTrack && (n2.video.settings = { customTrack: this._sharedTracks.videoTrack });
        var d3 = "none" === (null === (l2 = e2.video.processor) || void 0 === l2 ? void 0 : l2.type) && (null === (u2 = e2.video.processor) || void 0 === u2 ? void 0 : u2._isDefaultWhenNone);
        if (e2.video.processor && !d3) {
          var p2 = Va({}, e2.video.processor);
          delete p2._isDefaultWhenNone, n2.video = Va(Va({}, n2.video), {}, { processor: p2 });
        }
      }
      return n2;
    }
  } }, { key: "getInputSettings", value: function() {
    var e2 = this;
    return hc(), new Promise(function(t2) {
      t2(e2._getInputSettings());
    });
  } }, { key: "_getInputSettings", value: function() {
    var e2, t2, n2, r3, i3, o2, s2 = { processor: { type: "none", _isDefaultWhenNone: true } };
    this._inputSettings ? (e2 = (null === (n2 = this._inputSettings) || void 0 === n2 ? void 0 : n2.video) || s2, t2 = (null === (r3 = this._inputSettings) || void 0 === r3 ? void 0 : r3.audio) || s2) : (e2 = (null === (i3 = this._preloadCache) || void 0 === i3 || null === (i3 = i3.inputSettings) || void 0 === i3 ? void 0 : i3.video) || s2, t2 = (null === (o2 = this._preloadCache) || void 0 === o2 || null === (o2 = o2.inputSettings) || void 0 === o2 ? void 0 : o2.audio) || s2);
    var a2 = { audio: t2, video: e2 };
    return this._prepInputSettingsForSharing(a2, true);
  } }, { key: "_updatePreloadCacheInputSettings", value: function(e2, t2) {
    var n2 = this._inputSettings || {}, r3 = {};
    if (e2.video) {
      var i3, o2, s2;
      if (r3.video = {}, e2.video.settings) r3.video.settings = {}, t2 || e2.video.settings.customTrack || null === (s2 = n2.video) || void 0 === s2 || !s2.settings ? r3.video.settings = e2.video.settings : r3.video.settings = Va(Va({}, n2.video.settings), e2.video.settings), Object.keys(r3.video.settings).length || delete r3.video.settings;
      else null !== (i3 = n2.video) && void 0 !== i3 && i3.settings && (r3.video.settings = n2.video.settings);
      e2.video.processor ? r3.video.processor = e2.video.processor : null !== (o2 = n2.video) && void 0 !== o2 && o2.processor && (r3.video.processor = n2.video.processor);
    } else n2.video && (r3.video = n2.video);
    if (e2.audio) {
      var a2, c3, l2;
      if (r3.audio = {}, e2.audio.settings) r3.audio.settings = {}, t2 || e2.audio.settings.customTrack || null === (l2 = n2.audio) || void 0 === l2 || !l2.settings ? r3.audio.settings = e2.audio.settings : r3.audio.settings = Va(Va({}, n2.audio.settings), e2.audio.settings), Object.keys(r3.audio.settings).length || delete r3.audio.settings;
      else null !== (a2 = n2.audio) && void 0 !== a2 && a2.settings && (r3.audio.settings = n2.audio.settings);
      e2.audio.processor ? r3.audio.processor = e2.audio.processor : null !== (c3 = n2.audio) && void 0 !== c3 && c3.processor && (r3.audio.processor = n2.audio.processor);
    } else n2.audio && (r3.audio = n2.audio);
    this._maybeUpdateInputSettings(r3);
  } }, { key: "_devicesFromInputSettings", value: function(e2) {
    var t2, n2, r3 = (null == e2 || null === (t2 = e2.video) || void 0 === t2 || null === (t2 = t2.settings) || void 0 === t2 ? void 0 : t2.deviceId) || null, i3 = (null == e2 || null === (n2 = e2.audio) || void 0 === n2 || null === (n2 = n2.settings) || void 0 === n2 ? void 0 : n2.deviceId) || null, o2 = this._preloadCache.outputDeviceId || null;
    return { camera: r3 ? { deviceId: r3 } : {}, mic: i3 ? { deviceId: i3 } : {}, speaker: o2 ? { deviceId: o2 } : {} };
  } }, { key: "updateInputSettings", value: (q2 = p(function* (e2) {
    var t2 = this;
    return hc(), mc(e2) ? e2.video || e2.audio ? (yc(e2, this.properties.dailyConfig, this._sharedTracks), this._callObjectMode && !this._callMachineInitialized ? (this._updatePreloadCacheInputSettings(e2, true), this._getInputSettings()) : new Promise(function(n2, r3) {
      t2.sendMessageToCallMachine({ action: "update-input-settings", inputSettings: e2 }, function(i3) {
        if (i3.error) r3(i3.error);
        else {
          if (i3.returnPreloadCache) return t2._updatePreloadCacheInputSettings(e2, true), void n2(t2._getInputSettings());
          t2._maybeUpdateInputSettings(i3.inputSettings), n2(t2._prepInputSettingsForSharing(i3.inputSettings, true));
        }
      });
    })) : this._getInputSettings() : (console.error(Sc()), Promise.reject(Sc()));
  }), function(e2) {
    return q2.apply(this, arguments);
  }) }, { key: "setBandwidth", value: function(e2) {
    var t2 = e2.kbs, n2 = e2.trackConstraints;
    if (hc(), this._callMachineInitialized) return this.sendMessageToCallMachine({ action: "set-bandwidth", kbs: t2, trackConstraints: n2 }), this;
  } }, { key: "getDailyLang", value: function() {
    var e2 = this;
    if (hc(), this._callMachineInitialized) return new Promise(function(t2) {
      e2.sendMessageToCallMachine({ action: "get-daily-lang" }, function(e3) {
        delete e3.action, delete e3.callbackStamp, t2(e3);
      });
    });
  } }, { key: "setDailyLang", value: function(e2) {
    return hc(), this.sendMessageToCallMachine({ action: "set-daily-lang", lang: e2 }), this;
  } }, { key: "setProxyUrl", value: function(e2) {
    return this.sendMessageToCallMachine({ action: "set-proxy-url", proxyUrl: e2 }), this;
  } }, { key: "setIceConfig", value: function(e2) {
    return this.sendMessageToCallMachine({ action: "set-ice-config", iceConfig: e2 }), this;
  } }, { key: "meetingSessionSummary", value: function() {
    return [_i, wi].includes(this._callState) ? this._finalSummaryOfPrevSession : this._meetingSessionSummary;
  } }, { key: "getMeetingSession", value: ($2 = p(function* () {
    var e2 = this;
    return console.warn("getMeetingSession() is deprecated: use meetingSessionSummary(), which will return immediately"), sc(this._callState, "getMeetingSession()"), new Promise(function(t2) {
      e2.sendMessageToCallMachine({ action: "get-meeting-session" }, function(e3) {
        delete e3.action, delete e3.callbackStamp, t2(e3);
      });
    });
  }), function() {
    return $2.apply(this, arguments);
  }) }, { key: "meetingSessionState", value: function() {
    return sc(this._callState, "meetingSessionState"), this._meetingSessionState;
  } }, { key: "setMeetingSessionData", value: function(e2) {
    var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "replace";
    dc(this._callObjectMode, "setMeetingSessionData()"), sc(this._callState, "setMeetingSessionData");
    try {
      !function(e3, t3) {
        new Ws({ data: e3, mergeStrategy: t3 });
      }(e2, t2);
    } catch (e3) {
      throw console.error(e3), e3;
    }
    try {
      this.sendMessageToCallMachine({ action: "set-session-data", data: e2, mergeStrategy: t2 });
    } catch (e3) {
      throw new Error("Error setting meeting session data: ".concat(e3));
    }
  } }, { key: "setUserName", value: function(e2, t2) {
    var n2 = this;
    return this.properties.userName = e2, new Promise(function(r3) {
      n2.sendMessageToCallMachine({ action: "set-user-name", name: null != e2 ? e2 : "", thisMeetingOnly: ys() || !!t2 && !!t2.thisMeetingOnly }, function(e3) {
        delete e3.action, delete e3.callbackStamp, r3(e3);
      });
    });
  } }, { key: "setUserData", value: (J2 = p(function* (e2) {
    var t2 = this;
    try {
      vc(e2);
    } catch (e3) {
      throw console.error(e3), e3;
    }
    if (this.properties.userData = e2, this._callMachineInitialized) return new Promise(function(n2) {
      try {
        t2.sendMessageToCallMachine({ action: "set-user-data", userData: e2 }, function(e3) {
          delete e3.action, delete e3.callbackStamp, n2(e3);
        });
      } catch (e3) {
        throw new Error("Error setting user data: ".concat(e3));
      }
    });
  }), function(e2) {
    return J2.apply(this, arguments);
  }) }, { key: "validateAudioLevelInterval", value: function(e2) {
    if (e2 && (e2 < 100 || "number" != typeof e2)) throw new Error("The interval must be a number greater than or equal to 100 milliseconds.");
  } }, { key: "startLocalAudioLevelObserver", value: function(e2) {
    var t2 = this;
    if ("undefined" == typeof AudioWorkletNode && !ys()) throw new Error("startLocalAudioLevelObserver() is not supported on this browser");
    if (this.validateAudioLevelInterval(e2), this._callMachineInitialized) return this._isLocalAudioLevelObserverRunning = true, new Promise(function(n2, r3) {
      t2.sendMessageToCallMachine({ action: "start-local-audio-level-observer", interval: e2 }, function(e3) {
        t2._isLocalAudioLevelObserverRunning = !e3.error, e3.error ? r3({ error: e3.error }) : n2();
      });
    });
    this._preloadCache.localAudioLevelObserver = { enabled: true, interval: e2 };
  } }, { key: "isLocalAudioLevelObserverRunning", value: function() {
    return this._isLocalAudioLevelObserverRunning;
  } }, { key: "stopLocalAudioLevelObserver", value: function() {
    this._preloadCache.localAudioLevelObserver = null, this._localAudioLevel = 0, this._isLocalAudioLevelObserverRunning = false, this.sendMessageToCallMachine({ action: "stop-local-audio-level-observer" });
  } }, { key: "startRemoteParticipantsAudioLevelObserver", value: function(e2) {
    var t2 = this;
    if (this.validateAudioLevelInterval(e2), this._callMachineInitialized) return this._isRemoteParticipantsAudioLevelObserverRunning = true, new Promise(function(n2, r3) {
      t2.sendMessageToCallMachine({ action: "start-remote-participants-audio-level-observer", interval: e2 }, function(e3) {
        t2._isRemoteParticipantsAudioLevelObserverRunning = !e3.error, e3.error ? r3({ error: e3.error }) : n2();
      });
    });
    this._preloadCache.remoteParticipantsAudioLevelObserver = { enabled: true, interval: e2 };
  } }, { key: "isRemoteParticipantsAudioLevelObserverRunning", value: function() {
    return this._isRemoteParticipantsAudioLevelObserverRunning;
  } }, { key: "stopRemoteParticipantsAudioLevelObserver", value: function() {
    this._preloadCache.remoteParticipantsAudioLevelObserver = null, this._remoteParticipantsAudioLevel = {}, this._isRemoteParticipantsAudioLevelObserverRunning = false, this.sendMessageToCallMachine({ action: "stop-remote-participants-audio-level-observer" });
  } }, { key: "startCamera", value: (V2 = p(function* () {
    var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
    if (dc(this._callObjectMode, "startCamera()"), cc(this._callState, this._isPreparingToJoin, "startCamera()", "Did you mean to use setLocalAudio() and/or setLocalVideo() instead?"), this.needsLoad()) try {
      yield this.load(t2);
    } catch (e3) {
      return Promise.reject(e3);
    }
    else {
      if (this._didPreAuth) {
        if (t2.url && t2.url !== this.properties.url) return console.error("url in startCamera() is different than the one used in preAuth()"), Promise.reject();
        if (t2.token && t2.token !== this.properties.token) return console.error("token in startCamera() is different than the one used in preAuth()"), Promise.reject();
      }
      this.validateProperties(t2), this.properties = Va(Va({}, this.properties), t2);
    }
    return new Promise(function(t3) {
      e2._preloadCache.inputSettings = e2._prepInputSettingsForSharing(e2._inputSettings, false), e2.sendMessageToCallMachine({ action: "start-camera", properties: oc(e2.properties, e2.callClientId), preloadCache: oc(e2._preloadCache, e2.callClientId) }, function(e3) {
        t3({ camera: e3.camera, mic: e3.mic, speaker: e3.speaker });
      });
    });
  }), function() {
    return V2.apply(this, arguments);
  }) }, { key: "validateCustomTrack", value: function(e2, t2, n2) {
    if (n2 && n2.length > 50) throw new Error("Custom track `trackName` must not be more than 50 characters");
    if (t2 && "music" !== t2 && "speech" !== t2 && !(t2 instanceof Object)) throw new Error("Custom track `mode` must be either `music` | `speech` | `DailyMicAudioModeSettings` or `undefined`");
    if (!!n2 && ["cam-audio", "cam-video", "screen-video", "screen-audio", "rmpAudio", "rmpVideo", "customVideoDefaults"].includes(n2)) throw new Error("Custom track `trackName` must not match a track name already used by daily: cam-audio, cam-video, customVideoDefaults, screen-video, screen-audio, rmpAudio, rmpVideo");
    if (!(e2 instanceof MediaStreamTrack)) throw new Error("Custom tracks provided must be instances of MediaStreamTrack");
  } }, { key: "startCustomTrack", value: function() {
    var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : { track, mode, trackName };
    return hc(), sc(this._callState, "startCustomTrack()"), this.validateCustomTrack(t2.track, t2.mode, t2.trackName), new Promise(function(n2, r3) {
      e2._sharedTracks.customTrack = t2.track, t2.track = ls, e2.sendMessageToCallMachine({ action: "start-custom-track", properties: t2 }, function(e3) {
        e3.error ? r3({ error: e3.error }) : n2(e3.mediaTag);
      });
    });
  } }, { key: "stopCustomTrack", value: function(e2) {
    var t2 = this;
    return hc(), sc(this._callState, "stopCustomTrack()"), new Promise(function(n2) {
      t2.sendMessageToCallMachine({ action: "stop-custom-track", mediaTag: e2 }, function(e3) {
        n2(e3.mediaTag);
      });
    });
  } }, { key: "setCamera", value: function(e2) {
    var t2 = this;
    return fc(), lc(this._callMachineInitialized, "setCamera()"), new Promise(function(n2) {
      t2.sendMessageToCallMachine({ action: "set-camera", cameraDeviceId: e2 }, function(e3) {
        n2({ device: e3.device });
      });
    });
  } }, { key: "setAudioDevice", value: (U2 = p(function* (e2) {
    return fc(), this.nativeUtils().setAudioDevice(e2), { deviceId: yield this.nativeUtils().getAudioDevice() };
  }), function(e2) {
    return U2.apply(this, arguments);
  }) }, { key: "cycleCamera", value: function() {
    var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
    return new Promise(function(n2) {
      e2.sendMessageToCallMachine({ action: "cycle-camera", properties: t2 }, function(e3) {
        n2({ device: e3.device });
      });
    });
  } }, { key: "cycleMic", value: function() {
    var e2 = this;
    return hc(), new Promise(function(t2) {
      e2.sendMessageToCallMachine({ action: "cycle-mic" }, function(e3) {
        t2({ device: e3.device });
      });
    });
  } }, { key: "getCameraFacingMode", value: function() {
    var e2 = this;
    return fc(), new Promise(function(t2) {
      e2.sendMessageToCallMachine({ action: "get-camera-facing-mode" }, function(e3) {
        t2(e3.facingMode);
      });
    });
  } }, { key: "setInputDevicesAsync", value: (B2 = p(function* (e2) {
    var t2 = this, n2 = e2.audioDeviceId, r3 = e2.videoDeviceId, i3 = e2.audioSource, o2 = e2.videoSource;
    if (hc(), void 0 !== i3 && (n2 = i3), void 0 !== o2 && (r3 = o2), "boolean" == typeof n2 && (this._setAllowLocalAudio(n2), n2 = void 0), "boolean" == typeof r3 && (this._setAllowLocalVideo(r3), r3 = void 0), !n2 && !r3) return yield this.getInputDevices();
    var s2 = {};
    return n2 && (n2 instanceof MediaStreamTrack ? (this._sharedTracks.audioTrack = n2, n2 = ls, s2.audio = { settings: { customTrack: n2 } }) : (delete this._sharedTracks.audioTrack, s2.audio = { settings: { deviceId: n2 } })), r3 && (r3 instanceof MediaStreamTrack ? (this._sharedTracks.videoTrack = r3, r3 = ls, s2.video = { settings: { customTrack: r3 } }) : (delete this._sharedTracks.videoTrack, s2.video = { settings: { deviceId: r3 } })), this._callObjectMode && this.needsLoad() ? (this._updatePreloadCacheInputSettings(s2, false), this._devicesFromInputSettings(this._inputSettings)) : new Promise(function(e3) {
      t2.sendMessageToCallMachine({ action: "set-input-devices", audioDeviceId: n2, videoDeviceId: r3 }, function(n3) {
        if (delete n3.action, delete n3.callbackStamp, n3.returnPreloadCache) return t2._updatePreloadCacheInputSettings(s2, false), void e3(t2._devicesFromInputSettings(t2._inputSettings));
        e3(n3);
      });
    });
  }), function(e2) {
    return B2.apply(this, arguments);
  }) }, { key: "setOutputDeviceAsync", value: (F2 = p(function* (e2) {
    var t2 = this, n2 = e2.outputDeviceId;
    return hc(), n2 && (this._preloadCache.outputDeviceId = n2), this._callObjectMode && this.needsLoad() ? this._devicesFromInputSettings(this._inputSettings) : new Promise(function(e3) {
      t2.sendMessageToCallMachine({ action: "set-output-device", outputDeviceId: n2 }, function(n3) {
        delete n3.action, delete n3.callbackStamp, n3.returnPreloadCache ? e3(t2._devicesFromInputSettings(t2._inputSettings)) : e3(n3);
      });
    });
  }), function(e2) {
    return F2.apply(this, arguments);
  }) }, { key: "getInputDevices", value: (R2 = p(function* () {
    var e2 = this;
    return this._callObjectMode && this.needsLoad() ? this._devicesFromInputSettings(this._inputSettings) : new Promise(function(t2) {
      e2.sendMessageToCallMachine({ action: "get-input-devices" }, function(n2) {
        n2.returnPreloadCache ? t2(e2._devicesFromInputSettings(e2._inputSettings)) : t2({ camera: n2.camera, mic: n2.mic, speaker: n2.speaker });
      });
    });
  }), function() {
    return R2.apply(this, arguments);
  }) }, { key: "nativeInCallAudioMode", value: function() {
    return fc(), this._nativeInCallAudioMode;
  } }, { key: "setNativeInCallAudioMode", value: function(e2) {
    if (fc(), [Wa, Ha].includes(e2)) {
      if (e2 !== this._nativeInCallAudioMode) return this._nativeInCallAudioMode = e2, !this.disableReactNativeAutoDeviceManagement("audio") && ac(this._callState, this._isPreparingToJoin) && this.nativeUtils().setAudioMode(this._nativeInCallAudioMode), this;
    } else console.error("invalid in-call audio mode specified: ", e2);
  } }, { key: "preAuth", value: (D2 = p(function* () {
    var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
    if (dc(this._callObjectMode, "preAuth()"), cc(this._callState, this._isPreparingToJoin, "preAuth()"), this.needsLoad() && (yield this.load(t2)), !t2.url) throw new Error("preAuth() requires at least a url to be provided");
    return this.validateProperties(t2), this.properties = Va(Va({}, this.properties), t2), new Promise(function(t3, n2) {
      e2._preloadCache.inputSettings = e2._prepInputSettingsForSharing(e2._inputSettings, false), e2.sendMessageToCallMachine({ action: "daily-method-preauth", properties: oc(e2.properties, e2.callClientId), preloadCache: oc(e2._preloadCache, e2.callClientId) }, function(r3) {
        return r3.error ? n2(r3.error) : r3.access ? (e2._didPreAuth = true, void t3({ access: r3.access })) : n2(new Error("unknown error in preAuth()"));
      });
    });
  }), function() {
    return D2.apply(this, arguments);
  }) }, { key: "load", value: (L2 = p(function* (e2) {
    var t2 = this;
    if (this.needsLoad()) {
      if (this._destroyed && (this._logUseAfterDestroy(), this.strictMode)) throw new Error("Use after destroy");
      if (e2 && (this.validateProperties(e2), this.properties = Va(Va({}, this.properties), e2)), !this._callObjectMode && !this.properties.url) throw new Error("can't load iframe meeting because url property isn't set");
      return this._updateCallState(gi), this.emitDailyJSEvent({ action: to }), this._callObjectMode ? new Promise(function(e3, n2) {
        t2._callObjectLoader.cancel();
        var r3 = Date.now();
        t2._callObjectLoader.load(t2.properties.dailyConfig, function(n3) {
          t2._bundleLoadTime = n3 ? "no-op" : Date.now() - r3, t2._updateCallState(mi), n3 && t2.emitDailyJSEvent({ action: ro }), e3();
        }, function(e4, r4) {
          if (t2.emitDailyJSEvent({ action: no }), !r4) {
            t2._updateCallState(wi), t2.resetMeetingDependentVars();
            var i3 = { action: ns, errorMsg: e4.msg, error: { type: "connection-error", msg: "Failed to load call object bundle.", details: { on: "load", sourceError: e4, bundleUrl: Z(t2.properties.dailyConfig) } } };
            t2._maybeSendToSentry(i3), t2.emitDailyJSEvent(i3), n2(e4.msg);
          }
        });
      }) : (this._iframe.src = X(this.assembleMeetingUrl(), this.properties.dailyConfig), new Promise(function(e3, n2) {
        t2._loadedCallback = function(r3) {
          t2._callState !== wi ? (t2._updateCallState(mi), (t2.properties.cssFile || t2.properties.cssText) && t2.loadCss(t2.properties), e3()) : n2(r3);
        };
      }));
    }
  }), function(e2) {
    return L2.apply(this, arguments);
  }) }, { key: "join", value: (I2 = p(function* () {
    var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
    this._testCallInProgress && this.stopTestCallQuality();
    var n2 = false;
    if (this.needsLoad()) {
      this.updateIsPreparingToJoin(true);
      try {
        yield this.load(t2);
      } catch (e3) {
        return this.updateIsPreparingToJoin(false), Promise.reject(e3);
      }
    } else {
      if (n2 = !(!this.properties.cssFile && !this.properties.cssText), this._didPreAuth) {
        if (t2.url && t2.url !== this.properties.url) return console.error("url in join() is different than the one used in preAuth()"), this.updateIsPreparingToJoin(false), Promise.reject();
        if (t2.token && t2.token !== this.properties.token) return console.error("token in join() is different than the one used in preAuth()"), this.updateIsPreparingToJoin(false), Promise.reject();
      }
      if (t2.url && !this._callObjectMode && t2.url && t2.url !== this.properties.url) return console.error("url in join() is different than the one used in load() (".concat(this.properties.url, " -> ").concat(t2.url, ")")), this.updateIsPreparingToJoin(false), Promise.reject();
      this.validateProperties(t2), this.properties = Va(Va({}, this.properties), t2);
    }
    return void 0 !== t2.showLocalVideo && (this._callObjectMode ? console.error("showLocalVideo is not available in callObject mode") : this._showLocalVideo = !!t2.showLocalVideo), void 0 !== t2.showParticipantsBar && (this._callObjectMode ? console.error("showParticipantsBar is not available in callObject mode") : this._showParticipantsBar = !!t2.showParticipantsBar), this._callState === bi || this._callState === yi ? (console.warn("already joined meeting, call leave() before joining again"), void this.updateIsPreparingToJoin(false)) : (this._updateCallState(yi, false), this.emitDailyJSEvent({ action: so }), this._preloadCache.inputSettings = this._prepInputSettingsForSharing(this._inputSettings || {}, false), this.sendMessageToCallMachine({ action: "join-meeting", properties: oc(this.properties, this.callClientId), preloadCache: oc(this._preloadCache, this.callClientId) }), new Promise(function(t3, r3) {
      e2._joinedCallback = function(i3, o2) {
        if (e2._callState !== wi) {
          if (e2._updateCallState(bi), i3) for (var s2 in i3) {
            if (e2._callObjectMode) {
              var a2 = e2._callMachine().store;
              ua(i3[s2], a2), da(i3[s2], a2), ha(i3[s2], e2._participants[s2], a2);
            }
            e2._participants[s2] = Va({}, i3[s2]), e2.toggleParticipantAudioBasedOnNativeAudioFocus();
          }
          n2 && e2.loadCss(e2.properties), t3(i3);
        } else r3(o2);
      };
    }));
  }), function() {
    return I2.apply(this, arguments);
  }) }, { key: "leave", value: (j2 = p(function* () {
    var e2 = this;
    return this._testCallInProgress && this.stopTestCallQuality(), new Promise(function(t2) {
      e2._callState === _i || e2._callState === wi ? t2() : e2._callObjectLoader && !e2._callObjectLoader.loaded ? (e2._callObjectLoader.cancel(), e2._updateCallState(_i), e2.resetMeetingDependentVars(), e2.emitDailyJSEvent({ action: _i }), t2()) : (e2._resolveLeave = t2, e2.sendMessageToCallMachine({ action: "leave-meeting" }));
    });
  }), function() {
    return j2.apply(this, arguments);
  }) }, { key: "startScreenShare", value: (A2 = p(function* () {
    var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
    if (lc(this._callMachineInitialized, "startScreenShare()"), t2.screenVideoSendSettings && this._validateVideoSendSettings("screenVideo", t2.screenVideoSendSettings), t2.mediaStream && (this._sharedTracks.screenMediaStream = t2.mediaStream, t2.mediaStream = ls), "undefined" != typeof DailyNativeUtils && void 0 !== DailyNativeUtils.isIOS && DailyNativeUtils.isIOS) {
      var n2 = this.nativeUtils();
      if (yield n2.isScreenBeingCaptured()) return void this.emitDailyJSEvent({ action: ts, type: "screen-share-error", errorMsg: "Could not start the screen sharing. The screen is already been captured!" });
      n2.setSystemScreenCaptureStartCallback(function() {
        n2.setSystemScreenCaptureStartCallback(null), e2.sendMessageToCallMachine({ action: ss, captureOptions: t2 });
      }), n2.presentSystemScreenCapturePrompt();
    } else this.sendMessageToCallMachine({ action: ss, captureOptions: t2 });
  }), function() {
    return A2.apply(this, arguments);
  }) }, { key: "stopScreenShare", value: function() {
    lc(this._callMachineInitialized, "stopScreenShare()"), this.sendMessageToCallMachine({ action: "local-screen-stop" });
  } }, { key: "startRecording", value: function() {
    var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, t2 = e2.type;
    if (t2 && "cloud" !== t2 && "raw-tracks" !== t2 && "local" !== t2) throw new Error("invalid type: ".concat(t2, ", allowed values 'cloud', 'raw-tracks', or 'local'"));
    this.sendMessageToCallMachine(Va({ action: "local-recording-start" }, e2));
  } }, { key: "updateRecording", value: function(e2) {
    var t2 = e2.layout, n2 = void 0 === t2 ? { preset: "default" } : t2, r3 = e2.instanceId;
    this.sendMessageToCallMachine({ action: "daily-method-update-recording", layout: n2, instanceId: r3 });
  } }, { key: "stopRecording", value: function() {
    var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
    this.sendMessageToCallMachine(Va({ action: "local-recording-stop" }, e2));
  } }, { key: "startLiveStreaming", value: function() {
    var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
    this.sendMessageToCallMachine(Va({ action: "daily-method-start-live-streaming" }, e2));
  } }, { key: "updateLiveStreaming", value: function(e2) {
    var t2 = e2.layout, n2 = void 0 === t2 ? { preset: "default" } : t2, r3 = e2.instanceId;
    this.sendMessageToCallMachine({ action: "daily-method-update-live-streaming", layout: n2, instanceId: r3 });
  } }, { key: "addLiveStreamingEndpoints", value: function(e2) {
    var t2 = e2.endpoints, n2 = e2.instanceId;
    this.sendMessageToCallMachine({ action: as, endpointsOp: fs, endpoints: t2, instanceId: n2 });
  } }, { key: "removeLiveStreamingEndpoints", value: function(e2) {
    var t2 = e2.endpoints, n2 = e2.instanceId;
    this.sendMessageToCallMachine({ action: as, endpointsOp: vs, endpoints: t2, instanceId: n2 });
  } }, { key: "stopLiveStreaming", value: function() {
    var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
    this.sendMessageToCallMachine(Va({ action: "daily-method-stop-live-streaming" }, e2));
  } }, { key: "validateDailyConfig", value: function(e2) {
    e2.camSimulcastEncodings && (console.warn("camSimulcastEncodings is deprecated. Use sendSettings, found in DailyCallOptions, to provide camera simulcast settings."), this.validateSimulcastEncodings(e2.camSimulcastEncodings)), e2.screenSimulcastEncodings && console.warn("screenSimulcastEncodings is deprecated. Use sendSettings, found in DailyCallOptions, to provide screen simulcast settings."), Es() && e2.noAutoDefaultDeviceChange && console.warn("noAutoDefaultDeviceChange is not supported on Android, and will be ignored.");
  } }, { key: "validateSimulcastEncodings", value: function(e2) {
    var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null, n2 = arguments.length > 2 && void 0 !== arguments[2] && arguments[2];
    if (e2) {
      if (!(e2 instanceof Array || Array.isArray(e2))) throw new Error("encodings must be an Array");
      if (!Ac(e2.length, 1, 3)) throw new Error("encodings must be an Array with between 1 to ".concat(3, " layers"));
      for (var r3 = 0; r3 < e2.length; r3++) {
        var i3 = e2[r3];
        for (var o2 in this._validateEncodingLayerHasValidProperties(i3), i3) if (Ya.includes(o2)) {
          if ("number" != typeof i3[o2]) throw new Error("".concat(o2, " must be a number"));
          if (t2) {
            var s2 = t2[o2], a2 = s2.min, c3 = s2.max;
            if (!Ac(i3[o2], a2, c3)) throw new Error("".concat(o2, " value not in range. valid range: ").concat(a2, " to ").concat(c3));
          }
        } else if (!["active", "scalabilityMode"].includes(o2)) throw new Error("Invalid key ".concat(o2, ", valid keys are:") + Object.values(Ya));
        if (n2 && !i3.hasOwnProperty("maxBitrate")) throw new Error("maxBitrate is not specified");
      }
    }
  } }, { key: "startRemoteMediaPlayer", value: (P2 = p(function* (e2) {
    var t2 = this, n2 = e2.url, r3 = e2.settings, i3 = void 0 === r3 ? { state: ps.PLAY } : r3;
    try {
      !function(e3) {
        if ("string" != typeof e3) throw new Error('url parameter must be "string" type');
      }(n2), Pc(i3), function(e3) {
        for (var t3 in e3) if (!Xa.includes(t3)) throw new Error("Invalid key ".concat(t3, ", valid keys are: ").concat(Xa));
        e3.simulcastEncodings && this.validateSimulcastEncodings(e3.simulcastEncodings, Ka, true);
      }(i3);
    } catch (e3) {
      throw console.error("invalid argument Error: ".concat(e3)), console.error('startRemoteMediaPlayer arguments must be of the form:\n  { url: "playback url",\n  settings?:\n  {state: "play"|"pause", simulcastEncodings?: [{}] } }'), e3;
    }
    return new Promise(function(e3, r4) {
      t2.sendMessageToCallMachine({ action: "daily-method-start-remote-media-player", url: n2, settings: i3 }, function(t3) {
        t3.error ? r4({ error: t3.error, errorMsg: t3.errorMsg }) : e3({ session_id: t3.session_id, remoteMediaPlayerState: { state: t3.state, settings: t3.settings } });
      });
    });
  }), function(e2) {
    return P2.apply(this, arguments);
  }) }, { key: "stopRemoteMediaPlayer", value: (O2 = p(function* (e2) {
    var t2 = this;
    if ("string" != typeof e2) throw new Error(" remotePlayerID must be of type string");
    return new Promise(function(n2, r3) {
      t2.sendMessageToCallMachine({ action: "daily-method-stop-remote-media-player", session_id: e2 }, function(e3) {
        e3.error ? r3({ error: e3.error, errorMsg: e3.errorMsg }) : n2();
      });
    });
  }), function(e2) {
    return O2.apply(this, arguments);
  }) }, { key: "updateRemoteMediaPlayer", value: (T2 = p(function* (e2) {
    var t2 = this, n2 = e2.session_id, r3 = e2.settings;
    try {
      Pc(r3);
    } catch (e3) {
      throw console.error("invalid argument Error: ".concat(e3)), console.error('updateRemoteMediaPlayer arguments must be of the form:\n  session_id: "participant session",\n  { settings?: {state: "play"|"pause"} }'), e3;
    }
    return new Promise(function(e3, i3) {
      t2.sendMessageToCallMachine({ action: "daily-method-update-remote-media-player", session_id: n2, settings: r3 }, function(t3) {
        t3.error ? i3({ error: t3.error, errorMsg: t3.errorMsg }) : e3({ session_id: t3.session_id, remoteMediaPlayerState: { state: t3.state, settings: t3.settings } });
      });
    });
  }), function(e2) {
    return T2.apply(this, arguments);
  }) }, { key: "startTranscription", value: function(e2) {
    sc(this._callState, "startTranscription()"), this.sendMessageToCallMachine(Va({ action: "daily-method-start-transcription" }, e2));
  } }, { key: "updateTranscription", value: function(e2) {
    if (sc(this._callState, "updateTranscription()"), !e2) throw new Error("updateTranscription Error: options is mandatory");
    if ("object" !== n(e2)) throw new Error("updateTranscription Error: options must be object type");
    if (e2.participants && !Array.isArray(e2.participants)) throw new Error("updateTranscription Error: participants must be an array");
    this.sendMessageToCallMachine(Va({ action: "daily-method-update-transcription" }, e2));
  } }, { key: "stopTranscription", value: function(e2) {
    if (sc(this._callState, "stopTranscription()"), e2 && "object" !== n(e2)) throw new Error("stopTranscription Error: options must be object type");
    if (e2 && !e2.instanceId) throw new Error('"instanceId" not provided');
    this.sendMessageToCallMachine(Va({ action: "daily-method-stop-transcription" }, e2));
  } }, { key: "startDialOut", value: (E2 = p(function* (e2) {
    var t2 = this;
    sc(this._callState, "startDialOut()");
    var n2 = function(e3) {
      if (e3) {
        if (!Array.isArray(e3)) throw new Error("Error starting dial out: audio codec must be an array");
        if (e3.length <= 0) throw new Error("Error starting dial out: audio codec array specified but empty");
        e3.forEach(function(e4) {
          if ("string" != typeof e4) throw new Error("Error starting dial out: audio codec must be a string");
          if ("OPUS" !== e4 && "PCMU" !== e4 && "PCMA" !== e4 && "G722" !== e4) throw new Error("Error starting dial out: audio codec must be one of OPUS, PCMU, PCMA, G722");
        });
      }
    };
    if (!e2.sipUri && !e2.phoneNumber) throw new Error("Error starting dial out: either a sip uri or phone number must be provided");
    if (e2.sipUri && e2.phoneNumber) throw new Error("Error starting dial out: only one of sip uri or phone number must be provided");
    if (e2.sipUri) {
      if ("string" != typeof e2.sipUri) throw new Error("Error starting dial out: sipUri must be a string");
      if (!e2.sipUri.startsWith("sip:")) throw new Error("Error starting dial out: Invalid SIP URI, must start with 'sip:'");
      if (e2.video && "boolean" != typeof e2.video) throw new Error("Error starting dial out: video must be a boolean value");
      !function(e3) {
        if (e3 && (n2(e3.audio), e3.video)) {
          if (!Array.isArray(e3.video)) throw new Error("Error starting dial out: video codec must be an array");
          if (e3.video.length <= 0) throw new Error("Error starting dial out: video codec array specified but empty");
          e3.video.forEach(function(e4) {
            if ("string" != typeof e4) throw new Error("Error starting dial out: video codec must be a string");
            if ("H264" !== e4 && "VP8" !== e4) throw new Error("Error starting dial out: video codec must be H264 or VP8");
          });
        }
      }(e2.codecs);
    }
    if (e2.phoneNumber) {
      if ("string" != typeof e2.phoneNumber) throw new Error("Error starting dial out: phoneNumber must be a string");
      if (!/^\+\d{1,}$/.test(e2.phoneNumber)) throw new Error("Error starting dial out: Invalid phone number, must be valid phone number as per E.164");
      e2.codecs && n2(e2.codecs.audio);
    }
    if (e2.callerId) {
      if ("string" != typeof e2.callerId) throw new Error("Error starting dial out: callerId must be a string");
      if (e2.sipUri) throw new Error("Error starting dial out: callerId not allowed with sipUri");
    }
    if (e2.displayName) {
      if ("string" != typeof e2.displayName) throw new Error("Error starting dial out: displayName must be a string");
      if (e2.displayName.length >= 200) throw new Error("Error starting dial out: displayName length must be less than 200");
    }
    if (e2.userId) {
      if ("string" != typeof e2.userId) throw new Error("Error starting dial out: userId must be a string");
      if (e2.userId.length > 36) throw new Error("Error starting dial out: userId length must be less than or equal to 36");
    }
    return new Promise(function(n3, r3) {
      t2.sendMessageToCallMachine(Va({ action: "dialout-start" }, e2), function(e3) {
        e3.error ? r3(e3.error) : n3(e3);
      });
    });
  }), function(e2) {
    return E2.apply(this, arguments);
  }) }, { key: "stopDialOut", value: function(e2) {
    var t2 = this;
    return sc(this._callState, "stopDialOut()"), new Promise(function(n2, r3) {
      t2.sendMessageToCallMachine(Va({ action: "dialout-stop" }, e2), function(e3) {
        e3.error ? r3(e3.error) : n2(e3);
      });
    });
  } }, { key: "sipCallTransfer", value: (C2 = p(function* (e2) {
    var t2 = this;
    if (sc(this._callState, "sipCallTransfer()"), !e2) throw new Error("sipCallTransfer() requires a sessionId and toEndPoint");
    return e2.useSipRefer = false, Oc(e2, "sipCallTransfer"), new Promise(function(n2, r3) {
      t2.sendMessageToCallMachine(Va({ action: gs }, e2), function(e3) {
        e3.error ? r3(e3.error) : n2(e3);
      });
    });
  }), function(e2) {
    return C2.apply(this, arguments);
  }) }, { key: "sipRefer", value: (M2 = p(function* (e2) {
    var t2 = this;
    if (sc(this._callState, "sipRefer()"), !e2) throw new Error("sessionId and toEndPoint are mandatory parameter");
    return e2.useSipRefer = true, Oc(e2, "sipRefer"), new Promise(function(n2, r3) {
      t2.sendMessageToCallMachine(Va({ action: gs }, e2), function(e3) {
        e3.error ? r3(e3.error) : n2(e3);
      });
    });
  }), function(e2) {
    return M2.apply(this, arguments);
  }) }, { key: "sendDTMF", value: (k2 = p(function* (e2) {
    var t2 = this;
    return sc(this._callState, "sendDTMF()"), function(e3) {
      var t3 = e3.sessionId, n2 = e3.tones;
      if (!t3 || !n2) throw new Error("sessionId and tones are mandatory parameter");
      if ("string" != typeof t3 || "string" != typeof n2) throw new Error("sessionId and tones should be of string type");
      if (n2.length > 20) throw new Error("tones string must be upto 20 characters");
      var r3 = /[^0-9A-D*#]/g, i3 = n2.match(r3);
      if (i3 && i3[0]) throw new Error("".concat(i3[0], " is not valid DTMF tone"));
    }(e2), new Promise(function(n2, r3) {
      t2.sendMessageToCallMachine(Va({ action: "send-dtmf" }, e2), function(e3) {
        e3.error ? r3(e3.error) : n2(e3);
      });
    });
  }), function(e2) {
    return k2.apply(this, arguments);
  }) }, { key: "getNetworkStats", value: function() {
    var e2 = this;
    if (this._callState !== bi) {
      return Promise.resolve(Va({ stats: { latest: {} } }, this._network));
    }
    return new Promise(function(t2) {
      e2.sendMessageToCallMachine({ action: "get-calc-stats" }, function(n2) {
        t2(Va(Va({}, e2._network), {}, { stats: n2.stats }));
      });
    });
  } }, { key: "testWebsocketConnectivity", value: (S2 = p(function* () {
    var e2 = this;
    if (uc(this._testCallInProgress, "testWebsocketConnectivity()"), this.needsLoad()) try {
      yield this.load();
    } catch (e3) {
      return Promise.reject(e3);
    }
    return new Promise(function(t2, n2) {
      e2.sendMessageToCallMachine({ action: "test-websocket-connectivity" }, function(e3) {
        e3.error ? n2(e3.error) : t2(e3.results);
      });
    });
  }), function() {
    return S2.apply(this, arguments);
  }) }, { key: "abortTestWebsocketConnectivity", value: function() {
    this.sendMessageToCallMachine({ action: "abort-test-websocket-connectivity" });
  } }, { key: "_validateVideoTrackForNetworkTests", value: function(e2) {
    return e2 ? e2 instanceof MediaStreamTrack ? !!Ea(e2, { isLocalScreenVideo: false }) || (console.error("Video track is not playable. This test needs a live video track."), false) : (console.error("Video track needs to be of type `MediaStreamTrack`."), false) : (console.error("Missing video track. You must provide a video track in order to run this test."), false);
  } }, { key: "testCallQuality", value: (w2 = p(function* () {
    var t2 = this;
    hc(), dc(this._callObjectMode, "testCallQuality()"), lc(this._callMachineInitialized, "testCallQuality()", null, true), cc(this._callState, this._isPreparingToJoin, "testCallQuality()");
    var n2 = this._testCallAlreadyInProgress, r3 = function(e2) {
      n2 || (t2._testCallInProgress = e2);
    };
    if (r3(true), this.needsLoad()) try {
      var i3 = this._callState;
      yield this.load(), this._callState = i3;
    } catch (e2) {
      return r3(false), Promise.reject(e2);
    }
    return new Promise(function(n3) {
      t2.sendMessageToCallMachine({ action: "test-call-quality", dailyJsVersion: t2.properties.dailyJsVersion }, function(i4) {
        var o2 = i4.results, s2 = o2.result, a2 = e(o2, Fa);
        if ("failed" === s2) {
          var c3, l2 = Va({}, a2);
          null !== (c3 = a2.error) && void 0 !== c3 && c3.details ? (a2.error.details = JSON.parse(a2.error.details), l2.error = Va(Va({}, l2.error), {}, { details: Va({}, l2.error.details) }), l2.error.details.duringTest = "testCallQuality") : (l2.error = l2.error ? Va({}, l2.error) : {}, l2.error.details = { duringTest: "testCallQuality" }), t2._maybeSendToSentry(l2);
        }
        r3(false), n3(Va({ result: s2 }, a2));
      });
    });
  }), function() {
    return w2.apply(this, arguments);
  }) }, { key: "stopTestCallQuality", value: function() {
    this.sendMessageToCallMachine({ action: "stop-test-call-quality" });
  } }, { key: "testConnectionQuality", value: (_2 = p(function* (e2) {
    var t2;
    ys() ? (console.warn("testConnectionQuality() is deprecated: use testPeerToPeerCallQuality() instead"), t2 = yield this.testPeerToPeerCallQuality(e2)) : (console.warn("testConnectionQuality() is deprecated: use testCallQuality() instead"), t2 = yield this.testCallQuality());
    var n2 = { result: t2.result, secondsElapsed: t2.secondsElapsed };
    return t2.data && (n2.data = { maxRTT: t2.data.maxRoundTripTime, packetLoss: t2.data.avgRecvPacketLoss }), n2;
  }), function(e2) {
    return _2.apply(this, arguments);
  }) }, { key: "testPeerToPeerCallQuality", value: (b2 = p(function* (e2) {
    var t2 = this;
    if (uc(this._testCallInProgress, "testPeerToPeerCallQuality()"), this.needsLoad()) try {
      yield this.load();
    } catch (e3) {
      return Promise.reject(e3);
    }
    var n2 = e2.videoTrack, r3 = e2.duration;
    if (!this._validateVideoTrackForNetworkTests(n2)) throw new Error("Video track error");
    return this._sharedTracks.videoTrackForConnectionQualityTest = n2, new Promise(function(e3, n3) {
      t2.sendMessageToCallMachine({ action: "test-p2p-call-quality", duration: r3 }, function(t3) {
        t3.error ? n3(t3.error) : e3(t3.results);
      });
    });
  }), function(e2) {
    return b2.apply(this, arguments);
  }) }, { key: "stopTestConnectionQuality", value: function() {
    ys() ? (console.warn("stopTestConnectionQuality() is deprecated: use testPeerToPeerCallQuality() and stopTestPeerToPeerCallQuality() instead"), this.stopTestPeerToPeerCallQuality()) : (console.warn("stopTestConnectionQuality() is deprecated: use testCallQuality() and stopTestCallQuality() instead"), this.stopTestCallQuality());
  } }, { key: "stopTestPeerToPeerCallQuality", value: function() {
    this.sendMessageToCallMachine({ action: "stop-test-p2p-call-quality" });
  } }, { key: "testNetworkConnectivity", value: (y2 = p(function* (e2) {
    var t2 = this;
    if (uc(this._testCallInProgress, "testNetworkConnectivity()"), this.needsLoad()) try {
      yield this.load();
    } catch (e3) {
      return Promise.reject(e3);
    }
    if (!this._validateVideoTrackForNetworkTests(e2)) throw new Error("Video track error");
    return this._sharedTracks.videoTrackForNetworkConnectivityTest = e2, new Promise(function(e3, n2) {
      t2.sendMessageToCallMachine({ action: "test-network-connectivity" }, function(t3) {
        t3.error ? n2(t3.error) : e3(t3.results);
      });
    });
  }), function(e2) {
    return y2.apply(this, arguments);
  }) }, { key: "abortTestNetworkConnectivity", value: function() {
    this.sendMessageToCallMachine({ action: "abort-test-network-connectivity" });
  } }, { key: "getCpuLoadStats", value: function() {
    var e2 = this;
    return new Promise(function(t2) {
      if (e2._callState === bi) {
        e2.sendMessageToCallMachine({ action: "get-cpu-load-stats" }, function(e3) {
          t2(e3.cpuStats);
        });
      } else t2({ cpuLoadState: void 0, cpuLoadStateReason: void 0, stats: {} });
    });
  } }, { key: "_validateEncodingLayerHasValidProperties", value: function(e2) {
    var t2;
    if (!((null === (t2 = Object.keys(e2)) || void 0 === t2 ? void 0 : t2.length) > 0)) throw new Error("Empty encoding is not allowed. At least one of these valid keys should be specified:" + Object.values(Ya));
  } }, { key: "_validateVideoSendSettings", value: function(e2, t2) {
    var r3 = "screenVideo" === e2 ? ["default-screen-video", "detail-optimized", "motion-optimized", "motion-and-detail-balanced"] : ["default-video", "bandwidth-optimized", "bandwidth-and-quality-balanced", "quality-optimized", "adaptive-2-layers", "adaptive-3-layers"], i3 = "Video send settings should be either an object or one of the supported presets: ".concat(r3.join());
    if ("string" == typeof t2) {
      if (!r3.includes(t2)) throw new Error(i3);
    } else {
      if ("object" !== n(t2)) throw new Error(i3);
      if (!t2.maxQuality && !t2.encodings && void 0 === t2.allowAdaptiveLayers) throw new Error("Video send settings must contain at least maxQuality, allowAdaptiveLayers or encodings attribute");
      if (t2.maxQuality && -1 === ["low", "medium", "high"].indexOf(t2.maxQuality)) throw new Error("maxQuality must be either low, medium or high");
      if (t2.encodings) {
        var o2 = false;
        switch (Object.keys(t2.encodings).length) {
          case 1:
            o2 = !t2.encodings.low;
            break;
          case 2:
            o2 = !t2.encodings.low || !t2.encodings.medium;
            break;
          case 3:
            o2 = !t2.encodings.low || !t2.encodings.medium || !t2.encodings.high;
            break;
          default:
            o2 = true;
        }
        if (o2) throw new Error("Encodings must be defined as: low, low and medium, or low, medium and high.");
        t2.encodings.low && this._validateEncodingLayerHasValidProperties(t2.encodings.low), t2.encodings.medium && this._validateEncodingLayerHasValidProperties(t2.encodings.medium), t2.encodings.high && this._validateEncodingLayerHasValidProperties(t2.encodings.high);
      }
    }
  } }, { key: "validateUpdateSendSettings", value: function(e2) {
    var t2 = this;
    if (!e2 || 0 === Object.keys(e2).length) throw new Error("Send settings must contain at least information for one track!");
    Object.entries(e2).forEach(function(e3) {
      var n2 = f(e3, 2), r3 = n2[0], i3 = n2[1];
      t2._validateVideoSendSettings(r3, i3);
    });
  } }, { key: "updateSendSettings", value: function(e2) {
    var t2 = this;
    return this.validateUpdateSendSettings(e2), this.needsLoad() ? (this._preloadCache.sendSettings = e2, { sendSettings: this._preloadCache.sendSettings }) : new Promise(function(n2, r3) {
      t2.sendMessageToCallMachine({ action: "update-send-settings", sendSettings: e2 }, function(e3) {
        e3.error ? r3(e3.error) : n2(e3.sendSettings);
      });
    });
  } }, { key: "getSendSettings", value: function() {
    return this._sendSettings || this._preloadCache.sendSettings;
  } }, { key: "getLocalAudioLevel", value: function() {
    return this._localAudioLevel;
  } }, { key: "getRemoteParticipantsAudioLevel", value: function() {
    return this._remoteParticipantsAudioLevel;
  } }, { key: "getActiveSpeaker", value: function() {
    return hc(), this._activeSpeaker;
  } }, { key: "setActiveSpeakerMode", value: function(e2) {
    return hc(), this.sendMessageToCallMachine({ action: "set-active-speaker-mode", enabled: e2 }), this;
  } }, { key: "activeSpeakerMode", value: function() {
    return hc(), this._activeSpeakerMode;
  } }, { key: "subscribeToTracksAutomatically", value: function() {
    return this._preloadCache.subscribeToTracksAutomatically;
  } }, { key: "setSubscribeToTracksAutomatically", value: function(e2) {
    return sc(this._callState, "setSubscribeToTracksAutomatically()", "Use the subscribeToTracksAutomatically configuration property."), this._preloadCache.subscribeToTracksAutomatically = e2, this.sendMessageToCallMachine({ action: "daily-method-subscribe-to-tracks-automatically", enabled: e2 }), this;
  } }, { key: "enumerateDevices", value: (m2 = p(function* () {
    var e2 = this;
    if (this._callObjectMode) {
      var t2 = yield navigator.mediaDevices.enumerateDevices();
      return "Firefox" === Os() && Ps().major > 115 && Ps().major < 123 && (t2 = t2.filter(function(e3) {
        return "audiooutput" !== e3.kind;
      })), { devices: t2.map(function(e3) {
        var t3 = JSON.parse(JSON.stringify(e3));
        if (!ys() && "videoinput" === e3.kind && e3.getCapabilities) {
          var n2, r3 = e3.getCapabilities();
          t3.facing = (null == r3 || null === (n2 = r3.facingMode) || void 0 === n2 ? void 0 : n2.length) >= 1 ? r3.facingMode[0] : void 0;
        }
        return t3;
      }) };
    }
    return new Promise(function(t3) {
      e2.sendMessageToCallMachine({ action: "enumerate-devices" }, function(e3) {
        t3({ devices: e3.devices });
      });
    });
  }), function() {
    return m2.apply(this, arguments);
  }) }, { key: "sendAppMessage", value: function(e2) {
    var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "*";
    if (sc(this._callState, "sendAppMessage()"), JSON.stringify(e2).length > this._maxAppMessageSize) throw new Error("Message data too large. Max size is " + this._maxAppMessageSize);
    return this.sendMessageToCallMachine({ action: "app-msg", data: e2, to: t2 }), this;
  } }, { key: "addFakeParticipant", value: function(e2) {
    return hc(), sc(this._callState, "addFakeParticipant()"), this.sendMessageToCallMachine(Va({ action: "add-fake-participant" }, e2)), this;
  } }, { key: "setShowNamesMode", value: function(e2) {
    return pc(this._callObjectMode, "setShowNamesMode()"), hc(), e2 && "always" !== e2 && "never" !== e2 ? (console.error('setShowNamesMode argument should be "always", "never", or false'), this) : (this.sendMessageToCallMachine({ action: "set-show-names", mode: e2 }), this);
  } }, { key: "setShowLocalVideo", value: function() {
    var e2 = !(arguments.length > 0 && void 0 !== arguments[0]) || arguments[0];
    return pc(this._callObjectMode, "setShowLocalVideo()"), hc(), sc(this._callState, "setShowLocalVideo()"), "boolean" != typeof e2 ? (console.error("setShowLocalVideo only accepts a boolean value"), this) : (this.sendMessageToCallMachine({ action: "set-show-local-video", show: e2 }), this._showLocalVideo = e2, this);
  } }, { key: "showLocalVideo", value: function() {
    return pc(this._callObjectMode, "showLocalVideo()"), hc(), this._showLocalVideo;
  } }, { key: "setShowParticipantsBar", value: function() {
    var e2 = !(arguments.length > 0 && void 0 !== arguments[0]) || arguments[0];
    return pc(this._callObjectMode, "setShowParticipantsBar()"), hc(), sc(this._callState, "setShowParticipantsBar()"), "boolean" != typeof e2 ? (console.error("setShowParticipantsBar only accepts a boolean value"), this) : (this.sendMessageToCallMachine({ action: "set-show-participants-bar", show: e2 }), this._showParticipantsBar = e2, this);
  } }, { key: "showParticipantsBar", value: function() {
    return pc(this._callObjectMode, "showParticipantsBar()"), hc(), this._showParticipantsBar;
  } }, { key: "customIntegrations", value: function() {
    return hc(), pc(this._callObjectMode, "customIntegrations()"), this._customIntegrations;
  } }, { key: "setCustomIntegrations", value: function(e2) {
    return hc(), pc(this._callObjectMode, "setCustomIntegrations()"), sc(this._callState, "setCustomIntegrations()"), Ec(e2) ? (this.sendMessageToCallMachine({ action: "set-custom-integrations", integrations: e2 }), this._customIntegrations = e2, this) : this;
  } }, { key: "startCustomIntegrations", value: function(e2) {
    var t2 = this;
    if (hc(), pc(this._callObjectMode, "startCustomIntegrations()"), sc(this._callState, "startCustomIntegrations()"), Array.isArray(e2) && e2.some(function(e3) {
      return "string" != typeof e3;
    }) || !Array.isArray(e2) && "string" != typeof e2) return console.error("startCustomIntegrations() only accepts string | string[]"), this;
    var n2 = "string" == typeof e2 ? [e2] : e2, r3 = n2.filter(function(e3) {
      return !(e3 in t2._customIntegrations);
    });
    return r3.length ? (console.error(`Can't find custom integration(s): "`.concat(r3.join(", "), '"')), this) : (this.sendMessageToCallMachine({ action: "start-custom-integrations", ids: n2 }), this);
  } }, { key: "stopCustomIntegrations", value: function(e2) {
    var t2 = this;
    if (hc(), pc(this._callObjectMode, "stopCustomIntegrations()"), sc(this._callState, "stopCustomIntegrations()"), Array.isArray(e2) && e2.some(function(e3) {
      return "string" != typeof e3;
    }) || !Array.isArray(e2) && "string" != typeof e2) return console.error("stopCustomIntegrations() only accepts string | string[]"), this;
    var n2 = "string" == typeof e2 ? [e2] : e2, r3 = n2.filter(function(e3) {
      return !(e3 in t2._customIntegrations);
    });
    return r3.length ? (console.error(`Can't find custom integration(s): "`.concat(r3.join(", "), '"')), this) : (this.sendMessageToCallMachine({ action: "stop-custom-integrations", ids: n2 }), this);
  } }, { key: "customTrayButtons", value: function() {
    return pc(this._callObjectMode, "customTrayButtons()"), hc(), this._customTrayButtons;
  } }, { key: "updateCustomTrayButtons", value: function(e2) {
    return pc(this._callObjectMode, "updateCustomTrayButtons()"), hc(), sc(this._callState, "updateCustomTrayButtons()"), Cc(e2) ? (this.sendMessageToCallMachine({ action: "update-custom-tray-buttons", btns: e2 }), this._customTrayButtons = e2, this) : (console.error("updateCustomTrayButtons only accepts a dictionary of the type ".concat(JSON.stringify(ec))), this);
  } }, { key: "theme", value: function() {
    return pc(this._callObjectMode, "theme()"), this.properties.theme;
  } }, { key: "setTheme", value: function(e2) {
    var t2 = this;
    return pc(this._callObjectMode, "setTheme()"), new Promise(function(n2, r3) {
      try {
        t2.validateProperties({ theme: e2 }), t2.properties.theme = Va({}, e2), t2.sendMessageToCallMachine({ action: "set-theme", theme: t2.properties.theme });
        try {
          t2.emitDailyJSEvent({ action: eo, theme: t2.properties.theme });
        } catch (e3) {
          console.log("could not emit 'theme-updated'", e3);
        }
        n2(t2.properties.theme);
      } catch (e3) {
        r3(e3);
      }
    });
  } }, { key: "requestFullscreen", value: (g2 = p(function* () {
    if (hc(), this._iframe && !document.fullscreenElement && ws()) try {
      (yield this._iframe.requestFullscreen) ? this._iframe.requestFullscreen() : this._iframe.webkitRequestFullscreen();
    } catch (e2) {
      console.log("could not make video call fullscreen", e2);
    }
  }), function() {
    return g2.apply(this, arguments);
  }) }, { key: "exitFullscreen", value: function() {
    hc(), document.fullscreenElement ? document.exitFullscreen() : document.webkitFullscreenElement && document.webkitExitFullscreen();
  } }, { key: "getSidebarView", value: (v2 = p(function* () {
    var e2 = this;
    return this._callObjectMode ? (console.error("getSidebarView is not available in callObject mode"), Promise.resolve(null)) : new Promise(function(t2) {
      e2.sendMessageToCallMachine({ action: "get-sidebar-view" }, function(e3) {
        t2(e3.view);
      });
    });
  }), function() {
    return v2.apply(this, arguments);
  }) }, { key: "setSidebarView", value: function(e2) {
    return this._callObjectMode ? (console.error("setSidebarView is not available in callObject mode"), this) : (this.sendMessageToCallMachine({ action: "set-sidebar-view", view: e2 }), this);
  } }, { key: "room", value: (h2 = p(function* () {
    var e2 = this, t2 = (arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}).includeRoomConfigDefaults, n2 = void 0 === t2 || t2;
    return this._accessState.access === Oi || this.needsLoad() ? this.properties.url ? { roomUrlPendingJoin: this.properties.url } : null : new Promise(function(t3) {
      e2.sendMessageToCallMachine({ action: "lib-room-info", includeRoomConfigDefaults: n2 }, function(e3) {
        delete e3.action, delete e3.callbackStamp, t3(e3);
      });
    });
  }), function() {
    return h2.apply(this, arguments);
  }) }, { key: "geo", value: (d2 = p(function* () {
    try {
      var e2 = yield fetch("https://gs.daily.co/_ks_/x-swsl/:");
      return { current: (yield e2.json()).geo };
    } catch (e3) {
      return console.error("geo lookup failed", e3), { current: "" };
    }
  }), function() {
    return d2.apply(this, arguments);
  }) }, { key: "setNetworkTopology", value: (c2 = p(function* (e2) {
    var t2 = this;
    return hc(), sc(this._callState, "setNetworkTopology()"), new Promise(function(n2, r3) {
      t2.sendMessageToCallMachine({ action: "set-network-topology", opts: e2 }, function(e3) {
        e3.error ? r3({ error: e3.error }) : n2({ workerId: e3.workerId });
      });
    });
  }), function(e2) {
    return c2.apply(this, arguments);
  }) }, { key: "getNetworkTopology", value: (i2 = p(function* () {
    var e2 = this;
    return new Promise(function(t2, n2) {
      e2.needsLoad() && t2({ topology: "none" }), e2.sendMessageToCallMachine({ action: "get-network-topology" }, function(e3) {
        e3.error ? n2({ error: e3.error }) : t2({ topology: e3.topology });
      });
    });
  }), function() {
    return i2.apply(this, arguments);
  }) }, { key: "setPlayNewParticipantSound", value: function(e2) {
    if (hc(), "number" != typeof e2 && true !== e2 && false !== e2) throw new Error("argument to setShouldPlayNewParticipantSound should be true, false, or a number, but is ".concat(e2));
    this.sendMessageToCallMachine({ action: "daily-method-set-play-ding", arg: e2 });
  } }, { key: "on", value: function(e2, t2) {
    return x.prototype.on.call(this, e2, t2);
  } }, { key: "once", value: function(e2, t2) {
    return x.prototype.once.call(this, e2, t2);
  } }, { key: "off", value: function(e2, t2) {
    return x.prototype.off.call(this, e2, t2);
  } }, { key: "validateProperties", value: function(e2) {
    var t2, n2;
    if (null != e2 && null !== (t2 = e2.dailyConfig) && void 0 !== t2 && t2.userMediaAudioConstraints) {
      var r3, i3;
      ys() || console.warn("userMediaAudioConstraints is deprecated. You can override constraints with inputSettings.audio.settings, found in DailyCallOptions.");
      var o2 = e2.inputSettings || {};
      o2.audio = (null === (r3 = e2.inputSettings) || void 0 === r3 ? void 0 : r3.audio) || {}, o2.audio.settings = (null === (i3 = e2.inputSettings) || void 0 === i3 || null === (i3 = i3.audio) || void 0 === i3 ? void 0 : i3.settings) || {}, o2.audio.settings = Va(Va({}, o2.audio.settings), e2.dailyConfig.userMediaAudioConstraints), e2.inputSettings = o2, delete e2.dailyConfig.userMediaAudioConstraints;
    }
    if (null != e2 && null !== (n2 = e2.dailyConfig) && void 0 !== n2 && n2.userMediaVideoConstraints) {
      var s2, a2;
      ys() || console.warn("userMediaVideoConstraints is deprecated. You can override constraints with inputSettings.video.settings, found in DailyCallOptions.");
      var c3 = e2.inputSettings || {};
      c3.video = (null === (s2 = e2.inputSettings) || void 0 === s2 ? void 0 : s2.video) || {}, c3.video.settings = (null === (a2 = e2.inputSettings) || void 0 === a2 || null === (a2 = a2.video) || void 0 === a2 ? void 0 : a2.settings) || {}, c3.video.settings = Va(Va({}, c3.video.settings), e2.dailyConfig.userMediaVideoConstraints), e2.inputSettings = c3, delete e2.dailyConfig.userMediaVideoConstraints;
    }
    for (var l2 in e2) {
      if (!nc[l2]) throw new Error("unrecognized property '".concat(l2, "'"));
      if (nc[l2].validate && !nc[l2].validate(e2[l2], this)) throw new Error("property '".concat(l2, "': ").concat(nc[l2].help));
    }
  } }, { key: "assembleMeetingUrl", value: function() {
    var e2, t2, n2 = Va(Va({}, this.properties), {}, { emb: this.callClientId, embHref: encodeURIComponent(window.location.href), proxy: null !== (e2 = this.properties.dailyConfig) && void 0 !== e2 && e2.proxyUrl ? encodeURIComponent(null === (t2 = this.properties.dailyConfig) || void 0 === t2 ? void 0 : t2.proxyUrl) : void 0 }), r3 = n2.url.match(/\?/) ? "&" : "?";
    return n2.url + r3 + Object.keys(nc).filter(function(e3) {
      return nc[e3].queryString && void 0 !== n2[e3];
    }).map(function(e3) {
      return "".concat(nc[e3].queryString, "=").concat(n2[e3]);
    }).join("&");
  } }, { key: "needsLoad", value: function() {
    return [vi, gi, _i, wi].includes(this._callState);
  } }, { key: "sendMessageToCallMachine", value: function(e2, t2) {
    if (this._destroyed && (this._logUseAfterDestroy(), this.strictMode)) throw new Error("Use after destroy");
    this._messageChannel.sendMessageToCallMachine(e2, t2, this.callClientId, this._iframe);
  } }, { key: "forwardPackagedMessageToCallMachine", value: function(e2) {
    this._messageChannel.forwardPackagedMessageToCallMachine(e2, this._iframe, this.callClientId);
  } }, { key: "addListenerForPackagedMessagesFromCallMachine", value: function(e2) {
    return this._messageChannel.addListenerForPackagedMessagesFromCallMachine(e2, this.callClientId);
  } }, { key: "removeListenerForPackagedMessagesFromCallMachine", value: function(e2) {
    this._messageChannel.removeListenerForPackagedMessagesFromCallMachine(e2);
  } }, { key: "handleMessageFromCallMachine", value: function(t2) {
    switch (t2.action) {
      case Xi:
        this.sendMessageToCallMachine(Va({ action: Zi }, this.properties));
        break;
      case "call-machine-initialized":
        this._callMachineInitialized = true;
        var n2 = { action: cs, level: "log", code: 1011, stats: { event: "bundle load", time: "no-op" === this._bundleLoadTime ? 0 : this._bundleLoadTime, preLoaded: "no-op" === this._bundleLoadTime, url: Z(this.properties.dailyConfig) } };
        this.sendMessageToCallMachine(n2), this._delayDuplicateInstanceLog && this._logDuplicateInstanceAttempt();
        break;
      case ro:
        this._loadedCallback && (this._loadedCallback(), this._loadedCallback = null), this.emitDailyJSEvent(t2);
        break;
      case ao:
        var r3, i3 = Va({}, t2);
        delete i3.internal, this._maxAppMessageSize = (null === (r3 = t2.internal) || void 0 === r3 ? void 0 : r3._maxAppMessageSize) || rs, this._joinedCallback && (this._joinedCallback(t2.participants), this._joinedCallback = null), this.emitDailyJSEvent(i3);
        break;
      case lo:
      case uo:
        if (this._callState === _i) return;
        if (t2.participant && t2.participant.session_id) {
          var o2 = t2.participant.local ? "local" : t2.participant.session_id;
          if (this._callObjectMode) {
            var s2 = this._callMachine().store;
            ua(t2.participant, s2), da(t2.participant, s2), ha(t2.participant, this._participants[o2], s2);
          }
          try {
            this.maybeParticipantTracksStopped(this._participants[o2], t2.participant), this.maybeParticipantTracksStarted(this._participants[o2], t2.participant), this.maybeEventRecordingStopped(this._participants[o2], t2.participant), this.maybeEventRecordingStarted(this._participants[o2], t2.participant);
          } catch (e2) {
            console.error("track events error", e2);
          }
          this.compareEqualForParticipantUpdateEvent(t2.participant, this._participants[o2]) || (this._participants[o2] = Va({}, t2.participant), this.toggleParticipantAudioBasedOnNativeAudioFocus(), this.emitDailyJSEvent(t2));
        }
        break;
      case po:
        if (t2.participant && t2.participant.session_id) {
          var a2 = this._participants[t2.participant.session_id];
          a2 && this.maybeParticipantTracksStopped(a2, null), delete this._participants[t2.participant.session_id], this.emitDailyJSEvent(t2);
        }
        break;
      case ho:
        N(this._participantCounts, t2.participantCounts) || (this._participantCounts = t2.participantCounts, this.emitDailyJSEvent(t2));
        break;
      case fo:
        var c3 = { access: t2.access };
        t2.awaitingAccess && (c3.awaitingAccess = t2.awaitingAccess), N(this._accessState, c3) || (this._accessState = c3, this.emitDailyJSEvent(t2));
        break;
      case vo:
        if (t2.meetingSession) {
          this._meetingSessionSummary = t2.meetingSession, this.emitDailyJSEvent(t2);
          var l2 = Va(Va({}, t2), {}, { action: "meeting-session-updated" });
          this.emitDailyJSEvent(l2);
        }
        break;
      case ns:
        var u2;
        this._iframe && !t2.preserveIframe && (this._iframe.src = ""), this._updateCallState(wi), this.resetMeetingDependentVars(), this._loadedCallback && (this._loadedCallback(t2.errorMsg), this._loadedCallback = null), t2.preserveIframe;
        var d3 = e(t2, Ba);
        null != d3 && null !== (u2 = d3.error) && void 0 !== u2 && u2.details && (d3.error.details = JSON.parse(d3.error.details)), this._maybeSendToSentry(t2), this._joinedCallback && (this._joinedCallback(null, d3), this._joinedCallback = null), this.emitDailyJSEvent(d3);
        break;
      case co:
        this._callState !== wi && this._updateCallState(_i), this.resetMeetingDependentVars(), this._resolveLeave && (this._resolveLeave(), this._resolveLeave = null), this.emitDailyJSEvent(t2);
        break;
      case "selected-devices-updated":
        t2.devices && this.emitDailyJSEvent(t2);
        break;
      case Jo:
        var p2 = t2.state, h3 = t2.threshold, f2 = t2.quality, v3 = p2.state, g3 = p2.reasons;
        v3 === this._network.networkState && N(g3, this._network.networkStateReasons) && h3 === this._network.threshold && f2 === this._network.quality || (this._network.networkState = v3, this._network.networkStateReasons = g3, this._network.quality = f2, this._network.threshold = h3, t2.networkState = v3, g3.length && (t2.networkStateReasons = g3), delete t2.state, this.emitDailyJSEvent(t2));
        break;
      case qo:
        t2 && t2.cpuLoadState && this.emitDailyJSEvent(t2);
        break;
      case zo:
        t2 && void 0 !== t2.faceCounts && this.emitDailyJSEvent(t2);
        break;
      case Uo:
        var m3 = t2.activeSpeaker;
        this._activeSpeaker.peerId !== m3.peerId && (this._activeSpeaker.peerId = m3.peerId, this.emitDailyJSEvent({ action: t2.action, activeSpeaker: this._activeSpeaker }));
        break;
      case "show-local-video-changed":
        if (this._callObjectMode) return;
        var y3 = t2.show;
        this._showLocalVideo = y3, this.emitDailyJSEvent({ action: t2.action, show: y3 });
        break;
      case Vo:
        var b3 = t2.enabled;
        this._activeSpeakerMode !== b3 && (this._activeSpeakerMode = b3, this.emitDailyJSEvent({ action: t2.action, enabled: this._activeSpeakerMode }));
        break;
      case yo:
      case bo:
      case _o:
        this._waitingParticipants = t2.allWaitingParticipants, this.emitDailyJSEvent({ action: t2.action, participant: t2.participant });
        break;
      case Zo:
        N(this._receiveSettings, t2.receiveSettings) || (this._receiveSettings = t2.receiveSettings, this.emitDailyJSEvent({ action: t2.action, receiveSettings: t2.receiveSettings }));
        break;
      case es:
        this._maybeUpdateInputSettings(t2.inputSettings);
        break;
      case "send-settings-updated":
        N(this._sendSettings, t2.sendSettings) || (this._sendSettings = t2.sendSettings, this._preloadCache.sendSettings = null, this.emitDailyJSEvent({ action: t2.action, sendSettings: t2.sendSettings }));
        break;
      case "local-audio-level":
        this._localAudioLevel = t2.audioLevel, this._preloadCache.localAudioLevelObserver = null, this.emitDailyJSEvent(t2);
        break;
      case "remote-participants-audio-level":
        this._remoteParticipantsAudioLevel = t2.participantsAudioLevel, this._preloadCache.remoteParticipantsAudioLevelObserver = null, this.emitDailyJSEvent(t2);
        break;
      case Lo:
        var _3 = t2.session_id;
        this._rmpPlayerState[_3] = t2.playerState, this.emitDailyJSEvent(t2);
        break;
      case No:
        delete this._rmpPlayerState[t2.session_id], this.emitDailyJSEvent(t2);
        break;
      case Do:
        var w3 = t2.session_id, S3 = this._rmpPlayerState[w3];
        S3 && this.compareEqualForRMPUpdateEvent(S3, t2.remoteMediaPlayerState) || (this._rmpPlayerState[w3] = t2.remoteMediaPlayerState, this.emitDailyJSEvent(t2));
        break;
      case "custom-button-click":
      case "sidebar-view-changed":
        this.emitDailyJSEvent(t2);
        break;
      case go:
        var k3 = this._meetingSessionState.topology !== (t2.meetingSessionState && t2.meetingSessionState.topology);
        this._meetingSessionState = jc(t2.meetingSessionState, this._callObjectMode), (this._callObjectMode || k3) && this.emitDailyJSEvent(t2);
        break;
      case Ro:
        this._isScreenSharing = true, this.emitDailyJSEvent(t2);
        break;
      case Fo:
      case Bo:
        this._isScreenSharing = false, this.emitDailyJSEvent(t2);
        break;
      case Eo:
      case To:
      case Oo:
      case Po:
      case Ao:
      case ko:
      case Mo:
      case Co:
      case io:
      case oo:
      case Io:
      case xo:
      case "test-completed":
      case $o:
      case jo:
      case Go:
      case Qo:
      case Ko:
      case Yo:
      case ts:
      case Xo:
      case "dialin-ready":
      case "dialin-connected":
      case "dialin-error":
      case "dialin-stopped":
      case "dialin-warning":
      case "dialout-connected":
      case "dialout-answered":
      case "dialout-error":
      case "dialout-stopped":
      case "dialout-warning":
        this.emitDailyJSEvent(t2);
        break;
      case "request-fullscreen":
        this.requestFullscreen();
        break;
      case "request-exit-fullscreen":
        this.exitFullscreen();
    }
  } }, { key: "maybeEventRecordingStopped", value: function(e2, t2) {
    var n2 = "record";
    e2 && (t2.local || false !== t2[n2] || e2[n2] === t2[n2] || this.emitDailyJSEvent({ action: To }));
  } }, { key: "maybeEventRecordingStarted", value: function(e2, t2) {
    var n2 = "record";
    e2 && (t2.local || true !== t2[n2] || e2[n2] === t2[n2] || this.emitDailyJSEvent({ action: Eo }));
  } }, { key: "_trackStatePlayable", value: function(e2) {
    return !(!e2 || e2.state !== Ti);
  } }, { key: "_trackChanged", value: function(e2, t2) {
    return !((null == e2 ? void 0 : e2.id) === (null == t2 ? void 0 : t2.id));
  } }, { key: "maybeEventTrackStopped", value: function(e2, t2, n2) {
    var r3, i3, o2 = null !== (r3 = null == t2 ? void 0 : t2.tracks[e2]) && void 0 !== r3 ? r3 : null, s2 = null !== (i3 = null == n2 ? void 0 : n2.tracks[e2]) && void 0 !== i3 ? i3 : null, a2 = null == o2 ? void 0 : o2.track;
    if (a2) {
      var c3 = this._trackStatePlayable(o2), l2 = this._trackStatePlayable(s2), u2 = this._trackChanged(a2, null == s2 ? void 0 : s2.track);
      c3 && (l2 && !u2 || this.emitDailyJSEvent({ action: So, track: a2, participant: null != n2 ? n2 : t2, type: e2 }));
    }
  } }, { key: "maybeEventTrackStarted", value: function(e2, t2, n2) {
    var r3, i3, o2 = null !== (r3 = null == t2 ? void 0 : t2.tracks[e2]) && void 0 !== r3 ? r3 : null, s2 = null !== (i3 = null == n2 ? void 0 : n2.tracks[e2]) && void 0 !== i3 ? i3 : null, a2 = null == s2 ? void 0 : s2.track;
    if (a2) {
      var c3 = this._trackStatePlayable(o2), l2 = this._trackStatePlayable(s2), u2 = this._trackChanged(null == o2 ? void 0 : o2.track, a2);
      l2 && (c3 && !u2 || this.emitDailyJSEvent({ action: wo, track: a2, participant: n2, type: e2 }));
    }
  } }, { key: "maybeParticipantTracksStopped", value: function(e2, t2) {
    if (e2) for (var n2 in e2.tracks) this.maybeEventTrackStopped(n2, e2, t2);
  } }, { key: "maybeParticipantTracksStarted", value: function(e2, t2) {
    if (t2) for (var n2 in t2.tracks) this.maybeEventTrackStarted(n2, e2, t2);
  } }, { key: "compareEqualForRMPUpdateEvent", value: function(e2, t2) {
    var n2, r3;
    return e2.state === t2.state && (null === (n2 = e2.settings) || void 0 === n2 ? void 0 : n2.volume) === (null === (r3 = t2.settings) || void 0 === r3 ? void 0 : r3.volume);
  } }, { key: "emitDailyJSEvent", value: function(e2) {
    try {
      e2.callClientId = this.callClientId, this.emit(e2.action, e2);
    } catch (t2) {
      console.log("could not emit", e2, t2);
    }
  } }, { key: "compareEqualForParticipantUpdateEvent", value: function(e2, t2) {
    return !!N(e2, t2) && ((!e2.videoTrack || !t2.videoTrack || e2.videoTrack.id === t2.videoTrack.id && e2.videoTrack.muted === t2.videoTrack.muted && e2.videoTrack.enabled === t2.videoTrack.enabled) && (!e2.audioTrack || !t2.audioTrack || e2.audioTrack.id === t2.audioTrack.id && e2.audioTrack.muted === t2.audioTrack.muted && e2.audioTrack.enabled === t2.audioTrack.enabled));
  } }, { key: "nativeUtils", value: function() {
    return ys() ? "undefined" == typeof DailyNativeUtils ? (console.warn("in React Native, DailyNativeUtils is expected to be available"), null) : DailyNativeUtils : null;
  } }, { key: "updateIsPreparingToJoin", value: function(e2) {
    this._updateCallState(this._callState, e2);
  } }, { key: "_updateCallState", value: function(e2) {
    var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this._isPreparingToJoin;
    if (e2 !== this._callState || t2 !== this._isPreparingToJoin) {
      var n2 = this._callState, r3 = this._isPreparingToJoin;
      this._callState = e2, this._isPreparingToJoin = t2;
      var i3 = this._callState === bi;
      this.updateShowAndroidOngoingMeetingNotification(i3);
      var o2 = ac(n2, r3), s2 = ac(this._callState, this._isPreparingToJoin);
      o2 !== s2 && (this.updateKeepDeviceAwake(s2), this.updateDeviceAudioMode(s2), this.updateNoOpRecordingEnsuringBackgroundContinuity(s2));
    }
  } }, { key: "resetMeetingDependentVars", value: function() {
    this._participants = {}, this._participantCounts = Qa, this._waitingParticipants = {}, this._activeSpeaker = {}, this._activeSpeakerMode = false, this._didPreAuth = false, this._accessState = { access: Oi }, this._finalSummaryOfPrevSession = this._meetingSessionSummary, this._meetingSessionSummary = {}, this._meetingSessionState = jc(Ga, this._callObjectMode), this._isScreenSharing = false, this._receiveSettings = {}, this._inputSettings = void 0, this._sendSettings = {}, this._localAudioLevel = 0, this._isLocalAudioLevelObserverRunning = false, this._remoteParticipantsAudioLevel = {}, this._isRemoteParticipantsAudioLevelObserverRunning = false, this._maxAppMessageSize = rs, this._callMachineInitialized = false, this._bundleLoadTime = void 0, this._preloadCache;
  } }, { key: "updateKeepDeviceAwake", value: function(e2) {
    ys() && this.nativeUtils().setKeepDeviceAwake(e2, this.callClientId);
  } }, { key: "updateDeviceAudioMode", value: function(e2) {
    if (ys() && !this.disableReactNativeAutoDeviceManagement("audio")) {
      var t2 = e2 ? this._nativeInCallAudioMode : "idle";
      this.nativeUtils().setAudioMode(t2);
    }
  } }, { key: "updateShowAndroidOngoingMeetingNotification", value: function(e2) {
    if (ys() && this.nativeUtils().setShowOngoingMeetingNotification) {
      var t2, n2, r3, i3;
      if (this.properties.reactNativeConfig && this.properties.reactNativeConfig.androidInCallNotification) {
        var o2 = this.properties.reactNativeConfig.androidInCallNotification;
        t2 = o2.title, n2 = o2.subtitle, r3 = o2.iconName, i3 = o2.disableForCustomOverride;
      }
      i3 && (e2 = false), this.nativeUtils().setShowOngoingMeetingNotification(e2, t2, n2, r3, this.callClientId);
    }
  } }, { key: "updateNoOpRecordingEnsuringBackgroundContinuity", value: function(e2) {
    ys() && this.nativeUtils().enableNoOpRecordingEnsuringBackgroundContinuity && this.nativeUtils().enableNoOpRecordingEnsuringBackgroundContinuity(e2);
  } }, { key: "toggleParticipantAudioBasedOnNativeAudioFocus", value: function() {
    var e2;
    if (ys()) {
      var t2 = null === (e2 = this._callMachine()) || void 0 === e2 || null === (e2 = e2.store) || void 0 === e2 ? void 0 : e2.getState();
      for (var n2 in null == t2 ? void 0 : t2.streams) {
        var r3 = t2.streams[n2];
        r3 && r3.pendingTrack && "audio" === r3.pendingTrack.kind && (r3.pendingTrack.enabled = this._hasNativeAudioFocus);
      }
    }
  } }, { key: "disableReactNativeAutoDeviceManagement", value: function(e2) {
    return this.properties.reactNativeConfig && this.properties.reactNativeConfig.disableAutoDeviceManagement && this.properties.reactNativeConfig.disableAutoDeviceManagement[e2];
  } }, { key: "absoluteUrl", value: function(e2) {
    if (void 0 !== e2) {
      var t2 = document.createElement("a");
      return t2.href = e2, t2.href;
    }
  } }, { key: "sayHello", value: function() {
    var e2 = "hello, world.";
    return console.log(e2), e2;
  } }, { key: "_logUseAfterDestroy", value: function() {
    var e2 = Object.values(za)[0];
    if (this.needsLoad()) {
      if (e2 && !e2.needsLoad()) {
        var t2 = { action: cs, level: "error", code: this.strictMode ? 9995 : 9997 };
        e2.sendMessageToCallMachine(t2);
      } else if (!this.strictMode) {
        console.error("You are are attempting to use a call instance that was previously destroyed, which is unsupported. Please remove `strictMode: false` from your constructor properties to enable strict mode to track down and fix this unsupported usage.");
      }
    } else {
      var n2 = { action: cs, level: "error", code: this.strictMode ? 9995 : 9997 };
      this._messageChannel.sendMessageToCallMachine(n2, null, this.callClientId, this._iframe);
    }
  } }, { key: "_logDuplicateInstanceAttempt", value: function() {
    for (var e2 = 0, t2 = Object.values(za); e2 < t2.length; e2++) {
      var n2 = t2[e2];
      n2._callMachineInitialized ? (n2.sendMessageToCallMachine({ action: cs, level: "warn", code: this.allowMultipleCallInstances ? 9993 : 9992 }), n2._delayDuplicateInstanceLog = false) : n2._delayDuplicateInstanceLog = true;
    }
  } }, { key: "_maybeSendToSentry", value: function(e2) {
    var t2, n2, i3, o2;
    if (null !== (t2 = e2.error) && void 0 !== t2 && t2.type) {
      if (![$i, Vi, Bi].includes(e2.error.type)) return;
      if (e2.error.type === Bi && e2.error.msg.includes("deleted")) return;
    }
    var s2 = null !== (n2 = this.properties) && void 0 !== n2 && n2.url ? new URL(this.properties.url) : void 0, a2 = "production";
    s2 && s2.host.includes(".staging.daily") && (a2 = "staging");
    var c3, l2, u2, d3, p2, h3 = function(e3) {
      const t3 = [Gn(), Wn(), ii(), ni(), ui(), fi(), rr(), hi()];
      return false !== e3.autoSessionTracking && t3.push(li()), t3;
    }({}).filter(function(e3) {
      return !["BrowserApiErrors", "Breadcrumbs", "GlobalHandlers"].includes(e3.name);
    }), f2 = new jr({ dsn: "https://<EMAIL>/168844", transport: Wr, stackParser: Zr, integrations: h3, environment: a2 }), v3 = new Mt();
    if (v3.setClient(f2), f2.init(), this.session_id && v3.setExtra("sessionId", this.session_id), this.properties) {
      var g3 = Va({}, this.properties);
      g3.userName = g3.userName ? "[Filtered]" : void 0, g3.userData = g3.userData ? "[Filtered]" : void 0, g3.token = g3.token ? "[Filtered]" : void 0, v3.setExtra("properties", g3);
    }
    if (s2) {
      var m3 = s2.searchParams.get("domain");
      if (!m3) {
        var y3 = s2.host.match(/(.*?)\./);
        m3 = y3 && y3[1] || "";
      }
      m3 && v3.setTag("domain", m3);
    }
    e2.error && (v3.setTag("fatalErrorType", e2.error.type), v3.setExtra("errorDetails", e2.error.details), (null === (c3 = e2.error.details) || void 0 === c3 ? void 0 : c3.uri) && v3.setTag("serverAddress", e2.error.details.uri), (null === (l2 = e2.error.details) || void 0 === l2 ? void 0 : l2.workerGroup) && v3.setTag("workerGroup", e2.error.details.workerGroup), (null === (u2 = e2.error.details) || void 0 === u2 ? void 0 : u2.geoGroup) && v3.setTag("geoGroup", e2.error.details.geoGroup), (null === (d3 = e2.error.details) || void 0 === d3 ? void 0 : d3.on) && v3.setTag("connectionAttempt", e2.error.details.on), null !== (p2 = e2.error.details) && void 0 !== p2 && p2.bundleUrl && (v3.setTag("bundleUrl", e2.error.details.bundleUrl), v3.setTag("bundleError", e2.error.details.sourceError.type)));
    v3.setTags({ callMode: this._callObjectMode ? ys() ? "reactNative" : null !== (i3 = this.properties) && void 0 !== i3 && null !== (i3 = i3.dailyConfig) && void 0 !== i3 && null !== (i3 = i3.callMode) && void 0 !== i3 && i3.includes("prebuilt") ? this.properties.dailyConfig.callMode : "custom" : "prebuilt-frame", version: r2.version() });
    var b3 = (null === (o2 = e2.error) || void 0 === o2 ? void 0 : o2.msg) || e2.errorMsg;
    v3.captureException(new Error(b3));
  } }, { key: "_callMachine", value: function() {
    var e2;
    return null === (e2 = window._daily) || void 0 === e2 || null === (e2 = e2.instances) || void 0 === e2 || null === (e2 = e2[this.callClientId]) || void 0 === e2 ? void 0 : e2.callMachine;
  } }, { key: "_maybeUpdateInputSettings", value: function(e2) {
    if (!N(this._inputSettings, e2)) {
      var t2 = this._getInputSettings();
      this._inputSettings = e2;
      var n2 = this._getInputSettings();
      N(t2, n2) || this.emitDailyJSEvent({ action: es, inputSettings: n2 });
    }
  } }], [{ key: "supportedBrowser", value: function() {
    if (ys()) return { supported: true, mobile: true, name: "React Native", version: null, supportsScreenShare: true, supportsSfu: true, supportsVideoProcessing: false, supportsAudioProcessing: false };
    var e2 = Q.getParser(ms());
    return { supported: !!Cs(), mobile: "mobile" === e2.getPlatformType(), name: e2.getBrowserName(), version: e2.getBrowserVersion(), supportsFullscreen: !!ws(), supportsScreenShare: !!_s(), supportsSfu: !!Cs(), supportsVideoProcessing: ks(), supportsAudioProcessing: Ms() };
  } }, { key: "version", value: function() {
    return "0.77.0";
  } }, { key: "createCallObject", value: function() {
    var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
    return e2.layout = "none", new r2(null, e2);
  } }, { key: "wrap", value: function(e2) {
    var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
    if (hc(), !e2 || !e2.contentWindow || "string" != typeof e2.src) throw new Error("DailyIframe::Wrap needs an iframe-like first argument");
    return t2.layout || (t2.customLayout ? t2.layout = "custom-v1" : t2.layout = "browser"), new r2(e2, t2);
  } }, { key: "createFrame", value: function(e2, t2) {
    var n2, i3;
    hc(), e2 && t2 ? (n2 = e2, i3 = t2) : e2 && e2.append ? (n2 = e2, i3 = {}) : (n2 = document.body, i3 = e2 || {});
    var o2 = i3.iframeStyle;
    o2 || (o2 = n2 === document.body ? { position: "fixed", border: "1px solid black", backgroundColor: "white", width: "375px", height: "450px", right: "1em", bottom: "1em" } : { border: 0, width: "100%", height: "100%" });
    var s2 = document.createElement("iframe");
    window.navigator && window.navigator.userAgent.match(/Chrome\/61\./) ? s2.allow = "microphone, camera" : s2.allow = "microphone; camera; autoplay; display-capture; screen-wake-lock", s2.style.visibility = "hidden", n2.appendChild(s2), s2.style.visibility = null, Object.keys(o2).forEach(function(e3) {
      return s2.style[e3] = o2[e3];
    }), i3.layout || (i3.customLayout ? i3.layout = "custom-v1" : i3.layout = "browser");
    try {
      return new r2(s2, i3);
    } catch (e3) {
      throw n2.removeChild(s2), e3;
    }
  } }, { key: "createTransparentFrame", value: function() {
    var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
    hc();
    var t2 = document.createElement("iframe");
    return t2.allow = "microphone; camera; autoplay", t2.style.cssText = "\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      border: 0;\n      pointer-events: none;\n    ", document.body.appendChild(t2), e2.layout || (e2.layout = "custom-v1"), r2.wrap(t2, e2);
  } }, { key: "getCallInstance", value: function() {
    var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : void 0;
    return e2 ? za[e2] : Object.values(za)[0];
  } }]);
  var i2, c2, d2, h2, v2, g2, m2, y2, b2, _2, w2, S2, k2, M2, C2, E2, T2, O2, P2, A2, j2, I2, L2, D2, R2, F2, B2, U2, V2, J2, $2, q2, z2, W2, H2, G2, Y2, ee2;
}();
function oc(e2, t2) {
  var n2 = {};
  for (var r2 in e2) if (e2[r2] instanceof MediaStreamTrack) console.warn("MediaStreamTrack found in props or cache.", r2), n2[r2] = ls;
  else if ("dailyConfig" === r2) {
    if (e2[r2].modifyLocalSdpHook) {
      var i2 = window._daily.instances[t2].customCallbacks || {};
      i2.modifyLocalSdpHook = e2[r2].modifyLocalSdpHook, window._daily.instances[t2].customCallbacks = i2, delete e2[r2].modifyLocalSdpHook;
    }
    if (e2[r2].modifyRemoteSdpHook) {
      var o2 = window._daily.instances[t2].customCallbacks || {};
      o2.modifyRemoteSdpHook = e2[r2].modifyRemoteSdpHook, window._daily.instances[t2].customCallbacks = o2, delete e2[r2].modifyRemoteSdpHook;
    }
    n2[r2] = e2[r2];
  } else n2[r2] = e2[r2];
  return n2;
}
function sc(e2) {
  var t2 = arguments.length > 2 ? arguments[2] : void 0;
  if (e2 !== bi) {
    var n2 = "".concat(arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "This daily-js method", " only supported after join.");
    throw t2 && (n2 += " ".concat(t2)), console.error(n2), new Error(n2);
  }
}
function ac(e2, t2) {
  return [yi, bi].includes(e2) || t2;
}
function cc(e2, t2) {
  var n2 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "This daily-js method", r2 = arguments.length > 3 ? arguments[3] : void 0;
  if (ac(e2, t2)) {
    var i2 = "".concat(n2, " not supported after joining a meeting.");
    throw r2 && (i2 += " ".concat(r2)), console.error(i2), new Error(i2);
  }
}
function lc(e2) {
  var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "This daily-js method", n2 = arguments.length > 2 ? arguments[2] : void 0;
  if (!e2) {
    var r2 = "".concat(t2, arguments.length > 3 && void 0 !== arguments[3] && arguments[3] ? " requires preAuth() or startCamera() to initialize call state." : " requires preAuth(), startCamera(), or join() to initialize call state.");
    throw n2 && (r2 += " ".concat(n2)), console.error(r2), new Error(r2);
  }
}
function uc(e2) {
  if (e2) {
    var t2 = "A pre-call quality test is in progress. Please try ".concat(arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "This daily-js method", " again once testing has completed. Use stopTestCallQuality() to end it early.");
    throw console.error(t2), new Error(t2);
  }
}
function dc(e2) {
  if (!e2) {
    var t2 = "".concat(arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "This daily-js method", " is only supported on custom callObject instances");
    throw console.error(t2), new Error(t2);
  }
}
function pc(e2) {
  if (e2) {
    var t2 = "".concat(arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "This daily-js method", " is only supported as part of Daily's Prebuilt");
    throw console.error(t2), new Error(t2);
  }
}
function hc() {
  if (ys()) throw new Error("This daily-js method is not currently supported in React Native");
}
function fc() {
  if (!ys()) throw new Error("This daily-js method is only supported in React Native");
}
function vc(e2) {
  if (void 0 === e2) return true;
  var t2;
  if ("string" == typeof e2) t2 = e2;
  else try {
    t2 = JSON.stringify(e2), N(JSON.parse(t2), e2) || console.warn("The userData provided will be modified when serialized.");
  } catch (e3) {
    throw Error("userData must be serializable to JSON: ".concat(e3));
  }
  if (t2.length > 4096) throw Error("userData is too large (".concat(t2.length, " characters). Maximum size suppported is ").concat(4096, "."));
  return true;
}
function gc(e2, t2) {
  for (var n2 = t2.allowAllParticipantsKey, r2 = function(e3) {
    var t3 = ["local"];
    return n2 || t3.push("*"), e3 && !t3.includes(e3);
  }, i2 = function(e3) {
    return !!(void 0 === e3.layer || Number.isInteger(e3.layer) && e3.layer >= 0 || "inherit" === e3.layer);
  }, o2 = function(e3) {
    return !!e3 && (!(e3.video && !i2(e3.video)) && !(e3.screenVideo && !i2(e3.screenVideo)));
  }, s2 = 0, a2 = Object.entries(e2); s2 < a2.length; s2++) {
    var c2 = f(a2[s2], 2), l2 = c2[0], u2 = c2[1];
    if (!r2(l2) || !o2(u2)) return false;
  }
  return true;
}
function mc(e2) {
  if ("object" !== n(e2)) return false;
  for (var t2 = 0, r2 = Object.entries(e2); t2 < r2.length; t2++) {
    var i2 = f(r2[t2], 2), o2 = i2[0], s2 = i2[1];
    switch (o2) {
      case "video":
        if ("object" !== n(s2)) return false;
        for (var a2 = 0, c2 = Object.entries(s2); a2 < c2.length; a2++) {
          var l2 = f(c2[a2], 2), u2 = l2[0], d2 = l2[1];
          switch (u2) {
            case "processor":
              if (!_c(d2)) return false;
              break;
            case "settings":
              if (!wc(d2)) return false;
              break;
            default:
              return false;
          }
        }
        break;
      case "audio":
        if ("object" !== n(s2)) return false;
        for (var p2 = 0, h2 = Object.entries(s2); p2 < h2.length; p2++) {
          var v2 = f(h2[p2], 2), g2 = v2[0], m2 = v2[1];
          switch (g2) {
            case "processor":
              if (!bc(m2)) return false;
              break;
            case "settings":
              if (!wc(m2)) return false;
              break;
            default:
              return false;
          }
        }
        break;
      default:
        return false;
    }
  }
  return true;
}
function yc(e2, t2, n2) {
  var r2, i2 = [];
  e2.video && e2.video.processor && (ks(null !== (r2 = null == t2 ? void 0 : t2.useLegacyVideoProcessor) && void 0 !== r2 && r2) || (e2.video.settings ? delete e2.video.processor : delete e2.video, i2.push("video")));
  e2.audio && e2.audio.processor && (Ms() || (e2.audio.settings ? delete e2.audio.processor : delete e2.audio, i2.push("audio"))), i2.length > 0 && console.error("Ignoring settings for browser- or platform-unsupported input processor(s): ".concat(i2.join(", "))), e2.audio && e2.audio.settings && (e2.audio.settings.customTrack ? (n2.audioTrack = e2.audio.settings.customTrack, e2.audio.settings = { customTrack: ls }) : delete n2.audioTrack), e2.video && e2.video.settings && (e2.video.settings.customTrack ? (n2.videoTrack = e2.video.settings.customTrack, e2.video.settings = { customTrack: ls }) : delete n2.videoTrack);
}
function bc(e2) {
  if (ys()) return console.warn("Video processing is not yet supported in React Native"), false;
  var t2 = ["type"];
  return !!e2 && ("object" === n(e2) && (Object.keys(e2).filter(function(e3) {
    return !t2.includes(e3);
  }).forEach(function(t3) {
    console.warn("invalid key inputSettings -> audio -> processor : ".concat(t3)), delete e2[t3];
  }), !!function(e3) {
    if ("string" != typeof e3) return false;
    if (!Object.values(ds).includes(e3)) return console.error("inputSettings audio processor type invalid"), false;
    return true;
  }(e2.type)));
}
function _c(e2) {
  if (ys()) return console.warn("Video processing is not yet supported in React Native"), false;
  var t2 = ["type", "config"];
  if (!e2) return false;
  if ("object" !== n(e2)) return false;
  if (!function(e3) {
    if ("string" != typeof e3) return false;
    if (!Object.values(us).includes(e3)) return console.error("inputSettings video processor type invalid"), false;
    return true;
  }(e2.type)) return false;
  if (e2.config) {
    if ("object" !== n(e2.config)) return false;
    if (!function(e3, t3) {
      var n2 = Object.keys(t3);
      if (0 === n2.length) return true;
      var r2 = "invalid object in inputSettings -> video -> processor -> config";
      switch (e3) {
        case us.BGBLUR:
          return n2.length > 1 || "strength" !== n2[0] ? (console.error(r2), false) : !("number" != typeof t3.strength || t3.strength <= 0 || t3.strength > 1 || isNaN(t3.strength)) || (console.error("".concat(r2, "; expected: {0 < strength <= 1}, got: ").concat(t3.strength)), false);
        case us.BGIMAGE:
          return !(void 0 !== t3.source && !function(e4) {
            if ("default" === e4.source) return e4.type = "default", true;
            if (e4.source instanceof ArrayBuffer) return true;
            if (ee(e4.source)) return e4.type = "url", !!function(e5) {
              var t5 = new URL(e5), n4 = t5.pathname;
              if ("data:" === t5.protocol) try {
                var r3 = n4.substring(n4.indexOf(":") + 1, n4.indexOf(";")).split("/")[1];
                return hs.includes(r3);
              } catch (e6) {
                return console.error("failed to deduce blob content type", e6), false;
              }
              var i2 = n4.split(".").at(-1).toLowerCase().trim();
              return hs.includes(i2);
            }(e4.source) || (console.error("invalid image type; supported types: [".concat(hs.join(", "), "]")), false);
            return t4 = e4.source, n3 = Number(t4), isNaN(n3) || !Number.isInteger(n3) || n3 <= 0 || n3 > 10 ? (console.error("invalid image selection; must be an int, > 0, <= ".concat(10)), false) : (e4.type = "daily-preselect", true);
            var t4, n3;
          }(t3));
        default:
          return true;
      }
    }(e2.type, e2.config)) return false;
  }
  return Object.keys(e2).filter(function(e3) {
    return !t2.includes(e3);
  }).forEach(function(t3) {
    console.warn("invalid key inputSettings -> video -> processor : ".concat(t3)), delete e2[t3];
  }), true;
}
function wc(e2) {
  return "object" === n(e2) && (!e2.customTrack || e2.customTrack instanceof MediaStreamTrack);
}
function Sc() {
  var e2 = Object.values(us).join(" | "), t2 = Object.values(ds).join(" | ");
  return "inputSettings must be of the form: { video?: { processor?: { type: [ ".concat(e2, " ], config?: {} } }, audio?: { processor: {type: [ ").concat(t2, " ] } } }");
}
function kc(e2) {
  var t2 = e2.allowAllParticipantsKey;
  return "receiveSettings must be of the form { [<remote participant id> | ".concat(Ii).concat(t2 ? ' | "'.concat("*", '"') : "", "]: ") + '{ [video: [{ layer: [<non-negative integer> | "inherit"] } | "inherit"]], [screenVideo: [{ layer: [<non-negative integer> | "inherit"] } | "inherit"]] }}}';
}
function Mc() {
  return "customIntegrations should be an object of type ".concat(JSON.stringify(tc), ".");
}
function Cc(e2) {
  if (e2 && "object" !== n(e2) || Array.isArray(e2)) return console.error("customTrayButtons should be an Object of the type ".concat(JSON.stringify(ec), ".")), false;
  if (e2) for (var t2 = 0, r2 = Object.entries(e2); t2 < r2.length; t2++) for (var i2 = f(r2[t2], 1)[0], o2 = 0, s2 = Object.entries(e2[i2]); o2 < s2.length; o2++) {
    var a2 = f(s2[o2], 2), c2 = a2[0], l2 = a2[1], u2 = ec.id[c2];
    if (!u2) return console.error("customTrayButton does not support key ".concat(c2)), false;
    switch (c2) {
      case "iconPath":
      case "iconPathDarkMode":
        if (!ee(l2)) return console.error("customTrayButton ".concat(c2, " should be a url.")), false;
        break;
      case "visualState":
        if (!["default", "sidebar-open", "active"].includes(l2)) return console.error("customTrayButton ".concat(c2, " should be ").concat(u2, ". Got: ").concat(l2)), false;
        break;
      default:
        if (n(l2) !== u2) return console.error("customTrayButton ".concat(c2, " should be a ").concat(u2, ".")), false;
    }
  }
  return true;
}
function Ec(e2) {
  if (!e2 || e2 && "object" !== n(e2) || Array.isArray(e2)) return console.error(Mc()), false;
  for (var t2 = function(e3) {
    return "".concat(e3, " should be ").concat(tc.id[e3]);
  }, r2 = function(e3, t3) {
    return console.error("customIntegration ".concat(e3, ": ").concat(t3));
  }, i2 = 0, o2 = Object.entries(e2); i2 < o2.length; i2++) {
    var s2 = f(o2[i2], 1)[0];
    if (!("label" in e2[s2])) return r2(s2, "label is required"), false;
    if (!("location" in e2[s2])) return r2(s2, "location is required"), false;
    if (!("src" in e2[s2]) && !("srcdoc" in e2[s2])) return r2(s2, "src or srcdoc is required"), false;
    for (var a2 = 0, c2 = Object.entries(e2[s2]); a2 < c2.length; a2++) {
      var l2 = f(c2[a2], 2), u2 = l2[0], d2 = l2[1];
      switch (u2) {
        case "allow":
        case "csp":
        case "name":
        case "referrerPolicy":
        case "sandbox":
          if ("string" != typeof d2) return r2(s2, t2(u2)), false;
          break;
        case "iconURL":
          if (!ee(d2)) return r2(s2, "".concat(u2, " should be a url")), false;
          break;
        case "src":
          if ("srcdoc" in e2[s2]) return r2(s2, "cannot have both src and srcdoc"), false;
          if (!ee(d2)) return r2(s2, 'src "'.concat(d2, '" is not a valid URL')), false;
          break;
        case "srcdoc":
          if ("src" in e2[s2]) return r2(s2, "cannot have both src and srcdoc"), false;
          if ("string" != typeof d2) return r2(s2, t2(u2)), false;
          break;
        case "location":
          if (!["main", "sidebar"].includes(d2)) return r2(s2, t2(u2)), false;
          break;
        case "controlledBy":
          if ("*" !== d2 && "owners" !== d2 && (!Array.isArray(d2) || d2.some(function(e3) {
            return "string" != typeof e3;
          }))) return r2(s2, t2(u2)), false;
          break;
        case "shared":
          if ((!Array.isArray(d2) || d2.some(function(e3) {
            return "string" != typeof e3;
          })) && "owners" !== d2 && "boolean" != typeof d2) return r2(s2, t2(u2)), false;
          break;
        default:
          if (!tc.id[u2]) return console.error("customIntegration does not support key ".concat(u2)), false;
      }
    }
  }
  return true;
}
function Tc(e2, t2) {
  if (void 0 === t2) return false;
  switch (n(t2)) {
    case "string":
      return n(e2) === t2;
    case "object":
      if ("object" !== n(e2)) return false;
      for (var r2 in e2) if (!Tc(e2[r2], t2[r2])) return false;
      return true;
    default:
      return false;
  }
}
function Oc(e2, t2) {
  var n2 = e2.sessionId, r2 = e2.toEndPoint, i2 = e2.callerId, o2 = e2.useSipRefer;
  if (!n2 || !r2) throw new Error("".concat(t2, "() requires a sessionId and toEndPoint"));
  if ("string" != typeof n2 || "string" != typeof r2) throw new Error("Invalid paramater: sessionId and toEndPoint must be of type string");
  if (o2 && !r2.startsWith("sip:")) throw new Error('"toEndPoint" must be a "sip" address');
  if (!r2.startsWith("sip:") && !r2.startsWith("+")) throw new Error("toEndPoint: ".concat(r2, ' must starts with either "sip:" or "+"'));
  if (i2 && "string" != typeof i2) throw new Error("callerId must be of type string");
  if (i2 && !r2.startsWith("+")) throw new Error("callerId is only valid when transferring to a PSTN number");
}
function Pc(e2) {
  if ("object" !== n(e2)) throw new Error('RemoteMediaPlayerSettings: must be "object" type');
  if (e2.state && !Object.values(ps).includes(e2.state)) throw new Error("Invalid value for RemoteMediaPlayerSettings.state, valid values are: " + JSON.stringify(ps));
  if (e2.volume) {
    if ("number" != typeof e2.volume) throw new Error('RemoteMediaPlayerSettings.volume: must be "number" type');
    if (e2.volume < 0 || e2.volume > 2) throw new Error("RemoteMediaPlayerSettings.volume: must be between 0.0 - 2.0");
  }
}
function Ac(e2, t2, n2) {
  return !("number" != typeof e2 || e2 < t2 || e2 > n2);
}
function jc(e2, t2) {
  return e2 && !t2 && delete e2.data, e2;
}

// node_modules/@pipecat-ai/daily-transport/dist/index.module.js
function $parcel$interopDefault(a2) {
  return a2 && a2.__esModule ? a2.default : a2;
}
function $parcel$export(e2, n2, v2, s2) {
  Object.defineProperty(e2, n2, { get: v2, set: s2, enumerable: true, configurable: true });
}
var $683f111f61e07358$exports = {};
$parcel$export($683f111f61e07358$exports, "DailyRTVIMessageType", () => $683f111f61e07358$export$ef180de88fd317cc);
$parcel$export($683f111f61e07358$exports, "DailyTransport", () => $683f111f61e07358$export$b1ca982aa1e488c1);
var $6d4b7449a1e1544a$export$13afda237b1c9846 = class {
  /**
  * Converts Float32Array of amplitude data to ArrayBuffer in Int16Array format
  * @param {Float32Array} float32Array
  * @returns {ArrayBuffer}
  */
  static floatTo16BitPCM(float32Array) {
    const buffer = new ArrayBuffer(float32Array.length * 2);
    const view = new DataView(buffer);
    let offset = 0;
    for (let i2 = 0; i2 < float32Array.length; i2++, offset += 2) {
      let s2 = Math.max(-1, Math.min(1, float32Array[i2]));
      view.setInt16(offset, s2 < 0 ? s2 * 32768 : s2 * 32767, true);
    }
    return buffer;
  }
  /**
  * Concatenates two ArrayBuffers
  * @param {ArrayBuffer} leftBuffer
  * @param {ArrayBuffer} rightBuffer
  * @returns {ArrayBuffer}
  */
  static mergeBuffers(leftBuffer, rightBuffer) {
    const tmpArray = new Uint8Array(leftBuffer.byteLength + rightBuffer.byteLength);
    tmpArray.set(new Uint8Array(leftBuffer), 0);
    tmpArray.set(new Uint8Array(rightBuffer), leftBuffer.byteLength);
    return tmpArray.buffer;
  }
  /**
  * Packs data into an Int16 format
  * @private
  * @param {number} size 0 = 1x Int16, 1 = 2x Int16
  * @param {number} arg value to pack
  * @returns
  */
  _packData(size, arg) {
    return [
      new Uint8Array([
        arg,
        arg >> 8
      ]),
      new Uint8Array([
        arg,
        arg >> 8,
        arg >> 16,
        arg >> 24
      ])
    ][size];
  }
  /**
  * Packs audio into "audio/wav" Blob
  * @param {number} sampleRate
  * @param {{bitsPerSample: number, channels: Array<Float32Array>, data: Int16Array}} audio
  * @returns {WavPackerAudioType}
  */
  pack(sampleRate, audio) {
    if (!(audio == null ? void 0 : audio.bitsPerSample)) throw new Error(`Missing "bitsPerSample"`);
    else if (!(audio == null ? void 0 : audio.channels)) throw new Error(`Missing "channels"`);
    else if (!(audio == null ? void 0 : audio.data)) throw new Error(`Missing "data"`);
    const { bitsPerSample, channels, data } = audio;
    const output = [
      // Header
      "RIFF",
      this._packData(1, 52),
      "WAVE",
      // chunk 1
      "fmt ",
      this._packData(1, 16),
      this._packData(0, 1),
      this._packData(0, channels.length),
      this._packData(1, sampleRate),
      this._packData(1, sampleRate * channels.length * bitsPerSample / 8),
      this._packData(0, channels.length * bitsPerSample / 8),
      this._packData(0, bitsPerSample),
      // chunk 2
      "data",
      this._packData(1, channels[0].length * channels.length * bitsPerSample / 8),
      data
    ];
    const blob = new Blob(output, {
      type: "audio/mpeg"
    });
    const url = URL.createObjectURL(blob);
    return {
      blob,
      url,
      channelCount: channels.length,
      sampleRate,
      duration: data.byteLength / (channels.length * sampleRate * 2)
    };
  }
};
globalThis.WavPacker = $6d4b7449a1e1544a$export$13afda237b1c9846;
var $03f71ce85e00ada6$var$octave8Frequencies = [
  4186.01,
  4434.92,
  4698.63,
  4978.03,
  5274.04,
  5587.65,
  5919.91,
  6271.93,
  6644.88,
  7040,
  7458.62,
  7902.13
];
var $03f71ce85e00ada6$var$octave8FrequencyLabels = [
  "C",
  "C#",
  "D",
  "D#",
  "E",
  "F",
  "F#",
  "G",
  "G#",
  "A",
  "A#",
  "B"
];
var $03f71ce85e00ada6$export$776c63898ae5b636 = [];
var $03f71ce85e00ada6$export$facd167cc27ea9b0 = [];
for (let i2 = 1; i2 <= 8; i2++) for (let f2 = 0; f2 < $03f71ce85e00ada6$var$octave8Frequencies.length; f2++) {
  const freq = $03f71ce85e00ada6$var$octave8Frequencies[f2];
  $03f71ce85e00ada6$export$776c63898ae5b636.push(freq / Math.pow(2, 8 - i2));
  $03f71ce85e00ada6$export$facd167cc27ea9b0.push($03f71ce85e00ada6$var$octave8FrequencyLabels[f2] + i2);
}
var $03f71ce85e00ada6$var$voiceFrequencyRange = [
  32,
  2e3
];
var $03f71ce85e00ada6$export$dbc1581ed2cfa183 = $03f71ce85e00ada6$export$776c63898ae5b636.filter((_2, i2) => {
  return $03f71ce85e00ada6$export$776c63898ae5b636[i2] > $03f71ce85e00ada6$var$voiceFrequencyRange[0] && $03f71ce85e00ada6$export$776c63898ae5b636[i2] < $03f71ce85e00ada6$var$voiceFrequencyRange[1];
});
var $03f71ce85e00ada6$export$30a6f2881311088f = $03f71ce85e00ada6$export$facd167cc27ea9b0.filter((_2, i2) => {
  return $03f71ce85e00ada6$export$776c63898ae5b636[i2] > $03f71ce85e00ada6$var$voiceFrequencyRange[0] && $03f71ce85e00ada6$export$776c63898ae5b636[i2] < $03f71ce85e00ada6$var$voiceFrequencyRange[1];
});
var $f32f064564ee62f6$export$2c3136da0bf130f9 = class _$f32f064564ee62f6$export$2c3136da0bf130f9 {
  /**
  * Retrieves frequency domain data from an AnalyserNode adjusted to a decibel range
  * returns human-readable formatting and labels
  * @param {AnalyserNode} analyser
  * @param {number} sampleRate
  * @param {Float32Array} [fftResult]
  * @param {"frequency"|"music"|"voice"} [analysisType]
  * @param {number} [minDecibels] default -100
  * @param {number} [maxDecibels] default -30
  * @returns {AudioAnalysisOutputType}
  */
  static getFrequencies(analyser, sampleRate, fftResult, analysisType = "frequency", minDecibels = -100, maxDecibels = -30) {
    if (!fftResult) {
      fftResult = new Float32Array(analyser.frequencyBinCount);
      analyser.getFloatFrequencyData(fftResult);
    }
    const nyquistFrequency = sampleRate / 2;
    const frequencyStep = 1 / fftResult.length * nyquistFrequency;
    let outputValues;
    let frequencies;
    let labels;
    if (analysisType === "music" || analysisType === "voice") {
      const useFrequencies = analysisType === "voice" ? (0, $03f71ce85e00ada6$export$dbc1581ed2cfa183) : (0, $03f71ce85e00ada6$export$776c63898ae5b636);
      const aggregateOutput = Array(useFrequencies.length).fill(minDecibels);
      for (let i2 = 0; i2 < fftResult.length; i2++) {
        const frequency = i2 * frequencyStep;
        const amplitude = fftResult[i2];
        for (let n2 = useFrequencies.length - 1; n2 >= 0; n2--) if (frequency > useFrequencies[n2]) {
          aggregateOutput[n2] = Math.max(aggregateOutput[n2], amplitude);
          break;
        }
      }
      outputValues = aggregateOutput;
      frequencies = analysisType === "voice" ? (0, $03f71ce85e00ada6$export$dbc1581ed2cfa183) : (0, $03f71ce85e00ada6$export$776c63898ae5b636);
      labels = analysisType === "voice" ? (0, $03f71ce85e00ada6$export$30a6f2881311088f) : (0, $03f71ce85e00ada6$export$facd167cc27ea9b0);
    } else {
      outputValues = Array.from(fftResult);
      frequencies = outputValues.map((_2, i2) => frequencyStep * i2);
      labels = frequencies.map((f2) => `${f2.toFixed(2)} Hz`);
    }
    const normalizedOutput = outputValues.map((v2) => {
      return Math.max(0, Math.min((v2 - minDecibels) / (maxDecibels - minDecibels), 1));
    });
    const values = new Float32Array(normalizedOutput);
    return {
      values,
      frequencies,
      labels
    };
  }
  /**
  * Creates a new AudioAnalysis instance for an HTMLAudioElement
  * @param {HTMLAudioElement} audioElement
  * @param {AudioBuffer|null} [audioBuffer] If provided, will cache all frequency domain data from the buffer
  * @returns {AudioAnalysis}
  */
  constructor(audioElement, audioBuffer = null) {
    this.fftResults = [];
    if (audioBuffer) {
      const { length, sampleRate } = audioBuffer;
      const offlineAudioContext = new OfflineAudioContext({
        length,
        sampleRate
      });
      const source = offlineAudioContext.createBufferSource();
      source.buffer = audioBuffer;
      const analyser = offlineAudioContext.createAnalyser();
      analyser.fftSize = 8192;
      analyser.smoothingTimeConstant = 0.1;
      source.connect(analyser);
      const renderQuantumInSeconds = 1 / 60;
      const durationInSeconds = length / sampleRate;
      const analyze = (index) => {
        const suspendTime = renderQuantumInSeconds * index;
        if (suspendTime < durationInSeconds) offlineAudioContext.suspend(suspendTime).then(() => {
          const fftResult = new Float32Array(analyser.frequencyBinCount);
          analyser.getFloatFrequencyData(fftResult);
          this.fftResults.push(fftResult);
          analyze(index + 1);
        });
        if (index === 1) offlineAudioContext.startRendering();
        else offlineAudioContext.resume();
      };
      source.start(0);
      analyze(1);
      this.audio = audioElement;
      this.context = offlineAudioContext;
      this.analyser = analyser;
      this.sampleRate = sampleRate;
      this.audioBuffer = audioBuffer;
    } else {
      const audioContext = new AudioContext();
      const track2 = audioContext.createMediaElementSource(audioElement);
      const analyser = audioContext.createAnalyser();
      analyser.fftSize = 8192;
      analyser.smoothingTimeConstant = 0.1;
      track2.connect(analyser);
      analyser.connect(audioContext.destination);
      this.audio = audioElement;
      this.context = audioContext;
      this.analyser = analyser;
      this.sampleRate = this.context.sampleRate;
      this.audioBuffer = null;
    }
  }
  /**
  * Gets the current frequency domain data from the playing audio track
  * @param {"frequency"|"music"|"voice"} [analysisType]
  * @param {number} [minDecibels] default -100
  * @param {number} [maxDecibels] default -30
  * @returns {AudioAnalysisOutputType}
  */
  getFrequencies(analysisType = "frequency", minDecibels = -100, maxDecibels = -30) {
    let fftResult = null;
    if (this.audioBuffer && this.fftResults.length) {
      const pct = this.audio.currentTime / this.audio.duration;
      const index = Math.min(pct * this.fftResults.length | 0, this.fftResults.length - 1);
      fftResult = this.fftResults[index];
    }
    return _$f32f064564ee62f6$export$2c3136da0bf130f9.getFrequencies(this.analyser, this.sampleRate, fftResult, analysisType, minDecibels, maxDecibels);
  }
  /**
  * Resume the internal AudioContext if it was suspended due to the lack of
  * user interaction when the AudioAnalysis was instantiated.
  * @returns {Promise<true>}
  */
  async resumeIfSuspended() {
    if (this.context.state === "suspended") await this.context.resume();
    return true;
  }
};
globalThis.AudioAnalysis = $f32f064564ee62f6$export$2c3136da0bf130f9;
var $29a8a70a9466b14f$export$50b76700e2b15e9 = `
class StreamProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    this.hasStarted = false;
    this.hasInterrupted = false;
    this.outputBuffers = [];
    this.bufferLength = 128;
    this.write = { buffer: new Float32Array(this.bufferLength), trackId: null };
    this.writeOffset = 0;
    this.trackSampleOffsets = {};
    this.port.onmessage = (event) => {
      if (event.data) {
        const payload = event.data;
        if (payload.event === 'write') {
          const int16Array = payload.buffer;
          const float32Array = new Float32Array(int16Array.length);
          for (let i = 0; i < int16Array.length; i++) {
            float32Array[i] = int16Array[i] / 0x8000; // Convert Int16 to Float32
          }
          this.writeData(float32Array, payload.trackId);
        } else if (
          payload.event === 'offset' ||
          payload.event === 'interrupt'
        ) {
          const requestId = payload.requestId;
          const trackId = this.write.trackId;
          const offset = this.trackSampleOffsets[trackId] || 0;
          this.port.postMessage({
            event: 'offset',
            requestId,
            trackId,
            offset,
          });
          if (payload.event === 'interrupt') {
            this.hasInterrupted = true;
          }
        } else {
          throw new Error(\`Unhandled event "\${payload.event}"\`);
        }
      }
    };
  }

  writeData(float32Array, trackId = null) {
    let { buffer } = this.write;
    let offset = this.writeOffset;
    for (let i = 0; i < float32Array.length; i++) {
      buffer[offset++] = float32Array[i];
      if (offset >= buffer.length) {
        this.outputBuffers.push(this.write);
        this.write = { buffer: new Float32Array(this.bufferLength), trackId };
        buffer = this.write.buffer;
        offset = 0;
      }
    }
    this.writeOffset = offset;
    return true;
  }

  process(inputs, outputs, parameters) {
    const output = outputs[0];
    const outputChannelData = output[0];
    const outputBuffers = this.outputBuffers;
    if (this.hasInterrupted) {
      this.port.postMessage({ event: 'stop' });
      return false;
    } else if (outputBuffers.length) {
      this.hasStarted = true;
      const { buffer, trackId } = outputBuffers.shift();
      for (let i = 0; i < outputChannelData.length; i++) {
        outputChannelData[i] = buffer[i] || 0;
      }
      if (trackId) {
        this.trackSampleOffsets[trackId] =
          this.trackSampleOffsets[trackId] || 0;
        this.trackSampleOffsets[trackId] += buffer.length;
      }
      return true;
    } else if (this.hasStarted) {
      this.port.postMessage({ event: 'stop' });
      return false;
    } else {
      return true;
    }
  }
}

registerProcessor('stream_processor', StreamProcessor);
`;
var $29a8a70a9466b14f$var$script = new Blob([
  $29a8a70a9466b14f$export$50b76700e2b15e9
], {
  type: "application/javascript"
});
var $29a8a70a9466b14f$var$src = URL.createObjectURL($29a8a70a9466b14f$var$script);
var $29a8a70a9466b14f$export$bfa8c596114d74df = $29a8a70a9466b14f$var$src;
var $d0a969833958d9e7$export$9698d62c78b8f366 = class {
  /**
  * Creates a new WavStreamPlayer instance
  * @param {{sampleRate?: number}} options
  * @returns {WavStreamPlayer}
  */
  constructor({ sampleRate = 44100 } = {}) {
    this.scriptSrc = (0, $29a8a70a9466b14f$export$bfa8c596114d74df);
    this.sampleRate = sampleRate;
    this.context = null;
    this.stream = null;
    this.analyser = null;
    this.trackSampleOffsets = {};
    this.interruptedTrackIds = {};
  }
  /**
  * Connects the audio context and enables output to speakers
  * @returns {Promise<true>}
  */
  async connect() {
    this.context = new AudioContext({
      sampleRate: this.sampleRate
    });
    if (this._speakerID) this.context.setSinkId(this._speakerID);
    if (this.context.state === "suspended") await this.context.resume();
    try {
      await this.context.audioWorklet.addModule(this.scriptSrc);
    } catch (e2) {
      console.error(e2);
      throw new Error(`Could not add audioWorklet module: ${this.scriptSrc}`);
    }
    const analyser = this.context.createAnalyser();
    analyser.fftSize = 8192;
    analyser.smoothingTimeConstant = 0.1;
    this.analyser = analyser;
    return true;
  }
  /**
  * Gets the current frequency domain data from the playing track
  * @param {"frequency"|"music"|"voice"} [analysisType]
  * @param {number} [minDecibels] default -100
  * @param {number} [maxDecibels] default -30
  * @returns {import('./analysis/audio_analysis.js').AudioAnalysisOutputType}
  */
  getFrequencies(analysisType = "frequency", minDecibels = -100, maxDecibels = -30) {
    if (!this.analyser) throw new Error("Not connected, please call .connect() first");
    return (0, $f32f064564ee62f6$export$2c3136da0bf130f9).getFrequencies(this.analyser, this.sampleRate, null, analysisType, minDecibels, maxDecibels);
  }
  /**
  * @param {string} speaker deviceId
  */
  async updateSpeaker(speaker) {
    const _prevSpeaker = this._speakerID;
    this._speakerID = speaker;
    if (this.context) try {
      if (speaker === "default") await this.context.setSinkId();
      else await this.context.setSinkId(speaker);
    } catch (e2) {
      console.error(`Could not set sinkId to ${speaker}: ${e2}`);
      this._speakerID = _prevSpeaker;
    }
  }
  /**
  * Starts audio streaming
  * @private
  * @returns {Promise<true>}
  */
  _start() {
    const streamNode = new AudioWorkletNode(this.context, "stream_processor");
    streamNode.connect(this.context.destination);
    streamNode.port.onmessage = (e2) => {
      const { event } = e2.data;
      if (event === "stop") {
        streamNode.disconnect();
        this.stream = null;
      } else if (event === "offset") {
        const { requestId, trackId, offset } = e2.data;
        const currentTime = offset / this.sampleRate;
        this.trackSampleOffsets[requestId] = {
          trackId,
          offset,
          currentTime
        };
      }
    };
    this.analyser.disconnect();
    streamNode.connect(this.analyser);
    this.stream = streamNode;
    return true;
  }
  /**
  * Adds 16BitPCM data to the currently playing audio stream
  * You can add chunks beyond the current play point and they will be queued for play
  * @param {ArrayBuffer|Int16Array} arrayBuffer
  * @param {string} [trackId]
  * @returns {Int16Array}
  */
  add16BitPCM(arrayBuffer, trackId = "default") {
    if (typeof trackId !== "string") throw new Error(`trackId must be a string`);
    else if (this.interruptedTrackIds[trackId]) return;
    if (!this.stream) this._start();
    let buffer;
    if (arrayBuffer instanceof Int16Array) buffer = arrayBuffer;
    else if (arrayBuffer instanceof ArrayBuffer) buffer = new Int16Array(arrayBuffer);
    else throw new Error(`argument must be Int16Array or ArrayBuffer`);
    this.stream.port.postMessage({
      event: "write",
      buffer,
      trackId
    });
    return buffer;
  }
  /**
  * Gets the offset (sample count) of the currently playing stream
  * @param {boolean} [interrupt]
  * @returns {{trackId: string|null, offset: number, currentTime: number}}
  */
  async getTrackSampleOffset(interrupt = false) {
    if (!this.stream) return null;
    const requestId = crypto.randomUUID();
    this.stream.port.postMessage({
      event: interrupt ? "interrupt" : "offset",
      requestId
    });
    let trackSampleOffset;
    while (!trackSampleOffset) {
      trackSampleOffset = this.trackSampleOffsets[requestId];
      await new Promise((r2) => setTimeout(() => r2(), 1));
    }
    const { trackId } = trackSampleOffset;
    if (interrupt && trackId) this.interruptedTrackIds[trackId] = true;
    return trackSampleOffset;
  }
  /**
  * Strips the current stream and returns the sample offset of the audio
  * @param {boolean} [interrupt]
  * @returns {{trackId: string|null, offset: number, currentTime: number}}
  */
  async interrupt() {
    return this.getTrackSampleOffset(true);
  }
};
globalThis.WavStreamPlayer = $d0a969833958d9e7$export$9698d62c78b8f366;
var $8e1d1e6ff08f6fb5$var$AudioProcessorWorklet = `
class AudioProcessor extends AudioWorkletProcessor {

  constructor() {
    super();
    this.port.onmessage = this.receive.bind(this);
    this.initialize();
  }

  initialize() {
    this.foundAudio = false;
    this.recording = false;
    this.chunks = [];
  }

  /**
   * Concatenates sampled chunks into channels
   * Format is chunk[Left[], Right[]]
   */
  readChannelData(chunks, channel = -1, maxChannels = 9) {
    let channelLimit;
    if (channel !== -1) {
      if (chunks[0] && chunks[0].length - 1 < channel) {
        throw new Error(
          \`Channel \${channel} out of range: max \${chunks[0].length}\`
        );
      }
      channelLimit = channel + 1;
    } else {
      channel = 0;
      channelLimit = Math.min(chunks[0] ? chunks[0].length : 1, maxChannels);
    }
    const channels = [];
    for (let n = channel; n < channelLimit; n++) {
      const length = chunks.reduce((sum, chunk) => {
        return sum + chunk[n].length;
      }, 0);
      const buffers = chunks.map((chunk) => chunk[n]);
      const result = new Float32Array(length);
      let offset = 0;
      for (let i = 0; i < buffers.length; i++) {
        result.set(buffers[i], offset);
        offset += buffers[i].length;
      }
      channels[n] = result;
    }
    return channels;
  }

  /**
   * Combines parallel audio data into correct format,
   * channels[Left[], Right[]] to float32Array[LRLRLRLR...]
   */
  formatAudioData(channels) {
    if (channels.length === 1) {
      // Simple case is only one channel
      const float32Array = channels[0].slice();
      const meanValues = channels[0].slice();
      return { float32Array, meanValues };
    } else {
      const float32Array = new Float32Array(
        channels[0].length * channels.length
      );
      const meanValues = new Float32Array(channels[0].length);
      for (let i = 0; i < channels[0].length; i++) {
        const offset = i * channels.length;
        let meanValue = 0;
        for (let n = 0; n < channels.length; n++) {
          float32Array[offset + n] = channels[n][i];
          meanValue += channels[n][i];
        }
        meanValues[i] = meanValue / channels.length;
      }
      return { float32Array, meanValues };
    }
  }

  /**
   * Converts 32-bit float data to 16-bit integers
   */
  floatTo16BitPCM(float32Array) {
    const buffer = new ArrayBuffer(float32Array.length * 2);
    const view = new DataView(buffer);
    let offset = 0;
    for (let i = 0; i < float32Array.length; i++, offset += 2) {
      let s = Math.max(-1, Math.min(1, float32Array[i]));
      view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
    }
    return buffer;
  }

  /**
   * Retrieves the most recent amplitude values from the audio stream
   * @param {number} channel
   */
  getValues(channel = -1) {
    const channels = this.readChannelData(this.chunks, channel);
    const { meanValues } = this.formatAudioData(channels);
    return { meanValues, channels };
  }

  /**
   * Exports chunks as an audio/wav file
   */
  export() {
    const channels = this.readChannelData(this.chunks);
    const { float32Array, meanValues } = this.formatAudioData(channels);
    const audioData = this.floatTo16BitPCM(float32Array);
    return {
      meanValues: meanValues,
      audio: {
        bitsPerSample: 16,
        channels: channels,
        data: audioData,
      },
    };
  }

  receive(e) {
    const { event, id } = e.data;
    let receiptData = {};
    switch (event) {
      case 'start':
        this.recording = true;
        break;
      case 'stop':
        this.recording = false;
        break;
      case 'clear':
        this.initialize();
        break;
      case 'export':
        receiptData = this.export();
        break;
      case 'read':
        receiptData = this.getValues();
        break;
      default:
        break;
    }
    // Always send back receipt
    this.port.postMessage({ event: 'receipt', id, data: receiptData });
  }

  sendChunk(chunk) {
    const channels = this.readChannelData([chunk]);
    const { float32Array, meanValues } = this.formatAudioData(channels);
    const rawAudioData = this.floatTo16BitPCM(float32Array);
    const monoAudioData = this.floatTo16BitPCM(meanValues);
    this.port.postMessage({
      event: 'chunk',
      data: {
        mono: monoAudioData,
        raw: rawAudioData,
      },
    });
  }

  process(inputList, outputList, parameters) {
    // Copy input to output (e.g. speakers)
    // Note that this creates choppy sounds with Mac products
    const sourceLimit = Math.min(inputList.length, outputList.length);
    for (let inputNum = 0; inputNum < sourceLimit; inputNum++) {
      const input = inputList[inputNum];
      const output = outputList[inputNum];
      const channelCount = Math.min(input.length, output.length);
      for (let channelNum = 0; channelNum < channelCount; channelNum++) {
        input[channelNum].forEach((sample, i) => {
          output[channelNum][i] = sample;
        });
      }
    }
    const inputs = inputList[0];
    // There's latency at the beginning of a stream before recording starts
    // Make sure we actually receive audio data before we start storing chunks
    let sliceIndex = 0;
    if (!this.foundAudio) {
      for (const channel of inputs) {
        sliceIndex = 0; // reset for each channel
        if (this.foundAudio) {
          break;
        }
        if (channel) {
          for (const value of channel) {
            if (value !== 0) {
              // find only one non-zero entry in any channel
              this.foundAudio = true;
              break;
            } else {
              sliceIndex++;
            }
          }
        }
      }
    }
    if (inputs && inputs[0] && this.foundAudio && this.recording) {
      // We need to copy the TypedArray, because the \`process\`
      // internals will reuse the same buffer to hold each input
      const chunk = inputs.map((input) => input.slice(sliceIndex));
      this.chunks.push(chunk);
      this.sendChunk(chunk);
    }
    return true;
  }
}

registerProcessor('audio_processor', AudioProcessor);
`;
var $8e1d1e6ff08f6fb5$var$script = new Blob([
  $8e1d1e6ff08f6fb5$var$AudioProcessorWorklet
], {
  type: "application/javascript"
});
var $8e1d1e6ff08f6fb5$var$src = URL.createObjectURL($8e1d1e6ff08f6fb5$var$script);
var $8e1d1e6ff08f6fb5$export$1f65f50a8cbff43c = $8e1d1e6ff08f6fb5$var$src;
var $62bc376044a05513$export$439b217ca659a877 = class {
  /**
  * Create a new WavRecorder instance
  * @param {{sampleRate?: number, outputToSpeakers?: boolean, debug?: boolean}} [options]
  * @returns {WavRecorder}
  */
  constructor({ sampleRate = 44100, outputToSpeakers = false, debug = false } = {}) {
    this.scriptSrc = (0, $8e1d1e6ff08f6fb5$export$1f65f50a8cbff43c);
    this.sampleRate = sampleRate;
    this.outputToSpeakers = outputToSpeakers;
    this.debug = !!debug;
    this._deviceChangeCallback = null;
    this._devices = [];
    this.deviceSelection = null;
    this.stream = null;
    this.processor = null;
    this.source = null;
    this.node = null;
    this.recording = false;
    this._lastEventId = 0;
    this.eventReceipts = {};
    this.eventTimeout = 5e3;
    this._chunkProcessor = () => {
    };
    this._chunkProcessorSize = void 0;
    this._chunkProcessorBuffer = {
      raw: new ArrayBuffer(0),
      mono: new ArrayBuffer(0)
    };
  }
  /**
  * Decodes audio data from multiple formats to a Blob, url, Float32Array and AudioBuffer
  * @param {Blob|Float32Array|Int16Array|ArrayBuffer|number[]} audioData
  * @param {number} sampleRate
  * @param {number} fromSampleRate
  * @returns {Promise<DecodedAudioType>}
  */
  static async decode(audioData, sampleRate = 44100, fromSampleRate = -1) {
    const context = new AudioContext({
      sampleRate
    });
    let arrayBuffer;
    let blob;
    if (audioData instanceof Blob) {
      if (fromSampleRate !== -1) throw new Error(`Can not specify "fromSampleRate" when reading from Blob`);
      blob = audioData;
      arrayBuffer = await blob.arrayBuffer();
    } else if (audioData instanceof ArrayBuffer) {
      if (fromSampleRate !== -1) throw new Error(`Can not specify "fromSampleRate" when reading from ArrayBuffer`);
      arrayBuffer = audioData;
      blob = new Blob([
        arrayBuffer
      ], {
        type: "audio/wav"
      });
    } else {
      let float32Array;
      let data;
      if (audioData instanceof Int16Array) {
        data = audioData;
        float32Array = new Float32Array(audioData.length);
        for (let i2 = 0; i2 < audioData.length; i2++) float32Array[i2] = audioData[i2] / 32768;
      } else if (audioData instanceof Float32Array) float32Array = audioData;
      else if (audioData instanceof Array) float32Array = new Float32Array(audioData);
      else throw new Error(`"audioData" must be one of: Blob, Float32Arrray, Int16Array, ArrayBuffer, Array<number>`);
      if (fromSampleRate === -1) throw new Error(`Must specify "fromSampleRate" when reading from Float32Array, In16Array or Array`);
      else if (fromSampleRate < 3e3) throw new Error(`Minimum "fromSampleRate" is 3000 (3kHz)`);
      if (!data) data = (0, $6d4b7449a1e1544a$export$13afda237b1c9846).floatTo16BitPCM(float32Array);
      const audio = {
        bitsPerSample: 16,
        channels: [
          float32Array
        ],
        data
      };
      const packer = new (0, $6d4b7449a1e1544a$export$13afda237b1c9846)();
      const result = packer.pack(fromSampleRate, audio);
      blob = result.blob;
      arrayBuffer = await blob.arrayBuffer();
    }
    const audioBuffer = await context.decodeAudioData(arrayBuffer);
    const values = audioBuffer.getChannelData(0);
    const url = URL.createObjectURL(blob);
    return {
      blob,
      url,
      values,
      audioBuffer
    };
  }
  /**
  * Logs data in debug mode
  * @param {...any} arguments
  * @returns {true}
  */
  log() {
    if (this.debug) this.log(...arguments);
    return true;
  }
  /**
  * Retrieves the current sampleRate for the recorder
  * @returns {number}
  */
  getSampleRate() {
    return this.sampleRate;
  }
  /**
  * Retrieves the current status of the recording
  * @returns {"ended"|"paused"|"recording"}
  */
  getStatus() {
    if (!this.processor) return "ended";
    else if (!this.recording) return "paused";
    else return "recording";
  }
  /**
  * Sends an event to the AudioWorklet
  * @private
  * @param {string} name
  * @param {{[key: string]: any}} data
  * @param {AudioWorkletNode} [_processor]
  * @returns {Promise<{[key: string]: any}>}
  */
  async _event(name, data = {}, _processor = null) {
    _processor = _processor || this.processor;
    if (!_processor) throw new Error("Can not send events without recording first");
    const message = {
      event: name,
      id: this._lastEventId++,
      data
    };
    _processor.port.postMessage(message);
    const t0 = (/* @__PURE__ */ new Date()).valueOf();
    while (!this.eventReceipts[message.id]) {
      if ((/* @__PURE__ */ new Date()).valueOf() - t0 > this.eventTimeout) throw new Error(`Timeout waiting for "${name}" event`);
      await new Promise((res) => setTimeout(() => res(true), 1));
    }
    const payload = this.eventReceipts[message.id];
    delete this.eventReceipts[message.id];
    return payload;
  }
  /**
  * Sets device change callback, remove if callback provided is `null`
  * @param {(Array<MediaDeviceInfo & {default: boolean}>): void|null} callback
  * @returns {true}
  */
  listenForDeviceChange(callback) {
    if (callback === null && this._deviceChangeCallback) {
      navigator.mediaDevices.removeEventListener("devicechange", this._deviceChangeCallback);
      this._deviceChangeCallback = null;
    } else if (callback !== null) {
      let lastId = 0;
      let lastDevices = [];
      const serializeDevices = (devices) => devices.map((d2) => d2.deviceId).sort().join(",");
      const cb = async () => {
        let id = ++lastId;
        const devices = await this.listDevices();
        if (id === lastId) {
          if (serializeDevices(lastDevices) !== serializeDevices(devices)) {
            lastDevices = devices;
            callback(devices.slice());
          }
        }
      };
      navigator.mediaDevices.addEventListener("devicechange", cb);
      cb();
      this._deviceChangeCallback = cb;
    }
    return true;
  }
  /**
  * Manually request permission to use the microphone
  * @returns {Promise<true>}
  */
  async requestPermission() {
    const permissionStatus = await navigator.permissions.query({
      name: "microphone"
    });
    if (permissionStatus.state === "denied") window.alert("You must grant microphone access to use this feature.");
    else if (permissionStatus.state === "prompt") try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: true
      });
      const tracks = stream.getTracks();
      tracks.forEach((track2) => track2.stop());
    } catch (e2) {
      window.alert("You must grant microphone access to use this feature.");
    }
    return true;
  }
  /**
  * List all eligible devices for recording, will request permission to use microphone
  * @returns {Promise<Array<MediaDeviceInfo & {default: boolean}>>}
  */
  async listDevices() {
    if (!navigator.mediaDevices || !("enumerateDevices" in navigator.mediaDevices)) throw new Error("Could not request user devices");
    await this.requestPermission();
    const devices = await navigator.mediaDevices.enumerateDevices();
    const audioDevices = devices.filter((device) => device.kind === "audioinput");
    return audioDevices;
  }
  /**
  * Begins a recording session and requests microphone permissions if not already granted
  * Microphone recording indicator will appear on browser tab but status will be "paused"
  * @param {string} [deviceId] if no device provided, default device will be used
  * @returns {Promise<true>}
  */
  async begin(deviceId) {
    var _a2;
    if (this.processor) throw new Error(`Already connected: please call .end() to start a new session`);
    if (!navigator.mediaDevices || !("getUserMedia" in navigator.mediaDevices)) throw new Error("Could not request user media");
    deviceId = deviceId ?? ((_a2 = this.deviceSelection) == null ? void 0 : _a2.deviceId);
    try {
      const config = {
        audio: true
      };
      if (deviceId) config.audio = {
        deviceId: {
          exact: deviceId
        }
      };
      this.stream = await navigator.mediaDevices.getUserMedia(config);
    } catch (err) {
      throw new Error("Could not start media stream");
    }
    this.listDevices().then((devices) => {
      deviceId = this.stream.getAudioTracks()[0].getSettings().deviceId;
      console.log("find current device", devices, deviceId, this.stream.getAudioTracks()[0].getSettings());
      this.deviceSelection = devices.find((d2) => d2.deviceId === deviceId);
      console.log("current device", this.deviceSelection);
    });
    const context = new AudioContext({
      sampleRate: this.sampleRate
    });
    const source = context.createMediaStreamSource(this.stream);
    try {
      await context.audioWorklet.addModule(this.scriptSrc);
    } catch (e2) {
      console.error(e2);
      throw new Error(`Could not add audioWorklet module: ${this.scriptSrc}`);
    }
    const processor = new AudioWorkletNode(context, "audio_processor");
    processor.port.onmessage = (e2) => {
      const { event, id, data } = e2.data;
      if (event === "receipt") this.eventReceipts[id] = data;
      else if (event === "chunk") {
        if (this._chunkProcessorSize) {
          const buffer = this._chunkProcessorBuffer;
          this._chunkProcessorBuffer = {
            raw: (0, $6d4b7449a1e1544a$export$13afda237b1c9846).mergeBuffers(buffer.raw, data.raw),
            mono: (0, $6d4b7449a1e1544a$export$13afda237b1c9846).mergeBuffers(buffer.mono, data.mono)
          };
          if (this._chunkProcessorBuffer.mono.byteLength >= this._chunkProcessorSize) {
            this._chunkProcessor(this._chunkProcessorBuffer);
            this._chunkProcessorBuffer = {
              raw: new ArrayBuffer(0),
              mono: new ArrayBuffer(0)
            };
          }
        } else this._chunkProcessor(data);
      }
    };
    const node = source.connect(processor);
    const analyser = context.createAnalyser();
    analyser.fftSize = 8192;
    analyser.smoothingTimeConstant = 0.1;
    node.connect(analyser);
    if (this.outputToSpeakers) {
      console.warn("Warning: Output to speakers may affect sound quality,\nespecially due to system audio feedback preventative measures.\nuse only for debugging");
      analyser.connect(context.destination);
    }
    this.source = source;
    this.node = node;
    this.analyser = analyser;
    this.processor = processor;
    console.log("begin completed");
    return true;
  }
  /**
  * Gets the current frequency domain data from the recording track
  * @param {"frequency"|"music"|"voice"} [analysisType]
  * @param {number} [minDecibels] default -100
  * @param {number} [maxDecibels] default -30
  * @returns {import('./analysis/audio_analysis.js').AudioAnalysisOutputType}
  */
  getFrequencies(analysisType = "frequency", minDecibels = -100, maxDecibels = -30) {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    return (0, $f32f064564ee62f6$export$2c3136da0bf130f9).getFrequencies(this.analyser, this.sampleRate, null, analysisType, minDecibels, maxDecibels);
  }
  /**
  * Pauses the recording
  * Keeps microphone stream open but halts storage of audio
  * @returns {Promise<true>}
  */
  async pause() {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    else if (!this.recording) throw new Error("Already paused: please call .record() first");
    if (this._chunkProcessorBuffer.raw.byteLength) this._chunkProcessor(this._chunkProcessorBuffer);
    this.log("Pausing ...");
    await this._event("stop");
    this.recording = false;
    return true;
  }
  /**
  * Start recording stream and storing to memory from the connected audio source
  * @param {(data: { mono: Int16Array; raw: Int16Array }) => any} [chunkProcessor]
  * @param {number} [chunkSize] chunkProcessor will not be triggered until this size threshold met in mono audio
  * @returns {Promise<true>}
  */
  async record(chunkProcessor = () => {
  }, chunkSize = 8192) {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    else if (this.recording) throw new Error("Already recording: please call .pause() first");
    else if (typeof chunkProcessor !== "function") throw new Error(`chunkProcessor must be a function`);
    this._chunkProcessor = chunkProcessor;
    this._chunkProcessorSize = chunkSize;
    this._chunkProcessorBuffer = {
      raw: new ArrayBuffer(0),
      mono: new ArrayBuffer(0)
    };
    this.log("Recording ...");
    await this._event("start");
    this.recording = true;
    return true;
  }
  /**
  * Clears the audio buffer, empties stored recording
  * @returns {Promise<true>}
  */
  async clear() {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    await this._event("clear");
    return true;
  }
  /**
  * Reads the current audio stream data
  * @returns {Promise<{meanValues: Float32Array, channels: Array<Float32Array>}>}
  */
  async read() {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    this.log("Reading ...");
    const result = await this._event("read");
    return result;
  }
  /**
  * Saves the current audio stream to a file
  * @param {boolean} [force] Force saving while still recording
  * @returns {Promise<import('./wav_packer.js').WavPackerAudioType>}
  */
  async save(force = false) {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    if (!force && this.recording) throw new Error("Currently recording: please call .pause() first, or call .save(true) to force");
    this.log("Exporting ...");
    const exportData = await this._event("export");
    const packer = new (0, $6d4b7449a1e1544a$export$13afda237b1c9846)();
    const result = packer.pack(this.sampleRate, exportData.audio);
    return result;
  }
  /**
  * Ends the current recording session and saves the result
  * @returns {Promise<import('./wav_packer.js').WavPackerAudioType>}
  */
  async end() {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    const _processor = this.processor;
    this.log("Stopping ...");
    await this._event("stop");
    this.recording = false;
    const tracks = this.stream.getTracks();
    tracks.forEach((track2) => track2.stop());
    this.log("Exporting ...");
    const exportData = await this._event("export", {}, _processor);
    this.processor.disconnect();
    this.source.disconnect();
    this.node.disconnect();
    this.analyser.disconnect();
    this.stream = null;
    this.processor = null;
    this.source = null;
    this.node = null;
    const packer = new (0, $6d4b7449a1e1544a$export$13afda237b1c9846)();
    const result = packer.pack(this.sampleRate, exportData.audio);
    return result;
  }
  /**
  * Performs a full cleanup of WavRecorder instance
  * Stops actively listening via microphone and removes existing listeners
  * @returns {Promise<true>}
  */
  async quit() {
    this.listenForDeviceChange(null);
    this.deviceSelection = null;
    if (this.processor) await this.end();
    return true;
  }
};
globalThis.WavRecorder = $62bc376044a05513$export$439b217ca659a877;
var $5fc11d7bc0d20724$export$2934cf2d25c67a48 = class {
  /**
  * Create a new MediaStreamRecorder instance
  * @param {{sampleRate?: number, outputToSpeakers?: boolean, debug?: boolean}} [options]
  * @returns {MediaStreamRecorder}
  */
  constructor({ sampleRate = 44100, outputToSpeakers = false, debug = false } = {}) {
    this.scriptSrc = (0, $8e1d1e6ff08f6fb5$export$1f65f50a8cbff43c);
    this.sampleRate = sampleRate;
    this.outputToSpeakers = outputToSpeakers;
    this.debug = !!debug;
    this.stream = null;
    this.processor = null;
    this.source = null;
    this.node = null;
    this.recording = false;
    this._lastEventId = 0;
    this.eventReceipts = {};
    this.eventTimeout = 5e3;
    this._chunkProcessor = () => {
    };
    this._chunkProcessorSize = void 0;
    this._chunkProcessorBuffer = {
      raw: new ArrayBuffer(0),
      mono: new ArrayBuffer(0)
    };
  }
  /**
  * Logs data in debug mode
  * @param {...any} arguments
  * @returns {true}
  */
  log() {
    if (this.debug) this.log(...arguments);
    return true;
  }
  /**
  * Retrieves the current sampleRate for the recorder
  * @returns {number}
  */
  getSampleRate() {
    return this.sampleRate;
  }
  /**
  * Retrieves the current status of the recording
  * @returns {"ended"|"paused"|"recording"}
  */
  getStatus() {
    if (!this.processor) return "ended";
    else if (!this.recording) return "paused";
    else return "recording";
  }
  /**
  * Sends an event to the AudioWorklet
  * @private
  * @param {string} name
  * @param {{[key: string]: any}} data
  * @param {AudioWorkletNode} [_processor]
  * @returns {Promise<{[key: string]: any}>}
  */
  async _event(name, data = {}, _processor = null) {
    _processor = _processor || this.processor;
    if (!_processor) throw new Error("Can not send events without recording first");
    const message = {
      event: name,
      id: this._lastEventId++,
      data
    };
    _processor.port.postMessage(message);
    const t0 = (/* @__PURE__ */ new Date()).valueOf();
    while (!this.eventReceipts[message.id]) {
      if ((/* @__PURE__ */ new Date()).valueOf() - t0 > this.eventTimeout) throw new Error(`Timeout waiting for "${name}" event`);
      await new Promise((res) => setTimeout(() => res(true), 1));
    }
    const payload = this.eventReceipts[message.id];
    delete this.eventReceipts[message.id];
    return payload;
  }
  /**
  * Begins a recording session for the given audioTrack
  * Microphone recording indicator will appear on browser tab but status will be "paused"
  * @param {MediaStreamTrack} [audioTrack] if no device provided, default device will be used
  * @returns {Promise<true>}
  */
  async begin(audioTrack) {
    if (this.processor) throw new Error(`Already connected: please call .end() to start a new session`);
    if (!audioTrack || audioTrack.kind !== "audio") throw new Error("No audio track provided");
    this.stream = new MediaStream([
      audioTrack
    ]);
    const context = new AudioContext({
      sampleRate: this.sampleRate
    });
    const source = context.createMediaStreamSource(this.stream);
    try {
      await context.audioWorklet.addModule(this.scriptSrc);
    } catch (e2) {
      console.error(e2);
      throw new Error(`Could not add audioWorklet module: ${this.scriptSrc}`);
    }
    const processor = new AudioWorkletNode(context, "audio_processor");
    processor.port.onmessage = (e2) => {
      const { event, id, data } = e2.data;
      if (event === "receipt") this.eventReceipts[id] = data;
      else if (event === "chunk") {
        if (this._chunkProcessorSize) {
          const buffer = this._chunkProcessorBuffer;
          this._chunkProcessorBuffer = {
            raw: (0, $6d4b7449a1e1544a$export$13afda237b1c9846).mergeBuffers(buffer.raw, data.raw),
            mono: (0, $6d4b7449a1e1544a$export$13afda237b1c9846).mergeBuffers(buffer.mono, data.mono)
          };
          if (this._chunkProcessorBuffer.mono.byteLength >= this._chunkProcessorSize) {
            this._chunkProcessor(this._chunkProcessorBuffer);
            this._chunkProcessorBuffer = {
              raw: new ArrayBuffer(0),
              mono: new ArrayBuffer(0)
            };
          }
        } else this._chunkProcessor(data);
      }
    };
    const node = source.connect(processor);
    const analyser = context.createAnalyser();
    analyser.fftSize = 8192;
    analyser.smoothingTimeConstant = 0.1;
    node.connect(analyser);
    if (this.outputToSpeakers) {
      console.warn("Warning: Output to speakers may affect sound quality,\nespecially due to system audio feedback preventative measures.\nuse only for debugging");
      analyser.connect(context.destination);
    }
    this.source = source;
    this.node = node;
    this.analyser = analyser;
    this.processor = processor;
    return true;
  }
  /**
  * Gets the current frequency domain data from the recording track
  * @param {"frequency"|"music"|"voice"} [analysisType]
  * @param {number} [minDecibels] default -100
  * @param {number} [maxDecibels] default -30
  * @returns {import('./analysis/audio_analysis.js').AudioAnalysisOutputType}
  */
  getFrequencies(analysisType = "frequency", minDecibels = -100, maxDecibels = -30) {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    return (0, $f32f064564ee62f6$export$2c3136da0bf130f9).getFrequencies(this.analyser, this.sampleRate, null, analysisType, minDecibels, maxDecibels);
  }
  /**
  * Pauses the recording
  * Keeps microphone stream open but halts storage of audio
  * @returns {Promise<true>}
  */
  async pause() {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    else if (!this.recording) throw new Error("Already paused: please call .record() first");
    if (this._chunkProcessorBuffer.raw.byteLength) this._chunkProcessor(this._chunkProcessorBuffer);
    this.log("Pausing ...");
    await this._event("stop");
    this.recording = false;
    return true;
  }
  /**
  * Start recording stream and storing to memory from the connected audio source
  * @param {(data: { mono: Int16Array; raw: Int16Array }) => any} [chunkProcessor]
  * @param {number} [chunkSize] chunkProcessor will not be triggered until this size threshold met in mono audio
  * @returns {Promise<true>}
  */
  async record(chunkProcessor = () => {
  }, chunkSize = 8192) {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    else if (this.recording) throw new Error("Already recording: HELLO please call .pause() first");
    else if (typeof chunkProcessor !== "function") throw new Error(`chunkProcessor must be a function`);
    this._chunkProcessor = chunkProcessor;
    this._chunkProcessorSize = chunkSize;
    this._chunkProcessorBuffer = {
      raw: new ArrayBuffer(0),
      mono: new ArrayBuffer(0)
    };
    this.log("Recording ...");
    await this._event("start");
    this.recording = true;
    return true;
  }
  /**
  * Clears the audio buffer, empties stored recording
  * @returns {Promise<true>}
  */
  async clear() {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    await this._event("clear");
    return true;
  }
  /**
  * Reads the current audio stream data
  * @returns {Promise<{meanValues: Float32Array, channels: Array<Float32Array>}>}
  */
  async read() {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    this.log("Reading ...");
    const result = await this._event("read");
    return result;
  }
  /**
  * Saves the current audio stream to a file
  * @param {boolean} [force] Force saving while still recording
  * @returns {Promise<import('./wav_packer.js').WavPackerAudioType>}
  */
  async save(force = false) {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    if (!force && this.recording) throw new Error("Currently recording: please call .pause() first, or call .save(true) to force");
    this.log("Exporting ...");
    const exportData = await this._event("export");
    const packer = new (0, $6d4b7449a1e1544a$export$13afda237b1c9846)();
    const result = packer.pack(this.sampleRate, exportData.audio);
    return result;
  }
  /**
  * Ends the current recording session and saves the result
  * @returns {Promise<import('./wav_packer.js').WavPackerAudioType>}
  */
  async end() {
    if (!this.processor) throw new Error("Session ended: please call .begin() first");
    const _processor = this.processor;
    this.log("Stopping ...");
    await this._event("stop");
    this.recording = false;
    this.log("Exporting ...");
    const exportData = await this._event("export", {}, _processor);
    this.processor.disconnect();
    this.source.disconnect();
    this.node.disconnect();
    this.analyser.disconnect();
    this.stream = null;
    this.processor = null;
    this.source = null;
    this.node = null;
    const packer = new (0, $6d4b7449a1e1544a$export$13afda237b1c9846)();
    const result = packer.pack(this.sampleRate, exportData.audio);
    return result;
  }
  /**
  * Performs a full cleanup of WavRecorder instance
  * Stops actively listening via microphone and removes existing listeners
  * @returns {Promise<true>}
  */
  async quit() {
    this.listenForDeviceChange(null);
    if (this.processor) await this.end();
    return true;
  }
};
globalThis.WavRecorder = WavRecorder;
var $58d19ff082af5e5c$exports = {};
$58d19ff082af5e5c$exports = JSON.parse('{"name":"@pipecat-ai/daily-transport","version":"0.3.10","license":"BSD-2-Clause","main":"dist/index.js","module":"dist/index.module.js","types":"dist/index.d.ts","source":"src/index.ts","repository":{"type":"git","url":"git+https://github.com/pipecat-ai/pipecat-client-web-transports.git"},"files":["dist","package.json","README.md"],"scripts":{"build":"parcel build --no-cache","dev":"parcel watch","lint":"eslint . --ext ts --report-unused-disable-directives --max-warnings 0"},"devDependencies":{"@pipecat-ai/client-js":"^0.3.5","eslint":"9.11.1","eslint-config-prettier":"^9.1.0","eslint-plugin-simple-import-sort":"^12.1.1"},"peerDependencies":{"@pipecat-ai/client-js":"~0.3.5"},"dependencies":{"@daily-co/daily-js":"^0.77.0"},"description":"Pipecat Daily Transport Package","author":"Daily.co","bugs":{"url":"https://github.com/pipecat-ai/pipecat-client-web-transports/issues"},"homepage":"https://github.com/pipecat-ai/pipecat-client-web-transports/blob/main/transports/daily-webrtc/README.md"}');
var $683f111f61e07358$export$ef180de88fd317cc;
(function(DailyRTVIMessageType) {
  DailyRTVIMessageType["AUDIO_BUFFERING_STARTED"] = "audio-buffering-started";
  DailyRTVIMessageType["AUDIO_BUFFERING_STOPPED"] = "audio-buffering-stopped";
})($683f111f61e07358$export$ef180de88fd317cc || ($683f111f61e07358$export$ef180de88fd317cc = {}));
var $683f111f61e07358$var$DailyCallWrapper = class {
  constructor(daily) {
    this._daily = daily;
    this._proxy = new Proxy(this._daily, {
      get: (target, prop, receiver) => {
        if (typeof target[prop] === "function") {
          let errMsg;
          switch (String(prop)) {
            case "preAuth":
              errMsg = `Calls to preAuth() are disabled. Please use Transport.preAuth()`;
              break;
            case "startCamera":
              errMsg = `Calls to startCamera() are disabled. Please use RTVIClient.initDevices()`;
              break;
            case "join":
              errMsg = `Calls to join() are disabled. Please use RTVIClient.connect()`;
              break;
            case "leave":
              errMsg = `Calls to leave() are disabled. Please use RTVIClient.disconnect()`;
              break;
            case "destroy":
              errMsg = `Calls to destroy() are disabled.`;
              break;
          }
          if (errMsg) return () => {
            throw new Error(errMsg);
          };
          return (...args) => {
            return target[prop](...args);
          };
        }
        return Reflect.get(target, prop, receiver);
      }
    });
  }
  get proxy() {
    return this._proxy;
  }
};
var $683f111f61e07358$export$b1ca982aa1e488c1 = class _$683f111f61e07358$export$b1ca982aa1e488c1 extends (0, $4086f06442fcb7d7$export$86495b081fef8e52) {
  constructor({ dailyFactoryOptions = {}, bufferLocalAudioUntilBotReady = false } = {}) {
    super();
    this._botId = "";
    this._selectedCam = {};
    this._selectedMic = {};
    this._selectedSpeaker = {};
    this._currentAudioTrack = null;
    this._audioQueue = [];
    this._callbacks = {};
    this._dailyFactoryOptions = dailyFactoryOptions;
    this._bufferLocalAudioUntilBotReady = bufferLocalAudioUntilBotReady;
    this._daily = (0, ic).createCallObject({
      ...this._dailyFactoryOptions,
      allowMultipleCallInstances: true
    });
    this._dailyWrapper = new $683f111f61e07358$var$DailyCallWrapper(this._daily);
  }
  setupRecorder() {
    this._mediaStreamRecorder = new (0, $5fc11d7bc0d20724$export$2934cf2d25c67a48)({
      sampleRate: _$683f111f61e07358$export$b1ca982aa1e488c1.RECORDER_SAMPLE_RATE
    });
  }
  handleUserAudioStream(data) {
    this._audioQueue.push(data);
  }
  flushAudioQueue() {
    const batchSize = 10;
    if (this._audioQueue.length === 0) return;
    (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug(`Will flush audio queue: ${this._audioQueue.length}`);
    while (this._audioQueue.length > 0) {
      const batch = [];
      while (batch.length < batchSize && this._audioQueue.length > 0) {
        const queuedData = this._audioQueue.shift();
        if (queuedData) batch.push(queuedData);
      }
      if (batch.length > 0) this._sendAudioBatch(batch);
    }
  }
  _sendAudioBatch(dataBatch) {
    const encodedBatch = dataBatch.map((data) => {
      const pcmByteArray = new Uint8Array(data);
      return btoa(String.fromCharCode(...pcmByteArray));
    });
    const rtviMessage = {
      id: "raw-audio-batch",
      label: "rtvi-ai",
      type: "raw-audio-batch",
      data: {
        base64AudioBatch: encodedBatch,
        sampleRate: _$683f111f61e07358$export$b1ca982aa1e488c1.RECORDER_SAMPLE_RATE,
        numChannels: 1
      }
    };
    this.sendMessage(rtviMessage);
  }
  initialize(options, messageHandler) {
    if (this._bufferLocalAudioUntilBotReady) this.setupRecorder();
    this._callbacks = options.callbacks ?? {};
    this._onMessage = messageHandler;
    if (this._dailyFactoryOptions.startVideoOff == null || options.enableCam != null)
      this._dailyFactoryOptions.startVideoOff = !(options.enableCam ?? false);
    if (this._dailyFactoryOptions.startAudioOff == null || options.enableMic != null)
      this._dailyFactoryOptions.startAudioOff = !(options.enableMic ?? true);
    this.attachEventListeners();
    this.state = "disconnected";
    (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[RTVI Transport] Initialized", (0, $parcel$interopDefault($58d19ff082af5e5c$exports)).version);
  }
  get dailyCallClient() {
    return this._dailyWrapper.proxy;
  }
  get state() {
    return this._state;
  }
  set state(state) {
    var _a2, _b;
    if (this._state === state) return;
    this._state = state;
    (_b = (_a2 = this._callbacks).onTransportStateChanged) == null ? void 0 : _b.call(_a2, state);
  }
  async getAllCams() {
    const { devices } = await this._daily.enumerateDevices();
    return devices.filter((d2) => d2.kind === "videoinput");
  }
  updateCam(camId) {
    this._daily.setInputDevicesAsync({
      videoDeviceId: camId
    }).then((infos) => {
      this._selectedCam = infos.camera;
    });
  }
  get selectedCam() {
    return this._selectedCam;
  }
  async getAllMics() {
    const { devices } = await this._daily.enumerateDevices();
    return devices.filter((d2) => d2.kind === "audioinput");
  }
  updateMic(micId) {
    this._daily.setInputDevicesAsync({
      audioDeviceId: micId
    }).then((infos) => {
      this._selectedMic = infos.mic;
    });
  }
  get selectedMic() {
    return this._selectedMic;
  }
  async getAllSpeakers() {
    const { devices } = await this._daily.enumerateDevices();
    return devices.filter((d2) => d2.kind === "audiooutput");
  }
  updateSpeaker(speakerId) {
    this._daily.setOutputDeviceAsync({
      outputDeviceId: speakerId
    }).then((infos) => {
      this._selectedSpeaker = infos.speaker;
    });
  }
  get selectedSpeaker() {
    return this._selectedSpeaker;
  }
  enableMic(enable) {
    this._daily.setLocalAudio(enable);
  }
  get isMicEnabled() {
    return this._daily.localAudio();
  }
  enableCam(enable) {
    this._daily.setLocalVideo(enable);
  }
  get isCamEnabled() {
    return this._daily.localVideo();
  }
  enableScreenShare(enable) {
    if (enable) this._daily.startScreenShare();
    else this._daily.stopScreenShare();
  }
  get isSharingScreen() {
    return this._daily.localScreenAudio() || this._daily.localScreenVideo();
  }
  tracks() {
    var _a2, _b, _c2, _d, _e2, _f, _g, _h, _i2, _j, _k, _l, _m, _n2, _o2, _p;
    const participants = this._daily.participants() ?? {};
    const bot = participants == null ? void 0 : participants[this._botId];
    const tracks = {
      local: {
        audio: (_c2 = (_b = (_a2 = participants == null ? void 0 : participants.local) == null ? void 0 : _a2.tracks) == null ? void 0 : _b.audio) == null ? void 0 : _c2.persistentTrack,
        screenAudio: (_f = (_e2 = (_d = participants == null ? void 0 : participants.local) == null ? void 0 : _d.tracks) == null ? void 0 : _e2.screenAudio) == null ? void 0 : _f.persistentTrack,
        screenVideo: (_i2 = (_h = (_g = participants == null ? void 0 : participants.local) == null ? void 0 : _g.tracks) == null ? void 0 : _h.screenVideo) == null ? void 0 : _i2.persistentTrack,
        video: (_l = (_k = (_j = participants == null ? void 0 : participants.local) == null ? void 0 : _j.tracks) == null ? void 0 : _k.video) == null ? void 0 : _l.persistentTrack
      }
    };
    if (bot) tracks.bot = {
      audio: (_n2 = (_m = bot == null ? void 0 : bot.tracks) == null ? void 0 : _m.audio) == null ? void 0 : _n2.persistentTrack,
      video: (_p = (_o2 = bot == null ? void 0 : bot.tracks) == null ? void 0 : _o2.video) == null ? void 0 : _p.persistentTrack
    };
    return tracks;
  }
  async startRecording() {
    try {
      (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).info("[RTVI Transport] Initializing recording");
      await this._mediaStreamRecorder.record((data) => {
        this.handleUserAudioStream(data.mono);
      }, _$683f111f61e07358$export$b1ca982aa1e488c1.RECORDER_CHUNK_SIZE);
      this._onMessage({
        type: $683f111f61e07358$export$ef180de88fd317cc.AUDIO_BUFFERING_STARTED,
        data: {}
      });
      (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).info("[RTVI Transport] Recording Initialized");
    } catch (e2) {
      const err = e2;
      if (!err.message.includes("Already recording")) (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).error("Error starting recording", e2);
    }
  }
  async preAuth(dailyFactoryOptions) {
    this._dailyFactoryOptions = dailyFactoryOptions;
    await this._daily.preAuth(dailyFactoryOptions);
  }
  async initDevices() {
    var _a2, _b, _c2, _d, _e2, _f, _g, _h, _i2, _j, _k, _l;
    if (!this._daily) throw new (0, $8ead7b33b8402751$export$59b4786f333aac02)("Transport instance not initialized");
    this.state = "initializing";
    const infos = await this._daily.startCamera(this._dailyFactoryOptions);
    const { devices } = await this._daily.enumerateDevices();
    const cams = devices.filter((d2) => d2.kind === "videoinput");
    const mics = devices.filter((d2) => d2.kind === "audioinput");
    const speakers = devices.filter((d2) => d2.kind === "audiooutput");
    this._selectedCam = infos.camera;
    this._selectedMic = infos.mic;
    this._selectedSpeaker = infos.speaker;
    (_b = (_a2 = this._callbacks).onAvailableCamsUpdated) == null ? void 0 : _b.call(_a2, cams);
    (_d = (_c2 = this._callbacks).onAvailableMicsUpdated) == null ? void 0 : _d.call(_c2, mics);
    (_f = (_e2 = this._callbacks).onAvailableSpeakersUpdated) == null ? void 0 : _f.call(_e2, speakers);
    (_h = (_g = this._callbacks).onCamUpdated) == null ? void 0 : _h.call(_g, infos.camera);
    (_j = (_i2 = this._callbacks).onMicUpdated) == null ? void 0 : _j.call(_i2, infos.mic);
    (_l = (_k = this._callbacks).onSpeakerUpdated) == null ? void 0 : _l.call(_k, infos.speaker);
    if (!this._daily.isLocalAudioLevelObserverRunning()) await this._daily.startLocalAudioLevelObserver(100);
    if (!this._daily.isRemoteParticipantsAudioLevelObserverRunning()) await this._daily.startRemoteParticipantsAudioLevelObserver(100);
    this.state = "initialized";
  }
  async connect(authBundle, abortController) {
    var _a2, _b, _c2;
    if (!this._daily) throw new (0, $8ead7b33b8402751$export$59b4786f333aac02)("Transport instance not initialized");
    if (abortController.signal.aborted) return;
    this.state = "connecting";
    const opts = this._dailyFactoryOptions;
    opts.url = authBundle.room_url ?? opts.url;
    if (authBundle.token != null) opts.token = authBundle.token;
    try {
      await this._daily.join(opts);
      const room = await this._daily.room();
      if (room && "id" in room) this._expiry = (_a2 = room.config) == null ? void 0 : _a2.exp;
    } catch (e2) {
      (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).error("Failed to join room", e2);
      this.state = "error";
      throw new (0, $8ead7b33b8402751$export$e0624a511a2c4e9)();
    }
    if (abortController.signal.aborted) return;
    this.state = "connected";
    (_c2 = (_b = this._callbacks).onConnected) == null ? void 0 : _c2.call(_b);
  }
  async sendReadyMessage() {
    return new Promise((resolve) => {
      const isIOS = () => {
        const userAgent = navigator.userAgent;
        return /iPad|iPhone|iPod/.test(userAgent) || /Macintosh/.test(userAgent) && "ontouchend" in document;
      };
      const sendReadyMessage = () => {
        this.state = "ready";
        this.flushAudioQueue();
        this.sendMessage((0, $b48f893ed1354c1e$export$69aa9ab0334b212).clientReady());
        this.stopRecording();
        resolve();
      };
      const readyHandler = (ev) => {
        var _a2;
        if (!((_a2 = ev.participant) == null ? void 0 : _a2.local)) {
          this._daily.off("track-started", readyHandler);
          if (isIOS()) {
            (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[RTVI Transport] iOS device detected, adding 0.5 second delay before sending ready message");
            setTimeout(sendReadyMessage, 500);
          } else
            sendReadyMessage();
        }
      };
      this._daily.on("track-started", readyHandler);
    });
  }
  stopRecording() {
    if (this._mediaStreamRecorder && this._mediaStreamRecorder.getStatus() !== "ended") {
      this._mediaStreamRecorder.end();
      this._onMessage({
        type: $683f111f61e07358$export$ef180de88fd317cc.AUDIO_BUFFERING_STOPPED,
        data: {}
      });
    }
  }
  attachEventListeners() {
    this._daily.on("available-devices-updated", this.handleAvailableDevicesUpdated.bind(this));
    this._daily.on("selected-devices-updated", this.handleSelectedDevicesUpdated.bind(this));
    this._daily.on("track-started", this.handleTrackStarted.bind(this));
    this._daily.on("track-stopped", this.handleTrackStopped.bind(this));
    this._daily.on("participant-joined", this.handleParticipantJoined.bind(this));
    this._daily.on("participant-left", this.handleParticipantLeft.bind(this));
    this._daily.on("local-audio-level", this.handleLocalAudioLevel.bind(this));
    this._daily.on("remote-participants-audio-level", this.handleRemoteAudioLevel.bind(this));
    this._daily.on("app-message", this.handleAppMessage.bind(this));
    this._daily.on("left-meeting", this.handleLeftMeeting.bind(this));
    this._daily.on("error", this.handleFatalError.bind(this));
    this._daily.on("nonfatal-error", this.handleNonFatalError.bind(this));
  }
  async disconnect() {
    this.state = "disconnecting";
    this._daily.stopLocalAudioLevelObserver();
    this._daily.stopRemoteParticipantsAudioLevelObserver();
    this._audioQueue = [];
    this._currentAudioTrack = null;
    this.stopRecording();
    await this._daily.leave();
  }
  sendMessage(message) {
    this._daily.sendAppMessage(message, "*");
  }
  handleAppMessage(ev) {
    if (ev.data.label === "rtvi-ai") this._onMessage({
      id: ev.data.id,
      type: ev.data.type,
      data: ev.data.data
    });
  }
  handleAvailableDevicesUpdated(ev) {
    var _a2, _b, _c2, _d, _e2, _f;
    (_b = (_a2 = this._callbacks).onAvailableCamsUpdated) == null ? void 0 : _b.call(_a2, ev.availableDevices.filter((d2) => d2.kind === "videoinput"));
    (_d = (_c2 = this._callbacks).onAvailableMicsUpdated) == null ? void 0 : _d.call(_c2, ev.availableDevices.filter((d2) => d2.kind === "audioinput"));
    (_f = (_e2 = this._callbacks).onAvailableSpeakersUpdated) == null ? void 0 : _f.call(_e2, ev.availableDevices.filter((d2) => d2.kind === "audiooutput"));
  }
  handleSelectedDevicesUpdated(ev) {
    var _a2, _b, _c2, _d, _e2, _f, _g, _h, _i2;
    if (((_a2 = this._selectedCam) == null ? void 0 : _a2.deviceId) !== ev.devices.camera) {
      this._selectedCam = ev.devices.camera;
      (_c2 = (_b = this._callbacks).onCamUpdated) == null ? void 0 : _c2.call(_b, ev.devices.camera);
    }
    if (((_d = this._selectedMic) == null ? void 0 : _d.deviceId) !== ev.devices.mic) {
      this._selectedMic = ev.devices.mic;
      (_f = (_e2 = this._callbacks).onMicUpdated) == null ? void 0 : _f.call(_e2, ev.devices.mic);
    }
    if (((_g = this._selectedSpeaker) == null ? void 0 : _g.deviceId) !== ev.devices.speaker) {
      this._selectedSpeaker = ev.devices.speaker;
      (_i2 = (_h = this._callbacks).onSpeakerUpdated) == null ? void 0 : _i2.call(_h, ev.devices.speaker);
    }
  }
  async handleLocalAudioTrack(track2) {
    if (this.state == "ready" || !this._bufferLocalAudioUntilBotReady) return;
    const status = this._mediaStreamRecorder.getStatus();
    switch (status) {
      case "ended":
        await this._mediaStreamRecorder.begin(track2);
        await this.startRecording();
        break;
      case "paused":
        await this.startRecording();
        break;
      case "recording":
      default:
        if (this._currentAudioTrack !== track2) {
          await this._mediaStreamRecorder.end();
          await this._mediaStreamRecorder.begin(track2);
          await this.startRecording();
        } else (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).warn("track-started event received for current track and already recording");
        break;
    }
    this._currentAudioTrack = track2;
  }
  handleTrackStarted(ev) {
    var _a2, _b, _c2, _d, _e2;
    if (ev.type === "screenAudio" || ev.type === "screenVideo") (_b = (_a2 = this._callbacks).onScreenTrackStarted) == null ? void 0 : _b.call(_a2, ev.track, ev.participant ? $683f111f61e07358$var$dailyParticipantToParticipant(ev.participant) : void 0);
    else {
      if (((_c2 = ev.participant) == null ? void 0 : _c2.local) && ev.track.kind === "audio") this.handleLocalAudioTrack(ev.track);
      (_e2 = (_d = this._callbacks).onTrackStarted) == null ? void 0 : _e2.call(_d, ev.track, ev.participant ? $683f111f61e07358$var$dailyParticipantToParticipant(ev.participant) : void 0);
    }
  }
  handleTrackStopped(ev) {
    var _a2, _b, _c2, _d;
    if (ev.type === "screenAudio" || ev.type === "screenVideo") (_b = (_a2 = this._callbacks).onScreenTrackStopped) == null ? void 0 : _b.call(_a2, ev.track, ev.participant ? $683f111f61e07358$var$dailyParticipantToParticipant(ev.participant) : void 0);
    else (_d = (_c2 = this._callbacks).onTrackStopped) == null ? void 0 : _d.call(_c2, ev.track, ev.participant ? $683f111f61e07358$var$dailyParticipantToParticipant(ev.participant) : void 0);
  }
  handleParticipantJoined(ev) {
    var _a2, _b, _c2, _d;
    const p2 = $683f111f61e07358$var$dailyParticipantToParticipant(ev.participant);
    (_b = (_a2 = this._callbacks).onParticipantJoined) == null ? void 0 : _b.call(_a2, p2);
    if (p2.local) return;
    this._botId = ev.participant.session_id;
    (_d = (_c2 = this._callbacks).onBotConnected) == null ? void 0 : _d.call(_c2, p2);
  }
  handleParticipantLeft(ev) {
    var _a2, _b, _c2, _d;
    const p2 = $683f111f61e07358$var$dailyParticipantToParticipant(ev.participant);
    (_b = (_a2 = this._callbacks).onParticipantLeft) == null ? void 0 : _b.call(_a2, p2);
    if (p2.local) return;
    this._botId = "";
    (_d = (_c2 = this._callbacks).onBotDisconnected) == null ? void 0 : _d.call(_c2, p2);
  }
  handleLocalAudioLevel(ev) {
    var _a2, _b;
    (_b = (_a2 = this._callbacks).onLocalAudioLevel) == null ? void 0 : _b.call(_a2, ev.audioLevel);
  }
  handleRemoteAudioLevel(ev) {
    var _a2, _b;
    const participants = this._daily.participants();
    const ids = Object.keys(ev.participantsAudioLevel);
    for (let i2 = 0; i2 < ids.length; i2++) {
      const id = ids[i2];
      const level = ev.participantsAudioLevel[id];
      (_b = (_a2 = this._callbacks).onRemoteAudioLevel) == null ? void 0 : _b.call(_a2, level, $683f111f61e07358$var$dailyParticipantToParticipant(participants[id]));
    }
  }
  handleLeftMeeting() {
    var _a2, _b;
    this.state = "disconnected";
    this._botId = "";
    (_b = (_a2 = this._callbacks).onDisconnected) == null ? void 0 : _b.call(_a2);
  }
  handleFatalError(ev) {
    var _a2, _b;
    (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).error("Daily fatal error", ev.errorMsg);
    this.state = "error";
    this._botId = "";
    (_b = (_a2 = this._callbacks).onError) == null ? void 0 : _b.call(_a2, (0, $b48f893ed1354c1e$export$69aa9ab0334b212).error(ev.errorMsg, true));
  }
  handleNonFatalError(ev) {
    var _a2, _b;
    switch (ev.type) {
      case "screen-share-error":
        (_b = (_a2 = this._callbacks).onScreenShareError) == null ? void 0 : _b.call(_a2, ev.errorMsg);
        break;
    }
  }
};
$683f111f61e07358$export$b1ca982aa1e488c1.RECORDER_SAMPLE_RATE = 16e3;
$683f111f61e07358$export$b1ca982aa1e488c1.RECORDER_CHUNK_SIZE = 512;
var $683f111f61e07358$var$dailyParticipantToParticipant = (p2) => ({
  id: p2.user_id,
  local: p2.local,
  name: p2.user_name
});
export {
  $683f111f61e07358$export$ef180de88fd317cc as DailyRTVIMessageType,
  $683f111f61e07358$export$b1ca982aa1e488c1 as DailyTransport
};
/*! Bundled license information:

@daily-co/daily-js/dist/daily-esm.js:
  (*!
   * Bowser - a browser detector
   * https://github.com/lancedikson/bowser
   * MIT License | (c) Dustin Diaz 2012-2015
   * MIT License | (c) Denis Demchenko 2015-2019
   *)
*/
//# sourceMappingURL=@pipecat-ai_daily-transport.js.map
