import {
  __commonJS,
  __toESM
} from "./chunk-5WRI5ZAA.js";

// node_modules/kind-of/index.js
var require_kind_of = __commonJS({
  "node_modules/kind-of/index.js"(exports, module) {
    var toString = Object.prototype.toString;
    module.exports = function kindOf(val) {
      if (val === void 0) return "undefined";
      if (val === null) return "null";
      var type = typeof val;
      if (type === "boolean") return "boolean";
      if (type === "string") return "string";
      if (type === "number") return "number";
      if (type === "symbol") return "symbol";
      if (type === "function") {
        return isGeneratorFn(val) ? "generatorfunction" : "function";
      }
      if (isArray(val)) return "array";
      if (isBuffer(val)) return "buffer";
      if (isArguments(val)) return "arguments";
      if (isDate(val)) return "date";
      if (isError(val)) return "error";
      if (isRegexp(val)) return "regexp";
      switch (ctorName(val)) {
        case "Symbol":
          return "symbol";
        case "Promise":
          return "promise";
        case "WeakMap":
          return "weakmap";
        case "WeakSet":
          return "weakset";
        case "Map":
          return "map";
        case "Set":
          return "set";
        case "Int8Array":
          return "int8array";
        case "Uint8Array":
          return "uint8array";
        case "Uint8ClampedArray":
          return "uint8clampedarray";
        case "Int16Array":
          return "int16array";
        case "Uint16Array":
          return "uint16array";
        case "Int32Array":
          return "int32array";
        case "Uint32Array":
          return "uint32array";
        case "Float32Array":
          return "float32array";
        case "Float64Array":
          return "float64array";
      }
      if (isGeneratorObj(val)) {
        return "generator";
      }
      type = toString.call(val);
      switch (type) {
        case "[object Object]":
          return "object";
        case "[object Map Iterator]":
          return "mapiterator";
        case "[object Set Iterator]":
          return "setiterator";
        case "[object String Iterator]":
          return "stringiterator";
        case "[object Array Iterator]":
          return "arrayiterator";
      }
      return type.slice(8, -1).toLowerCase().replace(/\s/g, "");
    };
    function ctorName(val) {
      return typeof val.constructor === "function" ? val.constructor.name : null;
    }
    function isArray(val) {
      if (Array.isArray) return Array.isArray(val);
      return val instanceof Array;
    }
    function isError(val) {
      return val instanceof Error || typeof val.message === "string" && val.constructor && typeof val.constructor.stackTraceLimit === "number";
    }
    function isDate(val) {
      if (val instanceof Date) return true;
      return typeof val.toDateString === "function" && typeof val.getDate === "function" && typeof val.setDate === "function";
    }
    function isRegexp(val) {
      if (val instanceof RegExp) return true;
      return typeof val.flags === "string" && typeof val.ignoreCase === "boolean" && typeof val.multiline === "boolean" && typeof val.global === "boolean";
    }
    function isGeneratorFn(name, val) {
      return ctorName(name) === "GeneratorFunction";
    }
    function isGeneratorObj(val) {
      return typeof val.throw === "function" && typeof val.return === "function" && typeof val.next === "function";
    }
    function isArguments(val) {
      try {
        if (typeof val.length === "number" && typeof val.callee === "function") {
          return true;
        }
      } catch (err) {
        if (err.message.indexOf("callee") !== -1) {
          return true;
        }
      }
      return false;
    }
    function isBuffer(val) {
      if (val.constructor && typeof val.constructor.isBuffer === "function") {
        return val.constructor.isBuffer(val);
      }
      return false;
    }
  }
});

// node_modules/shallow-clone/index.js
var require_shallow_clone = __commonJS({
  "node_modules/shallow-clone/index.js"(exports, module) {
    "use strict";
    var valueOf = Symbol.prototype.valueOf;
    var typeOf = require_kind_of();
    function clone(val, deep) {
      switch (typeOf(val)) {
        case "array":
          return val.slice();
        case "object":
          return Object.assign({}, val);
        case "date":
          return new val.constructor(Number(val));
        case "map":
          return new Map(val);
        case "set":
          return new Set(val);
        case "buffer":
          return cloneBuffer(val);
        case "symbol":
          return cloneSymbol(val);
        case "arraybuffer":
          return cloneArrayBuffer(val);
        case "float32array":
        case "float64array":
        case "int16array":
        case "int32array":
        case "int8array":
        case "uint16array":
        case "uint32array":
        case "uint8clampedarray":
        case "uint8array":
          return cloneTypedArray(val);
        case "regexp":
          return cloneRegExp(val);
        case "error":
          return Object.create(val);
        default: {
          return val;
        }
      }
    }
    function cloneRegExp(val) {
      const flags = val.flags !== void 0 ? val.flags : /\w+$/.exec(val) || void 0;
      const re = new val.constructor(val.source, flags);
      re.lastIndex = val.lastIndex;
      return re;
    }
    function cloneArrayBuffer(val) {
      const res = new val.constructor(val.byteLength);
      new Uint8Array(res).set(new Uint8Array(val));
      return res;
    }
    function cloneTypedArray(val, deep) {
      return new val.constructor(val.buffer, val.byteOffset, val.length);
    }
    function cloneBuffer(val) {
      const len = val.length;
      const buf = Buffer.allocUnsafe ? Buffer.allocUnsafe(len) : Buffer.from(len);
      val.copy(buf);
      return buf;
    }
    function cloneSymbol(val) {
      return valueOf ? Object(valueOf.call(val)) : {};
    }
    module.exports = clone;
  }
});

// node_modules/isobject/index.js
var require_isobject = __commonJS({
  "node_modules/isobject/index.js"(exports, module) {
    "use strict";
    module.exports = function isObject(val) {
      return val != null && typeof val === "object" && Array.isArray(val) === false;
    };
  }
});

// node_modules/is-plain-object/index.js
var require_is_plain_object = __commonJS({
  "node_modules/is-plain-object/index.js"(exports, module) {
    "use strict";
    var isObject = require_isobject();
    function isObjectObject(o) {
      return isObject(o) === true && Object.prototype.toString.call(o) === "[object Object]";
    }
    module.exports = function isPlainObject(o) {
      var ctor, prot;
      if (isObjectObject(o) === false) return false;
      ctor = o.constructor;
      if (typeof ctor !== "function") return false;
      prot = ctor.prototype;
      if (isObjectObject(prot) === false) return false;
      if (prot.hasOwnProperty("isPrototypeOf") === false) {
        return false;
      }
      return true;
    };
  }
});

// node_modules/clone-deep/index.js
var require_clone_deep = __commonJS({
  "node_modules/clone-deep/index.js"(exports, module) {
    "use strict";
    var clone = require_shallow_clone();
    var typeOf = require_kind_of();
    var isPlainObject = require_is_plain_object();
    function cloneDeep(val, instanceClone) {
      switch (typeOf(val)) {
        case "object":
          return cloneObjectDeep(val, instanceClone);
        case "array":
          return cloneArrayDeep(val, instanceClone);
        default: {
          return clone(val);
        }
      }
    }
    function cloneObjectDeep(val, instanceClone) {
      if (typeof instanceClone === "function") {
        return instanceClone(val);
      }
      if (instanceClone || isPlainObject(val)) {
        const res = new val.constructor();
        for (let key in val) {
          res[key] = cloneDeep(val[key], instanceClone);
        }
        return res;
      }
      return val;
    }
    function cloneArrayDeep(val, instanceClone) {
      const res = new val.constructor(val.length);
      for (let i = 0; i < val.length; i++) {
        res[i] = cloneDeep(val[i], instanceClone);
      }
      return res;
    }
    module.exports = cloneDeep;
  }
});

// node_modules/events/events.js
var require_events = __commonJS({
  "node_modules/events/events.js"(exports, module) {
    "use strict";
    var R = typeof Reflect === "object" ? Reflect : null;
    var ReflectApply = R && typeof R.apply === "function" ? R.apply : function ReflectApply2(target, receiver, args) {
      return Function.prototype.apply.call(target, receiver, args);
    };
    var ReflectOwnKeys;
    if (R && typeof R.ownKeys === "function") {
      ReflectOwnKeys = R.ownKeys;
    } else if (Object.getOwnPropertySymbols) {
      ReflectOwnKeys = function ReflectOwnKeys2(target) {
        return Object.getOwnPropertyNames(target).concat(Object.getOwnPropertySymbols(target));
      };
    } else {
      ReflectOwnKeys = function ReflectOwnKeys2(target) {
        return Object.getOwnPropertyNames(target);
      };
    }
    function ProcessEmitWarning(warning) {
      if (console && console.warn) console.warn(warning);
    }
    var NumberIsNaN = Number.isNaN || function NumberIsNaN2(value) {
      return value !== value;
    };
    function EventEmitter() {
      EventEmitter.init.call(this);
    }
    module.exports = EventEmitter;
    module.exports.once = once;
    EventEmitter.EventEmitter = EventEmitter;
    EventEmitter.prototype._events = void 0;
    EventEmitter.prototype._eventsCount = 0;
    EventEmitter.prototype._maxListeners = void 0;
    var defaultMaxListeners = 10;
    function checkListener(listener) {
      if (typeof listener !== "function") {
        throw new TypeError('The "listener" argument must be of type Function. Received type ' + typeof listener);
      }
    }
    Object.defineProperty(EventEmitter, "defaultMaxListeners", {
      enumerable: true,
      get: function() {
        return defaultMaxListeners;
      },
      set: function(arg) {
        if (typeof arg !== "number" || arg < 0 || NumberIsNaN(arg)) {
          throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received ' + arg + ".");
        }
        defaultMaxListeners = arg;
      }
    });
    EventEmitter.init = function() {
      if (this._events === void 0 || this._events === Object.getPrototypeOf(this)._events) {
        this._events = /* @__PURE__ */ Object.create(null);
        this._eventsCount = 0;
      }
      this._maxListeners = this._maxListeners || void 0;
    };
    EventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {
      if (typeof n !== "number" || n < 0 || NumberIsNaN(n)) {
        throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received ' + n + ".");
      }
      this._maxListeners = n;
      return this;
    };
    function _getMaxListeners(that) {
      if (that._maxListeners === void 0)
        return EventEmitter.defaultMaxListeners;
      return that._maxListeners;
    }
    EventEmitter.prototype.getMaxListeners = function getMaxListeners() {
      return _getMaxListeners(this);
    };
    EventEmitter.prototype.emit = function emit(type) {
      var args = [];
      for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);
      var doError = type === "error";
      var events = this._events;
      if (events !== void 0)
        doError = doError && events.error === void 0;
      else if (!doError)
        return false;
      if (doError) {
        var er;
        if (args.length > 0)
          er = args[0];
        if (er instanceof Error) {
          throw er;
        }
        var err = new Error("Unhandled error." + (er ? " (" + er.message + ")" : ""));
        err.context = er;
        throw err;
      }
      var handler = events[type];
      if (handler === void 0)
        return false;
      if (typeof handler === "function") {
        ReflectApply(handler, this, args);
      } else {
        var len = handler.length;
        var listeners = arrayClone(handler, len);
        for (var i = 0; i < len; ++i)
          ReflectApply(listeners[i], this, args);
      }
      return true;
    };
    function _addListener(target, type, listener, prepend) {
      var m;
      var events;
      var existing;
      checkListener(listener);
      events = target._events;
      if (events === void 0) {
        events = target._events = /* @__PURE__ */ Object.create(null);
        target._eventsCount = 0;
      } else {
        if (events.newListener !== void 0) {
          target.emit(
            "newListener",
            type,
            listener.listener ? listener.listener : listener
          );
          events = target._events;
        }
        existing = events[type];
      }
      if (existing === void 0) {
        existing = events[type] = listener;
        ++target._eventsCount;
      } else {
        if (typeof existing === "function") {
          existing = events[type] = prepend ? [listener, existing] : [existing, listener];
        } else if (prepend) {
          existing.unshift(listener);
        } else {
          existing.push(listener);
        }
        m = _getMaxListeners(target);
        if (m > 0 && existing.length > m && !existing.warned) {
          existing.warned = true;
          var w = new Error("Possible EventEmitter memory leak detected. " + existing.length + " " + String(type) + " listeners added. Use emitter.setMaxListeners() to increase limit");
          w.name = "MaxListenersExceededWarning";
          w.emitter = target;
          w.type = type;
          w.count = existing.length;
          ProcessEmitWarning(w);
        }
      }
      return target;
    }
    EventEmitter.prototype.addListener = function addListener(type, listener) {
      return _addListener(this, type, listener, false);
    };
    EventEmitter.prototype.on = EventEmitter.prototype.addListener;
    EventEmitter.prototype.prependListener = function prependListener(type, listener) {
      return _addListener(this, type, listener, true);
    };
    function onceWrapper() {
      if (!this.fired) {
        this.target.removeListener(this.type, this.wrapFn);
        this.fired = true;
        if (arguments.length === 0)
          return this.listener.call(this.target);
        return this.listener.apply(this.target, arguments);
      }
    }
    function _onceWrap(target, type, listener) {
      var state = { fired: false, wrapFn: void 0, target, type, listener };
      var wrapped = onceWrapper.bind(state);
      wrapped.listener = listener;
      state.wrapFn = wrapped;
      return wrapped;
    }
    EventEmitter.prototype.once = function once2(type, listener) {
      checkListener(listener);
      this.on(type, _onceWrap(this, type, listener));
      return this;
    };
    EventEmitter.prototype.prependOnceListener = function prependOnceListener(type, listener) {
      checkListener(listener);
      this.prependListener(type, _onceWrap(this, type, listener));
      return this;
    };
    EventEmitter.prototype.removeListener = function removeListener(type, listener) {
      var list, events, position, i, originalListener;
      checkListener(listener);
      events = this._events;
      if (events === void 0)
        return this;
      list = events[type];
      if (list === void 0)
        return this;
      if (list === listener || list.listener === listener) {
        if (--this._eventsCount === 0)
          this._events = /* @__PURE__ */ Object.create(null);
        else {
          delete events[type];
          if (events.removeListener)
            this.emit("removeListener", type, list.listener || listener);
        }
      } else if (typeof list !== "function") {
        position = -1;
        for (i = list.length - 1; i >= 0; i--) {
          if (list[i] === listener || list[i].listener === listener) {
            originalListener = list[i].listener;
            position = i;
            break;
          }
        }
        if (position < 0)
          return this;
        if (position === 0)
          list.shift();
        else {
          spliceOne(list, position);
        }
        if (list.length === 1)
          events[type] = list[0];
        if (events.removeListener !== void 0)
          this.emit("removeListener", type, originalListener || listener);
      }
      return this;
    };
    EventEmitter.prototype.off = EventEmitter.prototype.removeListener;
    EventEmitter.prototype.removeAllListeners = function removeAllListeners(type) {
      var listeners, events, i;
      events = this._events;
      if (events === void 0)
        return this;
      if (events.removeListener === void 0) {
        if (arguments.length === 0) {
          this._events = /* @__PURE__ */ Object.create(null);
          this._eventsCount = 0;
        } else if (events[type] !== void 0) {
          if (--this._eventsCount === 0)
            this._events = /* @__PURE__ */ Object.create(null);
          else
            delete events[type];
        }
        return this;
      }
      if (arguments.length === 0) {
        var keys = Object.keys(events);
        var key;
        for (i = 0; i < keys.length; ++i) {
          key = keys[i];
          if (key === "removeListener") continue;
          this.removeAllListeners(key);
        }
        this.removeAllListeners("removeListener");
        this._events = /* @__PURE__ */ Object.create(null);
        this._eventsCount = 0;
        return this;
      }
      listeners = events[type];
      if (typeof listeners === "function") {
        this.removeListener(type, listeners);
      } else if (listeners !== void 0) {
        for (i = listeners.length - 1; i >= 0; i--) {
          this.removeListener(type, listeners[i]);
        }
      }
      return this;
    };
    function _listeners(target, type, unwrap) {
      var events = target._events;
      if (events === void 0)
        return [];
      var evlistener = events[type];
      if (evlistener === void 0)
        return [];
      if (typeof evlistener === "function")
        return unwrap ? [evlistener.listener || evlistener] : [evlistener];
      return unwrap ? unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);
    }
    EventEmitter.prototype.listeners = function listeners(type) {
      return _listeners(this, type, true);
    };
    EventEmitter.prototype.rawListeners = function rawListeners(type) {
      return _listeners(this, type, false);
    };
    EventEmitter.listenerCount = function(emitter, type) {
      if (typeof emitter.listenerCount === "function") {
        return emitter.listenerCount(type);
      } else {
        return listenerCount.call(emitter, type);
      }
    };
    EventEmitter.prototype.listenerCount = listenerCount;
    function listenerCount(type) {
      var events = this._events;
      if (events !== void 0) {
        var evlistener = events[type];
        if (typeof evlistener === "function") {
          return 1;
        } else if (evlistener !== void 0) {
          return evlistener.length;
        }
      }
      return 0;
    }
    EventEmitter.prototype.eventNames = function eventNames() {
      return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];
    };
    function arrayClone(arr, n) {
      var copy = new Array(n);
      for (var i = 0; i < n; ++i)
        copy[i] = arr[i];
      return copy;
    }
    function spliceOne(list, index) {
      for (; index + 1 < list.length; index++)
        list[index] = list[index + 1];
      list.pop();
    }
    function unwrapListeners(arr) {
      var ret = new Array(arr.length);
      for (var i = 0; i < ret.length; ++i) {
        ret[i] = arr[i].listener || arr[i];
      }
      return ret;
    }
    function once(emitter, name) {
      return new Promise(function(resolve, reject) {
        function errorListener(err) {
          emitter.removeListener(name, resolver);
          reject(err);
        }
        function resolver() {
          if (typeof emitter.removeListener === "function") {
            emitter.removeListener("error", errorListener);
          }
          resolve([].slice.call(arguments));
        }
        ;
        eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });
        if (name !== "error") {
          addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });
        }
      });
    }
    function addErrorHandlerIfEventEmitter(emitter, handler, flags) {
      if (typeof emitter.on === "function") {
        eventTargetAgnosticAddListener(emitter, "error", handler, flags);
      }
    }
    function eventTargetAgnosticAddListener(emitter, name, listener, flags) {
      if (typeof emitter.on === "function") {
        if (flags.once) {
          emitter.once(name, listener);
        } else {
          emitter.on(name, listener);
        }
      } else if (typeof emitter.addEventListener === "function") {
        emitter.addEventListener(name, function wrapListener(arg) {
          if (flags.once) {
            emitter.removeEventListener(name, wrapListener);
          }
          listener(arg);
        });
      } else {
        throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type ' + typeof emitter);
      }
    }
  }
});

// node_modules/@pipecat-ai/client-js/dist/index.module.js
var import_clone_deep = __toESM(require_clone_deep());
var import_events = __toESM(require_events());

// node_modules/uuid/dist/esm-browser/regex.js
var regex_default = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;

// node_modules/uuid/dist/esm-browser/validate.js
function validate(uuid) {
  return typeof uuid === "string" && regex_default.test(uuid);
}
var validate_default = validate;

// node_modules/uuid/dist/esm-browser/parse.js
function parse(uuid) {
  if (!validate_default(uuid)) {
    throw TypeError("Invalid UUID");
  }
  var v;
  var arr = new Uint8Array(16);
  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;
  arr[1] = v >>> 16 & 255;
  arr[2] = v >>> 8 & 255;
  arr[3] = v & 255;
  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;
  arr[5] = v & 255;
  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;
  arr[7] = v & 255;
  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;
  arr[9] = v & 255;
  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 1099511627776 & 255;
  arr[11] = v / 4294967296 & 255;
  arr[12] = v >>> 24 & 255;
  arr[13] = v >>> 16 & 255;
  arr[14] = v >>> 8 & 255;
  arr[15] = v & 255;
  return arr;
}
var parse_default = parse;

// node_modules/uuid/dist/esm-browser/stringify.js
var byteToHex = [];
for (i = 0; i < 256; ++i) {
  byteToHex.push((i + 256).toString(16).slice(1));
}
var i;
function unsafeStringify(arr, offset = 0) {
  return (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + "-" + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + "-" + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + "-" + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + "-" + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase();
}

// node_modules/uuid/dist/esm-browser/rng.js
var getRandomValues;
var rnds8 = new Uint8Array(16);
function rng() {
  if (!getRandomValues) {
    getRandomValues = typeof crypto !== "undefined" && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);
    if (!getRandomValues) {
      throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");
    }
  }
  return getRandomValues(rnds8);
}

// node_modules/uuid/dist/esm-browser/v35.js
function stringToBytes(str) {
  str = unescape(encodeURIComponent(str));
  var bytes = [];
  for (var i = 0; i < str.length; ++i) {
    bytes.push(str.charCodeAt(i));
  }
  return bytes;
}
var DNS = "6ba7b810-9dad-11d1-80b4-00c04fd430c8";
var URL = "6ba7b811-9dad-11d1-80b4-00c04fd430c8";
function v35(name, version, hashfunc) {
  function generateUUID(value, namespace, buf, offset) {
    var _namespace;
    if (typeof value === "string") {
      value = stringToBytes(value);
    }
    if (typeof namespace === "string") {
      namespace = parse_default(namespace);
    }
    if (((_namespace = namespace) === null || _namespace === void 0 ? void 0 : _namespace.length) !== 16) {
      throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");
    }
    var bytes = new Uint8Array(16 + value.length);
    bytes.set(namespace);
    bytes.set(value, namespace.length);
    bytes = hashfunc(bytes);
    bytes[6] = bytes[6] & 15 | version;
    bytes[8] = bytes[8] & 63 | 128;
    if (buf) {
      offset = offset || 0;
      for (var i = 0; i < 16; ++i) {
        buf[offset + i] = bytes[i];
      }
      return buf;
    }
    return unsafeStringify(bytes);
  }
  try {
    generateUUID.name = name;
  } catch (err) {
  }
  generateUUID.DNS = DNS;
  generateUUID.URL = URL;
  return generateUUID;
}

// node_modules/uuid/dist/esm-browser/md5.js
function md5(bytes) {
  if (typeof bytes === "string") {
    var msg = unescape(encodeURIComponent(bytes));
    bytes = new Uint8Array(msg.length);
    for (var i = 0; i < msg.length; ++i) {
      bytes[i] = msg.charCodeAt(i);
    }
  }
  return md5ToHexEncodedArray(wordsToMd5(bytesToWords(bytes), bytes.length * 8));
}
function md5ToHexEncodedArray(input) {
  var output = [];
  var length32 = input.length * 32;
  var hexTab = "0123456789abcdef";
  for (var i = 0; i < length32; i += 8) {
    var x = input[i >> 5] >>> i % 32 & 255;
    var hex = parseInt(hexTab.charAt(x >>> 4 & 15) + hexTab.charAt(x & 15), 16);
    output.push(hex);
  }
  return output;
}
function getOutputLength(inputLength8) {
  return (inputLength8 + 64 >>> 9 << 4) + 14 + 1;
}
function wordsToMd5(x, len) {
  x[len >> 5] |= 128 << len % 32;
  x[getOutputLength(len) - 1] = len;
  var a = 1732584193;
  var b = -271733879;
  var c = -1732584194;
  var d = 271733878;
  for (var i = 0; i < x.length; i += 16) {
    var olda = a;
    var oldb = b;
    var oldc = c;
    var oldd = d;
    a = md5ff(a, b, c, d, x[i], 7, -680876936);
    d = md5ff(d, a, b, c, x[i + 1], 12, -389564586);
    c = md5ff(c, d, a, b, x[i + 2], 17, 606105819);
    b = md5ff(b, c, d, a, x[i + 3], 22, -1044525330);
    a = md5ff(a, b, c, d, x[i + 4], 7, -176418897);
    d = md5ff(d, a, b, c, x[i + 5], 12, 1200080426);
    c = md5ff(c, d, a, b, x[i + 6], 17, -1473231341);
    b = md5ff(b, c, d, a, x[i + 7], 22, -45705983);
    a = md5ff(a, b, c, d, x[i + 8], 7, 1770035416);
    d = md5ff(d, a, b, c, x[i + 9], 12, -1958414417);
    c = md5ff(c, d, a, b, x[i + 10], 17, -42063);
    b = md5ff(b, c, d, a, x[i + 11], 22, -1990404162);
    a = md5ff(a, b, c, d, x[i + 12], 7, 1804603682);
    d = md5ff(d, a, b, c, x[i + 13], 12, -40341101);
    c = md5ff(c, d, a, b, x[i + 14], 17, -1502002290);
    b = md5ff(b, c, d, a, x[i + 15], 22, 1236535329);
    a = md5gg(a, b, c, d, x[i + 1], 5, -165796510);
    d = md5gg(d, a, b, c, x[i + 6], 9, -1069501632);
    c = md5gg(c, d, a, b, x[i + 11], 14, 643717713);
    b = md5gg(b, c, d, a, x[i], 20, -373897302);
    a = md5gg(a, b, c, d, x[i + 5], 5, -701558691);
    d = md5gg(d, a, b, c, x[i + 10], 9, 38016083);
    c = md5gg(c, d, a, b, x[i + 15], 14, -660478335);
    b = md5gg(b, c, d, a, x[i + 4], 20, -405537848);
    a = md5gg(a, b, c, d, x[i + 9], 5, 568446438);
    d = md5gg(d, a, b, c, x[i + 14], 9, -1019803690);
    c = md5gg(c, d, a, b, x[i + 3], 14, -187363961);
    b = md5gg(b, c, d, a, x[i + 8], 20, 1163531501);
    a = md5gg(a, b, c, d, x[i + 13], 5, -1444681467);
    d = md5gg(d, a, b, c, x[i + 2], 9, -51403784);
    c = md5gg(c, d, a, b, x[i + 7], 14, 1735328473);
    b = md5gg(b, c, d, a, x[i + 12], 20, -1926607734);
    a = md5hh(a, b, c, d, x[i + 5], 4, -378558);
    d = md5hh(d, a, b, c, x[i + 8], 11, -2022574463);
    c = md5hh(c, d, a, b, x[i + 11], 16, 1839030562);
    b = md5hh(b, c, d, a, x[i + 14], 23, -35309556);
    a = md5hh(a, b, c, d, x[i + 1], 4, -1530992060);
    d = md5hh(d, a, b, c, x[i + 4], 11, 1272893353);
    c = md5hh(c, d, a, b, x[i + 7], 16, -155497632);
    b = md5hh(b, c, d, a, x[i + 10], 23, -1094730640);
    a = md5hh(a, b, c, d, x[i + 13], 4, 681279174);
    d = md5hh(d, a, b, c, x[i], 11, -358537222);
    c = md5hh(c, d, a, b, x[i + 3], 16, -722521979);
    b = md5hh(b, c, d, a, x[i + 6], 23, 76029189);
    a = md5hh(a, b, c, d, x[i + 9], 4, -640364487);
    d = md5hh(d, a, b, c, x[i + 12], 11, -421815835);
    c = md5hh(c, d, a, b, x[i + 15], 16, 530742520);
    b = md5hh(b, c, d, a, x[i + 2], 23, -995338651);
    a = md5ii(a, b, c, d, x[i], 6, -198630844);
    d = md5ii(d, a, b, c, x[i + 7], 10, 1126891415);
    c = md5ii(c, d, a, b, x[i + 14], 15, -1416354905);
    b = md5ii(b, c, d, a, x[i + 5], 21, -57434055);
    a = md5ii(a, b, c, d, x[i + 12], 6, 1700485571);
    d = md5ii(d, a, b, c, x[i + 3], 10, -1894986606);
    c = md5ii(c, d, a, b, x[i + 10], 15, -1051523);
    b = md5ii(b, c, d, a, x[i + 1], 21, -2054922799);
    a = md5ii(a, b, c, d, x[i + 8], 6, 1873313359);
    d = md5ii(d, a, b, c, x[i + 15], 10, -30611744);
    c = md5ii(c, d, a, b, x[i + 6], 15, -1560198380);
    b = md5ii(b, c, d, a, x[i + 13], 21, 1309151649);
    a = md5ii(a, b, c, d, x[i + 4], 6, -145523070);
    d = md5ii(d, a, b, c, x[i + 11], 10, -1120210379);
    c = md5ii(c, d, a, b, x[i + 2], 15, 718787259);
    b = md5ii(b, c, d, a, x[i + 9], 21, -343485551);
    a = safeAdd(a, olda);
    b = safeAdd(b, oldb);
    c = safeAdd(c, oldc);
    d = safeAdd(d, oldd);
  }
  return [a, b, c, d];
}
function bytesToWords(input) {
  if (input.length === 0) {
    return [];
  }
  var length8 = input.length * 8;
  var output = new Uint32Array(getOutputLength(length8));
  for (var i = 0; i < length8; i += 8) {
    output[i >> 5] |= (input[i / 8] & 255) << i % 32;
  }
  return output;
}
function safeAdd(x, y) {
  var lsw = (x & 65535) + (y & 65535);
  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
  return msw << 16 | lsw & 65535;
}
function bitRotateLeft(num, cnt) {
  return num << cnt | num >>> 32 - cnt;
}
function md5cmn(q, a, b, x, s, t) {
  return safeAdd(bitRotateLeft(safeAdd(safeAdd(a, q), safeAdd(x, t)), s), b);
}
function md5ff(a, b, c, d, x, s, t) {
  return md5cmn(b & c | ~b & d, a, b, x, s, t);
}
function md5gg(a, b, c, d, x, s, t) {
  return md5cmn(b & d | c & ~d, a, b, x, s, t);
}
function md5hh(a, b, c, d, x, s, t) {
  return md5cmn(b ^ c ^ d, a, b, x, s, t);
}
function md5ii(a, b, c, d, x, s, t) {
  return md5cmn(c ^ (b | ~d), a, b, x, s, t);
}
var md5_default = md5;

// node_modules/uuid/dist/esm-browser/v3.js
var v3 = v35("v3", 48, md5_default);

// node_modules/uuid/dist/esm-browser/native.js
var randomUUID = typeof crypto !== "undefined" && crypto.randomUUID && crypto.randomUUID.bind(crypto);
var native_default = {
  randomUUID
};

// node_modules/uuid/dist/esm-browser/v4.js
function v4(options, buf, offset) {
  if (native_default.randomUUID && !buf && !options) {
    return native_default.randomUUID();
  }
  options = options || {};
  var rnds = options.random || (options.rng || rng)();
  rnds[6] = rnds[6] & 15 | 64;
  rnds[8] = rnds[8] & 63 | 128;
  if (buf) {
    offset = offset || 0;
    for (var i = 0; i < 16; ++i) {
      buf[offset + i] = rnds[i];
    }
    return buf;
  }
  return unsafeStringify(rnds);
}
var v4_default = v4;

// node_modules/uuid/dist/esm-browser/sha1.js
function f(s, x, y, z) {
  switch (s) {
    case 0:
      return x & y ^ ~x & z;
    case 1:
      return x ^ y ^ z;
    case 2:
      return x & y ^ x & z ^ y & z;
    case 3:
      return x ^ y ^ z;
  }
}
function ROTL(x, n) {
  return x << n | x >>> 32 - n;
}
function sha1(bytes) {
  var K = [1518500249, 1859775393, 2400959708, 3395469782];
  var H = [1732584193, 4023233417, 2562383102, 271733878, 3285377520];
  if (typeof bytes === "string") {
    var msg = unescape(encodeURIComponent(bytes));
    bytes = [];
    for (var i = 0; i < msg.length; ++i) {
      bytes.push(msg.charCodeAt(i));
    }
  } else if (!Array.isArray(bytes)) {
    bytes = Array.prototype.slice.call(bytes);
  }
  bytes.push(128);
  var l = bytes.length / 4 + 2;
  var N = Math.ceil(l / 16);
  var M = new Array(N);
  for (var _i = 0; _i < N; ++_i) {
    var arr = new Uint32Array(16);
    for (var j = 0; j < 16; ++j) {
      arr[j] = bytes[_i * 64 + j * 4] << 24 | bytes[_i * 64 + j * 4 + 1] << 16 | bytes[_i * 64 + j * 4 + 2] << 8 | bytes[_i * 64 + j * 4 + 3];
    }
    M[_i] = arr;
  }
  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);
  M[N - 1][14] = Math.floor(M[N - 1][14]);
  M[N - 1][15] = (bytes.length - 1) * 8 & 4294967295;
  for (var _i2 = 0; _i2 < N; ++_i2) {
    var W = new Uint32Array(80);
    for (var t = 0; t < 16; ++t) {
      W[t] = M[_i2][t];
    }
    for (var _t = 16; _t < 80; ++_t) {
      W[_t] = ROTL(W[_t - 3] ^ W[_t - 8] ^ W[_t - 14] ^ W[_t - 16], 1);
    }
    var a = H[0];
    var b = H[1];
    var c = H[2];
    var d = H[3];
    var e = H[4];
    for (var _t2 = 0; _t2 < 80; ++_t2) {
      var s = Math.floor(_t2 / 20);
      var T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[_t2] >>> 0;
      e = d;
      d = c;
      c = ROTL(b, 30) >>> 0;
      b = a;
      a = T;
    }
    H[0] = H[0] + a >>> 0;
    H[1] = H[1] + b >>> 0;
    H[2] = H[2] + c >>> 0;
    H[3] = H[3] + d >>> 0;
    H[4] = H[4] + e >>> 0;
  }
  return [H[0] >> 24 & 255, H[0] >> 16 & 255, H[0] >> 8 & 255, H[0] & 255, H[1] >> 24 & 255, H[1] >> 16 & 255, H[1] >> 8 & 255, H[1] & 255, H[2] >> 24 & 255, H[2] >> 16 & 255, H[2] >> 8 & 255, H[2] & 255, H[3] >> 24 & 255, H[3] >> 16 & 255, H[3] >> 8 & 255, H[3] & 255, H[4] >> 24 & 255, H[4] >> 16 & 255, H[4] >> 8 & 255, H[4] & 255];
}
var sha1_default = sha1;

// node_modules/uuid/dist/esm-browser/v5.js
var v5 = v35("v5", 80, sha1_default);

// node_modules/@pipecat-ai/client-js/dist/index.module.js
function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, { get: v, set: s, enumerable: true, configurable: true });
}
function $parcel$interopDefault(a) {
  return a && a.__esModule ? a.default : a;
}
var $4bb349f22aee5185$exports = {};
$parcel$export($4bb349f22aee5185$exports, "httpActionGenerator", () => $4bb349f22aee5185$export$8728b60ea57bf43e);
async function $4bb349f22aee5185$export$8728b60ea57bf43e(actionUrl, action, params, handleResponse) {
  try {
    (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[RTVI] Fetch action", actionUrl, action);
    const headers = new Headers({
      ...Object.fromEntries((params.headers ?? new Headers()).entries())
    });
    if (!headers.has("Content-Type")) headers.set("Content-Type", "application/json");
    headers.set("Cache-Control", "no-cache");
    headers.set("Connection", "keep-alive");
    const response = await fetch(actionUrl, {
      method: "POST",
      headers,
      body: JSON.stringify({
        ...params.requestData,
        actions: [
          action
        ]
      })
    });
    const contentType = response.headers.get("content-type");
    if (!response.ok) {
      const errorMessage = await response.text();
      throw new (0, $8ead7b33b8402751$export$59b4786f333aac02)(`Failed to resolve action: ${errorMessage}`, response.status);
    }
    if (response.body && (contentType == null ? void 0 : contentType.includes("text/event-stream"))) {
      const reader = response.body.pipeThrough(new TextDecoderStream()).getReader();
      let buffer = "";
      while (true) {
        const { value, done } = await reader.read();
        if (done) break;
        buffer += value;
        let boundary = buffer.indexOf("\n\n");
        while (boundary !== -1) {
          const message = buffer.slice(0, boundary);
          buffer = buffer.slice(boundary + 2);
          const lines = message.split("\n");
          let encodedData = "";
          for (const line of lines) {
            const colonIndex = line.indexOf(":");
            if (colonIndex !== -1) encodedData += line.slice(colonIndex + 1).trim();
          }
          try {
            const jsonData = atob(encodedData);
            const parsedData = JSON.parse(jsonData);
            handleResponse(parsedData);
          } catch (error) {
            (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).error("[RTVI] Failed to parse JSON:", error);
            throw error;
          }
          boundary = buffer.indexOf("\n\n");
        }
      }
    } else {
      const data = await response.json();
      handleResponse(data);
    }
  } catch (error) {
    (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).error("[RTVI] Error during fetch:", error);
    throw error;
  }
}
var $a7c324a73303ad55$exports = {};
$parcel$export($a7c324a73303ad55$exports, "RTVIClient", () => $a7c324a73303ad55$export$fa42a01c1d60f4a1);
var $e3bad9cc25e327f7$exports = {};
$e3bad9cc25e327f7$exports = JSON.parse('{"name":"@pipecat-ai/client-js","version":"0.3.4","license":"BSD-2-Clause","main":"dist/index.js","module":"dist/index.module.js","types":"dist/index.d.ts","source":"src/index.ts","repository":{"type":"git","url":"git+https://github.com/pipecat-ai/pipecat-client-web.git"},"files":["dist","package.json","README.md"],"scripts":{"build":"jest --silent && parcel build --no-cache","dev":"parcel watch","lint":"eslint src/ --report-unused-disable-directives --max-warnings 0","test":"jest"},"jest":{"preset":"ts-jest","testEnvironment":"node"},"devDependencies":{"@jest/globals":"^29.7.0","@types/clone-deep":"^4.0.4","@types/jest":"^29.5.12","@types/uuid":"^10.0.0","eslint":"^9.11.1","eslint-config-prettier":"^9.1.0","eslint-plugin-simple-import-sort":"^12.1.1","jest":"^29.7.0","ts-jest":"^29.2.5"},"dependencies":{"@types/events":"^3.0.3","clone-deep":"^4.0.1","events":"^3.3.0","typed-emitter":"^2.1.0","uuid":"^10.0.0"}}');
var $8ead7b33b8402751$exports = {};
$parcel$export($8ead7b33b8402751$exports, "RTVIError", () => $8ead7b33b8402751$export$59b4786f333aac02);
$parcel$export($8ead7b33b8402751$exports, "ConnectionTimeoutError", () => $8ead7b33b8402751$export$c67992fa684a81a6);
$parcel$export($8ead7b33b8402751$exports, "StartBotError", () => $8ead7b33b8402751$export$e7544ab812238a61);
$parcel$export($8ead7b33b8402751$exports, "TransportStartError", () => $8ead7b33b8402751$export$e0624a511a2c4e9);
$parcel$export($8ead7b33b8402751$exports, "BotNotReadyError", () => $8ead7b33b8402751$export$885fb96b850e8fbb);
$parcel$export($8ead7b33b8402751$exports, "ConfigUpdateError", () => $8ead7b33b8402751$export$4eda4fd287fbbca5);
$parcel$export($8ead7b33b8402751$exports, "ActionEndpointNotSetError", () => $8ead7b33b8402751$export$be839f0100cd3132);
var $8ead7b33b8402751$export$59b4786f333aac02 = class extends Error {
  constructor(message, status) {
    super(message);
    this.status = status;
  }
};
var $8ead7b33b8402751$export$c67992fa684a81a6 = class extends $8ead7b33b8402751$export$59b4786f333aac02 {
  constructor(message) {
    super(message ?? "Bot did not enter ready state within the specified timeout period.");
  }
};
var $8ead7b33b8402751$export$e7544ab812238a61 = class extends $8ead7b33b8402751$export$59b4786f333aac02 {
  constructor(message, status) {
    super(message ?? `Failed to connect / invalid auth bundle from base url`, status ?? 500);
    this.error = "invalid-request-error";
  }
};
var $8ead7b33b8402751$export$e0624a511a2c4e9 = class extends $8ead7b33b8402751$export$59b4786f333aac02 {
  constructor(message) {
    super(message ?? "Unable to connect to transport");
  }
};
var $8ead7b33b8402751$export$885fb96b850e8fbb = class extends $8ead7b33b8402751$export$59b4786f333aac02 {
  constructor(message) {
    super(message ?? "Attempt to call action on transport when not in 'ready' state.");
  }
};
var $8ead7b33b8402751$export$4eda4fd287fbbca5 = class extends $8ead7b33b8402751$export$59b4786f333aac02 {
  constructor(message) {
    super(message ?? "Unable to update configuration");
    this.status = 400;
  }
};
var $8ead7b33b8402751$export$be839f0100cd3132 = class extends $8ead7b33b8402751$export$59b4786f333aac02 {
  constructor(message) {
    super(message ?? "Action endpoint is not set");
  }
};
function $16f019d4d16917df$export$f1586721024c4dab(_target, propertyKey, descriptor) {
  const originalMethod = descriptor.value;
  descriptor.value = function(...args) {
    if (this.state === "ready") return originalMethod.apply(this, args);
    else throw new (0, $8ead7b33b8402751$export$885fb96b850e8fbb)(`Attempt to call ${propertyKey.toString()} when transport not in ready state. Await connect() first.`);
  };
  return descriptor;
}
function $16f019d4d16917df$export$5c35b4fe6fa8c9a6(...states) {
  states = [
    "ready",
    ...states
  ];
  return function(_target, propertyKey, descriptor) {
    const originalGetter = descriptor.get;
    descriptor.get = function() {
      if (states.includes(this.state)) return originalGetter == null ? void 0 : originalGetter.apply(this);
      else throw new (0, $8ead7b33b8402751$export$885fb96b850e8fbb)(`Attempt to call ${propertyKey.toString()} when transport not in ${states}. Await connect() first.`);
    };
    return descriptor;
  };
}
var $f9fc0c57b9aaed9c$exports = {};
$parcel$export($f9fc0c57b9aaed9c$exports, "RTVIEvent", () => $f9fc0c57b9aaed9c$export$6b4624d233c61fcb);
var $f9fc0c57b9aaed9c$export$6b4624d233c61fcb;
(function(RTVIEvent) {
  RTVIEvent["MessageError"] = "messageError";
  RTVIEvent["Error"] = "error";
  RTVIEvent["Connected"] = "connected";
  RTVIEvent["Disconnected"] = "disconnected";
  RTVIEvent["TransportStateChanged"] = "transportStateChanged";
  RTVIEvent["Config"] = "config";
  RTVIEvent["ConfigDescribe"] = "configDescribe";
  RTVIEvent["ActionsAvailable"] = "actionsAvailable";
  RTVIEvent["ParticipantConnected"] = "participantConnected";
  RTVIEvent["ParticipantLeft"] = "participantLeft";
  RTVIEvent["TrackStarted"] = "trackStarted";
  RTVIEvent["TrackStopped"] = "trackStopped";
  RTVIEvent["ScreenTrackStarted"] = "screenTrackStarted";
  RTVIEvent["ScreenTrackStopped"] = "screenTrackStopped";
  RTVIEvent["ScreenShareError"] = "screenShareError";
  RTVIEvent["AvailableCamsUpdated"] = "availableCamsUpdated";
  RTVIEvent["AvailableMicsUpdated"] = "availableMicsUpdated";
  RTVIEvent["AvailableSpeakersUpdated"] = "availableSpeakersUpdated";
  RTVIEvent["CamUpdated"] = "camUpdated";
  RTVIEvent["MicUpdated"] = "micUpdated";
  RTVIEvent["SpeakerUpdated"] = "speakerUpdated";
  RTVIEvent["BotConnected"] = "botConnected";
  RTVIEvent["BotReady"] = "botReady";
  RTVIEvent["BotDisconnected"] = "botDisconnected";
  RTVIEvent["BotStartedSpeaking"] = "botStartedSpeaking";
  RTVIEvent["BotStoppedSpeaking"] = "botStoppedSpeaking";
  RTVIEvent["RemoteAudioLevel"] = "remoteAudioLevel";
  RTVIEvent["UserStartedSpeaking"] = "userStartedSpeaking";
  RTVIEvent["UserStoppedSpeaking"] = "userStoppedSpeaking";
  RTVIEvent["LocalAudioLevel"] = "localAudioLevel";
  RTVIEvent["Metrics"] = "metrics";
  RTVIEvent["UserTranscript"] = "userTranscript";
  RTVIEvent["BotTranscript"] = "botTranscript";
  RTVIEvent["BotLlmText"] = "botLlmText";
  RTVIEvent["BotLlmStarted"] = "botLlmStarted";
  RTVIEvent["BotLlmStopped"] = "botLlmStopped";
  RTVIEvent["BotTtsText"] = "botTtsText";
  RTVIEvent["BotTtsStarted"] = "botTtsStarted";
  RTVIEvent["BotTtsStopped"] = "botTtsStopped";
  RTVIEvent["LLMFunctionCall"] = "llmFunctionCall";
  RTVIEvent["LLMFunctionCallStart"] = "llmFunctionCallStart";
  RTVIEvent["LLMJsonCompletion"] = "llmJsonCompletion";
  RTVIEvent["StorageItemStored"] = "storageItemStored";
  RTVIEvent["BotLlmSearchResponse"] = "botLlmSearchResponse";
  RTVIEvent["ServerMessage"] = "serverMessage";
})($f9fc0c57b9aaed9c$export$6b4624d233c61fcb || ($f9fc0c57b9aaed9c$export$6b4624d233c61fcb = {}));
var $7614fb2168c523cc$exports = {};
$parcel$export($7614fb2168c523cc$exports, "RTVIClientHelper", () => $7614fb2168c523cc$export$23bc637255b2a471);
var $7614fb2168c523cc$export$23bc637255b2a471 = class {
  constructor(options) {
    this._options = options;
  }
  set client(client) {
    this._client = client;
  }
  set service(service) {
    this._service = service;
  }
};
var $7afbbd59ebaa42bf$exports = {};
$parcel$export($7afbbd59ebaa42bf$exports, "LogLevel", () => $7afbbd59ebaa42bf$export$243e62d78d3b544d);
$parcel$export($7afbbd59ebaa42bf$exports, "logger", () => $7afbbd59ebaa42bf$export$af88d00dbe7f521);
var $7afbbd59ebaa42bf$export$243e62d78d3b544d;
(function(LogLevel) {
  LogLevel[LogLevel["NONE"] = 0] = "NONE";
  LogLevel[LogLevel["ERROR"] = 1] = "ERROR";
  LogLevel[LogLevel["WARN"] = 2] = "WARN";
  LogLevel[LogLevel["INFO"] = 3] = "INFO";
  LogLevel[LogLevel["DEBUG"] = 4] = "DEBUG";
})($7afbbd59ebaa42bf$export$243e62d78d3b544d || ($7afbbd59ebaa42bf$export$243e62d78d3b544d = {}));
var $7afbbd59ebaa42bf$var$Logger = class _$7afbbd59ebaa42bf$var$Logger {
  constructor() {
    this.level = $7afbbd59ebaa42bf$export$243e62d78d3b544d.DEBUG;
  }
  static getInstance() {
    if (!_$7afbbd59ebaa42bf$var$Logger.instance) _$7afbbd59ebaa42bf$var$Logger.instance = new _$7afbbd59ebaa42bf$var$Logger();
    return _$7afbbd59ebaa42bf$var$Logger.instance;
  }
  setLevel(level) {
    this.level = level;
  }
  debug(...args) {
    if (this.level >= $7afbbd59ebaa42bf$export$243e62d78d3b544d.DEBUG) console.debug(...args);
  }
  info(...args) {
    if (this.level >= $7afbbd59ebaa42bf$export$243e62d78d3b544d.INFO) console.info(...args);
  }
  warn(...args) {
    if (this.level >= $7afbbd59ebaa42bf$export$243e62d78d3b544d.WARN) console.warn(...args);
  }
  error(...args) {
    if (this.level >= $7afbbd59ebaa42bf$export$243e62d78d3b544d.ERROR) console.error(...args);
  }
};
var $7afbbd59ebaa42bf$export$af88d00dbe7f521 = $7afbbd59ebaa42bf$var$Logger.getInstance();
var $b48f893ed1354c1e$exports = {};
$parcel$export($b48f893ed1354c1e$exports, "RTVI_MESSAGE_LABEL", () => $b48f893ed1354c1e$export$882b13c7fda338f5);
$parcel$export($b48f893ed1354c1e$exports, "RTVIMessageType", () => $b48f893ed1354c1e$export$38b3db05cbf0e240);
$parcel$export($b48f893ed1354c1e$exports, "RTVIMessage", () => $b48f893ed1354c1e$export$69aa9ab0334b212);
$parcel$export($b48f893ed1354c1e$exports, "RTVIActionRequest", () => $b48f893ed1354c1e$export$378529d7a8bead8b);
$parcel$export($b48f893ed1354c1e$exports, "MessageDispatcher", () => $b48f893ed1354c1e$export$e9a960646cc432aa);
var $b48f893ed1354c1e$export$882b13c7fda338f5 = "rtvi-ai";
var $b48f893ed1354c1e$export$38b3db05cbf0e240;
(function(RTVIMessageType) {
  RTVIMessageType["CLIENT_READY"] = "client-ready";
  RTVIMessageType["UPDATE_CONFIG"] = "update-config";
  RTVIMessageType["GET_CONFIG"] = "get-config";
  RTVIMessageType["DESCRIBE_CONFIG"] = "describe-config";
  RTVIMessageType["DESCRIBE_ACTIONS"] = "describe-actions";
  RTVIMessageType["DISCONNECT_BOT"] = "disconnect-bot";
  RTVIMessageType["ACTION"] = "action";
  RTVIMessageType["BOT_READY"] = "bot-ready";
  RTVIMessageType["ERROR"] = "error";
  RTVIMessageType["ERROR_RESPONSE"] = "error-response";
  RTVIMessageType["CONFIG"] = "config";
  RTVIMessageType["CONFIG_AVAILABLE"] = "config-available";
  RTVIMessageType["CONFIG_ERROR"] = "config-error";
  RTVIMessageType["ACTIONS_AVAILABLE"] = "actions-available";
  RTVIMessageType["ACTION_RESPONSE"] = "action-response";
  RTVIMessageType["METRICS"] = "metrics";
  RTVIMessageType["USER_TRANSCRIPTION"] = "user-transcription";
  RTVIMessageType["BOT_TRANSCRIPTION"] = "bot-transcription";
  RTVIMessageType["USER_STARTED_SPEAKING"] = "user-started-speaking";
  RTVIMessageType["USER_STOPPED_SPEAKING"] = "user-stopped-speaking";
  RTVIMessageType["BOT_STARTED_SPEAKING"] = "bot-started-speaking";
  RTVIMessageType["BOT_STOPPED_SPEAKING"] = "bot-stopped-speaking";
  RTVIMessageType["USER_LLM_TEXT"] = "user-llm-text";
  RTVIMessageType["BOT_LLM_TEXT"] = "bot-llm-text";
  RTVIMessageType["BOT_LLM_STARTED"] = "bot-llm-started";
  RTVIMessageType["BOT_LLM_STOPPED"] = "bot-llm-stopped";
  RTVIMessageType["BOT_TTS_TEXT"] = "bot-tts-text";
  RTVIMessageType["BOT_TTS_STARTED"] = "bot-tts-started";
  RTVIMessageType["BOT_TTS_STOPPED"] = "bot-tts-stopped";
  RTVIMessageType["BOT_LLM_SEARCH_RESPONSE"] = "bot-llm-search-response";
  RTVIMessageType["STORAGE_ITEM_STORED"] = "storage-item-stored";
  RTVIMessageType["SERVER_MESSAGE"] = "server-message";
})($b48f893ed1354c1e$export$38b3db05cbf0e240 || ($b48f893ed1354c1e$export$38b3db05cbf0e240 = {}));
var $b48f893ed1354c1e$export$69aa9ab0334b212 = class _$b48f893ed1354c1e$export$69aa9ab0334b212 {
  constructor(type, data, id) {
    this.label = $b48f893ed1354c1e$export$882b13c7fda338f5;
    this.type = type;
    this.data = data;
    this.id = id || (0, v4_default)().slice(0, 8);
  }
  // Outbound message types
  static clientReady() {
    return new _$b48f893ed1354c1e$export$69aa9ab0334b212($b48f893ed1354c1e$export$38b3db05cbf0e240.CLIENT_READY, {});
  }
  static updateConfig(config, interrupt = false) {
    return new _$b48f893ed1354c1e$export$69aa9ab0334b212($b48f893ed1354c1e$export$38b3db05cbf0e240.UPDATE_CONFIG, {
      config,
      interrupt
    });
  }
  static describeConfig() {
    return new _$b48f893ed1354c1e$export$69aa9ab0334b212($b48f893ed1354c1e$export$38b3db05cbf0e240.DESCRIBE_CONFIG, {});
  }
  static getBotConfig() {
    return new _$b48f893ed1354c1e$export$69aa9ab0334b212($b48f893ed1354c1e$export$38b3db05cbf0e240.GET_CONFIG, {});
  }
  static describeActions() {
    return new _$b48f893ed1354c1e$export$69aa9ab0334b212($b48f893ed1354c1e$export$38b3db05cbf0e240.DESCRIBE_ACTIONS, {});
  }
  static disconnectBot() {
    return new _$b48f893ed1354c1e$export$69aa9ab0334b212($b48f893ed1354c1e$export$38b3db05cbf0e240.DISCONNECT_BOT, {});
  }
  static error(message, fatal = false) {
    return new _$b48f893ed1354c1e$export$69aa9ab0334b212($b48f893ed1354c1e$export$38b3db05cbf0e240.ERROR, {
      message,
      fatal
    });
  }
};
var $b48f893ed1354c1e$export$378529d7a8bead8b = class extends $b48f893ed1354c1e$export$69aa9ab0334b212 {
  constructor(data) {
    super($b48f893ed1354c1e$export$38b3db05cbf0e240.ACTION, data);
  }
};
var $b48f893ed1354c1e$export$e9a960646cc432aa = class {
  constructor(client) {
    this._queue = new Array();
    this._gcTime = 1e4;
    this._queue = [];
    this._client = client;
  }
  dispatch(message) {
    const promise = new Promise((resolve, reject) => {
      this._queue.push({
        message,
        timestamp: Date.now(),
        resolve,
        reject
      });
    });
    (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[MessageDispatcher] dispatch", message);
    this._client.sendMessage(message);
    this._gc();
    return promise;
  }
  async dispatchAction(action, onMessage) {
    var _a;
    const promise = new Promise((resolve, reject) => {
      this._queue.push({
        message: action,
        timestamp: Date.now(),
        resolve,
        reject
      });
    });
    (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[MessageDispatcher] action", action);
    if (this._client.connected)
      this._client.sendMessage(action);
    else {
      if (!((_a = this._client.params.endpoints) == null ? void 0 : _a.action)) {
        (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).error("[MessageDispatcher] Action endpoint is required when dispatching action in disconnected state");
        throw new (0, $8ead7b33b8402751$export$be839f0100cd3132)();
      }
      const actionUrl = this._client.constructUrl("action");
      try {
        await (0, $4bb349f22aee5185$export$8728b60ea57bf43e)(actionUrl, action, this._client.params, (response) => {
          onMessage(response);
        });
      } catch (e) {
        onMessage(new $b48f893ed1354c1e$export$69aa9ab0334b212($b48f893ed1354c1e$export$38b3db05cbf0e240.ERROR_RESPONSE, `Action endpoint '${actionUrl}' returned an error response`, action.id));
      }
    }
    this._gc();
    return promise;
  }
  _resolveReject(message, resolve = true) {
    const queuedMessage = this._queue.find((msg) => msg.message.id === message.id);
    if (queuedMessage) {
      if (resolve) {
        (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[MessageDispatcher] Resolve", message);
        queuedMessage.resolve(message.type === $b48f893ed1354c1e$export$38b3db05cbf0e240.ACTION_RESPONSE ? message : message);
      } else {
        (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[MessageDispatcher] Reject", message);
        queuedMessage.reject(message);
      }
      this._queue = this._queue.filter((msg) => msg.message.id !== message.id);
      (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[MessageDispatcher] Queue", this._queue);
    }
    return message;
  }
  resolve(message) {
    return this._resolveReject(message, true);
  }
  reject(message) {
    return this._resolveReject(message, false);
  }
  _gc() {
    this._queue = this._queue.filter((msg) => {
      return Date.now() - msg.timestamp < this._gcTime;
    });
    (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[MessageDispatcher] GC", this._queue);
  }
};
var $a7c324a73303ad55$var$__decorate = function(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var $a7c324a73303ad55$var$defaultEndpoints = {
  connect: "/connect",
  action: "/action"
};
var $a7c324a73303ad55$var$TransportWrapper = class {
  constructor(transport) {
    this._transport = transport;
    this._proxy = new Proxy(this._transport, {
      get: (target, prop, receiver) => {
        if (typeof target[prop] === "function") {
          let errMsg;
          switch (String(prop)) {
            case "initialize":
              errMsg = `Calls to initialize() are disabled and used internally by the RTVIClient`;
              break;
            case "sendReadyMessage":
              errMsg = `Calls to sendReadyMessage() are disabled and used internally by the RTVIClient`;
              break;
            case "connect":
              errMsg = `Calls to connect() are disabled. Please use RTVIClient.connect()`;
              break;
            case "disconnect":
              errMsg = `Calls to disconnect() are disabled. Please use RTVIClient.disconnect()`;
              break;
          }
          if (errMsg) return () => {
            throw new Error(errMsg);
          };
          return (...args) => {
            return target[prop](...args);
          };
        }
        return Reflect.get(target, prop, receiver);
      }
    });
  }
  get proxy() {
    return this._proxy;
  }
};
var $a7c324a73303ad55$var$RTVIEventEmitter = class extends (0, import_events.default) {
};
var $a7c324a73303ad55$export$fa42a01c1d60f4a1 = class extends $a7c324a73303ad55$var$RTVIEventEmitter {
  constructor(options) {
    super();
    this.params = {
      ...options.params,
      endpoints: {
        ...$a7c324a73303ad55$var$defaultEndpoints,
        ...options.params.endpoints ?? {}
      }
    };
    this._helpers = {};
    this._transport = options.transport;
    this._transportWrapper = new $a7c324a73303ad55$var$TransportWrapper(this._transport);
    const wrappedCallbacks = {
      ...options.callbacks,
      onMessageError: (message) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onMessageError) == null ? void 0 : _b.call(_a, message);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).MessageError, message);
      },
      onError: (message) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onError) == null ? void 0 : _b.call(_a, message);
        try {
          this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).Error, message);
        } catch (e) {
          (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("Could not emit error", message);
        }
        const data = message.data;
        if (data == null ? void 0 : data.fatal) {
          (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).error("Fatal error reported. Disconnecting...");
          this.disconnect();
        }
      },
      onConnected: () => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onConnected) == null ? void 0 : _b.call(_a);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).Connected);
      },
      onDisconnected: () => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onDisconnected) == null ? void 0 : _b.call(_a);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).Disconnected);
      },
      onTransportStateChanged: (state) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onTransportStateChanged) == null ? void 0 : _b.call(_a, state);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).TransportStateChanged, state);
      },
      onConfig: (config) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onConfig) == null ? void 0 : _b.call(_a, config);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).Config, config);
      },
      onConfigDescribe: (configDescription) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onConfigDescribe) == null ? void 0 : _b.call(_a, configDescription);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).ConfigDescribe, configDescription);
      },
      onActionsAvailable: (actionsAvailable) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onActionsAvailable) == null ? void 0 : _b.call(_a, actionsAvailable);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).ActionsAvailable, actionsAvailable);
      },
      onParticipantJoined: (p) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onParticipantJoined) == null ? void 0 : _b.call(_a, p);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).ParticipantConnected, p);
      },
      onParticipantLeft: (p) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onParticipantLeft) == null ? void 0 : _b.call(_a, p);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).ParticipantLeft, p);
      },
      onTrackStarted: (track, p) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onTrackStarted) == null ? void 0 : _b.call(_a, track, p);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).TrackStarted, track, p);
      },
      onTrackStopped: (track, p) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onTrackStopped) == null ? void 0 : _b.call(_a, track, p);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).TrackStopped, track, p);
      },
      onScreenTrackStarted: (track, p) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onScreenTrackStarted) == null ? void 0 : _b.call(_a, track, p);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).ScreenTrackStarted, track, p);
      },
      onScreenTrackStopped: (track, p) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onScreenTrackStopped) == null ? void 0 : _b.call(_a, track, p);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).ScreenTrackStopped, track, p);
      },
      onScreenShareError: (errorMessage) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onScreenShareError) == null ? void 0 : _b.call(_a, errorMessage);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).ScreenShareError, errorMessage);
      },
      onAvailableCamsUpdated: (cams) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onAvailableCamsUpdated) == null ? void 0 : _b.call(_a, cams);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).AvailableCamsUpdated, cams);
      },
      onAvailableMicsUpdated: (mics) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onAvailableMicsUpdated) == null ? void 0 : _b.call(_a, mics);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).AvailableMicsUpdated, mics);
      },
      onAvailableSpeakersUpdated: (speakers) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onAvailableSpeakersUpdated) == null ? void 0 : _b.call(_a, speakers);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).AvailableSpeakersUpdated, speakers);
      },
      onCamUpdated: (cam) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onCamUpdated) == null ? void 0 : _b.call(_a, cam);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).CamUpdated, cam);
      },
      onMicUpdated: (mic) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onMicUpdated) == null ? void 0 : _b.call(_a, mic);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).MicUpdated, mic);
      },
      onSpeakerUpdated: (speaker) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onSpeakerUpdated) == null ? void 0 : _b.call(_a, speaker);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).SpeakerUpdated, speaker);
      },
      onBotConnected: (p) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onBotConnected) == null ? void 0 : _b.call(_a, p);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).BotConnected, p);
      },
      onBotReady: (botReadyData) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onBotReady) == null ? void 0 : _b.call(_a, botReadyData);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).BotReady, botReadyData);
      },
      onBotDisconnected: (p) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onBotDisconnected) == null ? void 0 : _b.call(_a, p);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).BotDisconnected, p);
      },
      onBotStartedSpeaking: () => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onBotStartedSpeaking) == null ? void 0 : _b.call(_a);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).BotStartedSpeaking);
      },
      onBotStoppedSpeaking: () => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onBotStoppedSpeaking) == null ? void 0 : _b.call(_a);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).BotStoppedSpeaking);
      },
      onRemoteAudioLevel: (level, p) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onRemoteAudioLevel) == null ? void 0 : _b.call(_a, level, p);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).RemoteAudioLevel, level, p);
      },
      onUserStartedSpeaking: () => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onUserStartedSpeaking) == null ? void 0 : _b.call(_a);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).UserStartedSpeaking);
      },
      onUserStoppedSpeaking: () => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onUserStoppedSpeaking) == null ? void 0 : _b.call(_a);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).UserStoppedSpeaking);
      },
      onLocalAudioLevel: (level) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onLocalAudioLevel) == null ? void 0 : _b.call(_a, level);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).LocalAudioLevel, level);
      },
      onUserTranscript: (data) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onUserTranscript) == null ? void 0 : _b.call(_a, data);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).UserTranscript, data);
      },
      onBotTranscript: (text) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onBotTranscript) == null ? void 0 : _b.call(_a, text);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).BotTranscript, text);
      },
      onBotLlmText: (text) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onBotLlmText) == null ? void 0 : _b.call(_a, text);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).BotLlmText, text);
      },
      onBotLlmStarted: () => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onBotLlmStarted) == null ? void 0 : _b.call(_a);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).BotLlmStarted);
      },
      onBotLlmStopped: () => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onBotLlmStopped) == null ? void 0 : _b.call(_a);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).BotLlmStopped);
      },
      onBotTtsText: (text) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onBotTtsText) == null ? void 0 : _b.call(_a, text);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).BotTtsText, text);
      },
      onBotTtsStarted: () => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onBotTtsStarted) == null ? void 0 : _b.call(_a);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).BotTtsStarted);
      },
      onBotTtsStopped: () => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onBotTtsStopped) == null ? void 0 : _b.call(_a);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).BotTtsStopped);
      },
      onStorageItemStored: (data) => {
        var _a, _b;
        (_b = (_a = options == null ? void 0 : options.callbacks) == null ? void 0 : _a.onStorageItemStored) == null ? void 0 : _b.call(_a, data);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).StorageItemStored, data);
      }
    };
    this._options = {
      ...options,
      callbacks: wrappedCallbacks,
      enableMic: options.enableMic ?? true,
      enableCam: options.enableCam ?? false
    };
    this._initialize();
    (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[RTVI Client] Initialized", this.version);
  }
  constructUrl(endpoint) {
    var _a;
    if (!this.params.baseUrl) throw new $8ead7b33b8402751$export$59b4786f333aac02("Base URL not set. Please set rtviClient.params.baseUrl");
    const baseUrl = this.params.baseUrl.replace(/\/+$/, "");
    return baseUrl + (((_a = this.params.endpoints) == null ? void 0 : _a[endpoint]) ?? "");
  }
  setLogLevel(level) {
    (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).setLevel(level);
  }
  // ------ Transport methods
  /**
   * Initialize local media devices
   */
  async initDevices() {
    (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[RTVI Client] Initializing devices...");
    await this._transport.initDevices();
  }
  /**
   * Connect the voice client session with chosen transport
   * Call async (await) to handle errors
   */
  async connect() {
    if ([
      "authenticating",
      "connecting",
      "connected",
      "ready"
    ].includes(this._transport.state)) throw new $8ead7b33b8402751$export$59b4786f333aac02("Voice client has already been started. Please call disconnect() before starting again.");
    this._abortController = new AbortController();
    return new Promise((resolve, reject) => {
      (async () => {
        var _a, _b, _c;
        this._startResolve = resolve;
        if (this._transport.state === "disconnected") await this._transport.initDevices();
        this._transport.state = "authenticating";
        if (this._options.timeout) this._handshakeTimeout = setTimeout(async () => {
          var _a2;
          (_a2 = this._abortController) == null ? void 0 : _a2.abort();
          await this.disconnect();
          this._transport.state = "error";
          reject(new $8ead7b33b8402751$export$c67992fa684a81a6());
        }, this._options.timeout);
        let authBundle;
        const customConnectHandler = this._options.customConnectHandler;
        (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[RTVI Client] Start params", this.params);
        this.params = {
          ...this.params,
          requestData: {
            ...this.params.requestData,
            rtvi_client_version: this.version
          }
        };
        if (!this.params.baseUrl && !((_a = this.params.endpoints) == null ? void 0 : _a.connect)) {
          (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[RTVI Client] Connecting directly (skipping handshake / auth)...");
          clearTimeout(this._handshakeTimeout);
        } else {
          const connectUrl = this.constructUrl("connect");
          (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[RTVI Client] Connecting...", connectUrl);
          (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[RTVI Client] Start params", this.params);
          try {
            if (customConnectHandler) authBundle = await customConnectHandler(this.params, this._handshakeTimeout, this._abortController);
            else authBundle = await fetch(connectUrl, {
              method: "POST",
              mode: "cors",
              headers: new Headers({
                "Content-Type": "application/json",
                ...Object.fromEntries((this.params.headers ?? new Headers()).entries())
              }),
              body: JSON.stringify({
                config: this.params.config,
                ...this.params.services ? {
                  services: this.params.services
                } : {},
                ...this.params.requestData
              }),
              signal: (_b = this._abortController) == null ? void 0 : _b.signal
            }).then((res) => {
              clearTimeout(this._handshakeTimeout);
              if (res.ok) return res.json();
              return Promise.reject(res);
            });
          } catch (e) {
            clearTimeout(this._handshakeTimeout);
            if ((_c = this._abortController) == null ? void 0 : _c.signal.aborted) return;
            this._transport.state = "error";
            if (e instanceof Response) {
              const errorResp = await e.json();
              reject(new $8ead7b33b8402751$export$e7544ab812238a61(errorResp.info ?? errorResp.detail ?? e.statusText, e.status));
            } else reject(new $8ead7b33b8402751$export$e7544ab812238a61());
            return;
          }
          (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[RTVI Client] Auth bundle received", authBundle);
        }
        try {
          await this._transport.connect(authBundle, this._abortController);
          await this._transport.sendReadyMessage();
        } catch (e) {
          clearTimeout(this._handshakeTimeout);
          this.disconnect();
          reject(e);
          return;
        }
      })();
    });
  }
  /**
   * Disconnect the voice client from the transport
   * Reset / reinitialize transport and abort any pending requests
   */
  async disconnect() {
    if (this._abortController) this._abortController.abort();
    clearTimeout(this._handshakeTimeout);
    await this._transport.disconnect();
    this._messageDispatcher = new (0, $b48f893ed1354c1e$export$e9a960646cc432aa)(this);
  }
  _initialize() {
    this._transport.initialize(this._options, this.handleMessage.bind(this));
    this._messageDispatcher = new (0, $b48f893ed1354c1e$export$e9a960646cc432aa)(this);
  }
  /**
   * Get the current state of the transport
   */
  get connected() {
    return [
      "connected",
      "ready"
    ].includes(this._transport.state);
  }
  get transport() {
    return this._transportWrapper.proxy;
  }
  get state() {
    return this._transport.state;
  }
  get version() {
    return (0, $parcel$interopDefault($e3bad9cc25e327f7$exports)).version;
  }
  // ------ Device methods
  async getAllMics() {
    return await this._transport.getAllMics();
  }
  async getAllCams() {
    return await this._transport.getAllCams();
  }
  async getAllSpeakers() {
    return await this._transport.getAllSpeakers();
  }
  get selectedMic() {
    return this._transport.selectedMic;
  }
  get selectedCam() {
    return this._transport.selectedCam;
  }
  get selectedSpeaker() {
    return this._transport.selectedSpeaker;
  }
  updateMic(micId) {
    this._transport.updateMic(micId);
  }
  updateCam(camId) {
    this._transport.updateCam(camId);
  }
  updateSpeaker(speakerId) {
    this._transport.updateSpeaker(speakerId);
  }
  enableMic(enable) {
    this._transport.enableMic(enable);
  }
  get isMicEnabled() {
    return this._transport.isMicEnabled;
  }
  enableCam(enable) {
    this._transport.enableCam(enable);
  }
  get isCamEnabled() {
    return this._transport.isCamEnabled;
  }
  tracks() {
    return this._transport.tracks();
  }
  enableScreenShare(enable) {
    return this._transport.enableScreenShare(enable);
  }
  get isSharingScreen() {
    return this._transport.isSharingScreen;
  }
  // ------ Config methods
  /**
   * Request the bot to send the current configuration
   * @returns Promise<RTVIClientConfigOption[]> - Promise that resolves with the bot's configuration
   */
  async getConfig() {
    const configMsg = await this._messageDispatcher.dispatch((0, $b48f893ed1354c1e$export$69aa9ab0334b212).getBotConfig());
    return configMsg.data.config;
  }
  /**
   * Update pipeline and services
   * @param config - RTVIClientConfigOption[] partial object with the new configuration
   * @param interrupt - boolean flag to interrupt the current pipeline, or wait until the next turn
   * @returns Promise<RTVIMessage> - Promise that resolves with the updated configuration
   */
  async updateConfig(config, interrupt = false) {
    (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[RTVI Client] Updating config", config);
    return this._messageDispatcher.dispatch((0, $b48f893ed1354c1e$export$69aa9ab0334b212).updateConfig(config, interrupt));
  }
  /**
   * Request bot describe the current configuration options
   * @returns Promise<unknown> - Promise that resolves with the bot's configuration description
   */
  async describeConfig() {
    return this._messageDispatcher.dispatch((0, $b48f893ed1354c1e$export$69aa9ab0334b212).describeConfig());
  }
  /**
   * Returns configuration options for specified service key
   * @param serviceKey - Service name to get options for (e.g. "llm")
   * @param config? - Optional RTVIClientConfigOption[] to query (vs. using remote config)
   * @returns RTVIClientConfigOption | undefined - Configuration options array for the service with specified key or undefined
   */
  async getServiceOptionsFromConfig(serviceKey, config) {
    if (!config && this.state !== "ready") throw new $8ead7b33b8402751$export$885fb96b850e8fbb("getServiceOptionsFromConfig called without config array before bot is ready");
    return Promise.resolve().then(async () => {
      if (!serviceKey) {
        (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("Target service name is required");
        return void 0;
      }
      const passedConfig = config ?? await this.getConfig();
      const configServiceKey = passedConfig.find((config2) => config2.service === serviceKey);
      if (!configServiceKey) {
        (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("No service with name " + serviceKey + " not found in config");
        return void 0;
      }
      return configServiceKey;
    });
  }
  /**
   * Returns configuration option value (unknown) for specified service key and option name
   * @param serviceKey - Service name to get options for (e.g. "llm")
   * @optional option Name of option return from the config (e.g. "model")
   * @returns Promise<unknown | undefined> - Service configuration option value or undefined
   */
  async getServiceOptionValueFromConfig(serviceKey, option, config) {
    const configServiceKey = await this.getServiceOptionsFromConfig(serviceKey, config);
    if (!configServiceKey) {
      (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("Service with name " + serviceKey + " not found in config");
      return void 0;
    }
    const optionValue = configServiceKey.options.find((o) => o.name === option);
    return optionValue ? optionValue.value : void 0;
  }
  _updateOrAddOption(existingOptions, newOption) {
    const existingOptionIndex = existingOptions.findIndex((item) => item.name === newOption.name);
    if (existingOptionIndex !== -1)
      return existingOptions.map((item, index) => index === existingOptionIndex ? {
        ...item,
        value: newOption.value
      } : item);
    else
      return [
        ...existingOptions,
        {
          name: newOption.name,
          value: newOption.value
        }
      ];
  }
  /**
   * Returns config with updated option(s) for specified service key and option name
   * Note: does not update current config, only returns a new object (call updateConfig to apply changes)
   * @param serviceKey - Service name to get options for (e.g. "llm")
   * @param option - Service name to get options for (e.g. "model")
   * @param config - Optional RTVIClientConfigOption[] to update (vs. using current config)
   * @returns Promise<RTVIClientConfigOption[] | undefined> - Configuration options array with updated option(s) or undefined
   */
  async setServiceOptionInConfig(serviceKey, option, config) {
    const newConfig = (0, import_clone_deep.default)(config ?? await this.getConfig());
    const serviceOptions = await this.getServiceOptionsFromConfig(serviceKey, newConfig);
    if (!serviceOptions) {
      (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("Service with name '" + serviceKey + "' not found in config");
      return newConfig;
    }
    const optionsArray = Array.isArray(option) ? option : [
      option
    ];
    for (const opt of optionsArray) {
      const existingItem = newConfig.find((item) => item.service === serviceKey);
      const updatedOptions = existingItem ? this._updateOrAddOption(existingItem.options, opt) : [
        {
          name: opt.name,
          value: opt.value
        }
      ];
      if (existingItem) existingItem.options = updatedOptions;
      else newConfig.push({
        service: serviceKey,
        options: updatedOptions
      });
    }
    return newConfig;
  }
  /**
   * Returns config object with updated properties from passed array.
   * @param configOptions - Array of RTVIClientConfigOption[] to update
   * @param config? - Optional RTVIClientConfigOption[] to update (vs. using current config)
   * @returns Promise<RTVIClientConfigOption[]> - Configuration options
   */
  async setConfigOptions(configOptions, config) {
    let accumulator = (0, import_clone_deep.default)(config ?? await this.getConfig());
    for (const configOption of configOptions) accumulator = await this.setServiceOptionInConfig(configOption.service, configOption.options, accumulator) || accumulator;
    return accumulator;
  }
  // ------ Actions
  /**
   * Dispatch an action message to the bot or http single-turn endpoint
   */
  async action(action) {
    return this._messageDispatcher.dispatchAction(new (0, $b48f893ed1354c1e$export$378529d7a8bead8b)(action), this.handleMessage.bind(this));
  }
  /**
   * Describe available / registered actions the bot has
   * @returns Promise<unknown> - Promise that resolves with the bot's actions
   */
  async describeActions() {
    return this._messageDispatcher.dispatch((0, $b48f893ed1354c1e$export$69aa9ab0334b212).describeActions());
  }
  // ------ Transport methods
  /**
   * Get the session expiry time for the transport session (if applicable)
   * @returns number - Expiry time in milliseconds
   */
  get transportExpiry() {
    return this._transport.expiry;
  }
  // ------ Messages
  /**
   * Directly send a message to the bot via the transport
   * @param message - RTVIMessage object to send
   */
  sendMessage(message) {
    this._transport.sendMessage(message);
  }
  /**
   * Disconnects the bot, but keeps the session alive
   */
  disconnectBot() {
    this._transport.sendMessage(new (0, $b48f893ed1354c1e$export$69aa9ab0334b212)((0, $b48f893ed1354c1e$export$38b3db05cbf0e240).DISCONNECT_BOT, {}));
  }
  handleMessage(ev) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _A, _B, _C, _D, _E, _F, _G, _H, _I, _J, _K, _L, _M, _N, _O, _P, _Q, _R, _S, _T, _U;
    (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug("[RTVI Message]", ev);
    switch (ev.type) {
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).BOT_READY:
        clearTimeout(this._handshakeTimeout);
        (_a = this._startResolve) == null ? void 0 : _a.call(this, ev.data);
        (_c = (_b = this._options.callbacks) == null ? void 0 : _b.onBotReady) == null ? void 0 : _c.call(_b, ev.data);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).CONFIG_AVAILABLE:
        this._messageDispatcher.resolve(ev);
        (_e = (_d = this._options.callbacks) == null ? void 0 : _d.onConfigDescribe) == null ? void 0 : _e.call(_d, ev.data);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).CONFIG: {
        const resp = this._messageDispatcher.resolve(ev);
        (_g = (_f = this._options.callbacks) == null ? void 0 : _f.onConfig) == null ? void 0 : _g.call(_f, resp.data.config);
        break;
      }
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).ACTIONS_AVAILABLE:
        this._messageDispatcher.resolve(ev);
        (_i = (_h = this._options.callbacks) == null ? void 0 : _h.onActionsAvailable) == null ? void 0 : _i.call(_h, ev.data);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).ACTION_RESPONSE:
        this._messageDispatcher.resolve(ev);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).ERROR_RESPONSE: {
        const resp = this._messageDispatcher.reject(ev);
        (_k = (_j = this._options.callbacks) == null ? void 0 : _j.onMessageError) == null ? void 0 : _k.call(_j, resp);
        break;
      }
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).ERROR:
        (_m = (_l = this._options.callbacks) == null ? void 0 : _l.onError) == null ? void 0 : _m.call(_l, ev);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).USER_STARTED_SPEAKING:
        (_o = (_n = this._options.callbacks) == null ? void 0 : _n.onUserStartedSpeaking) == null ? void 0 : _o.call(_n);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).USER_STOPPED_SPEAKING:
        (_q = (_p = this._options.callbacks) == null ? void 0 : _p.onUserStoppedSpeaking) == null ? void 0 : _q.call(_p);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).BOT_STARTED_SPEAKING:
        (_s = (_r = this._options.callbacks) == null ? void 0 : _r.onBotStartedSpeaking) == null ? void 0 : _s.call(_r);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).BOT_STOPPED_SPEAKING:
        (_u = (_t = this._options.callbacks) == null ? void 0 : _t.onBotStoppedSpeaking) == null ? void 0 : _u.call(_t);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).USER_TRANSCRIPTION: {
        const TranscriptData = ev.data;
        (_w = (_v = this._options.callbacks) == null ? void 0 : _v.onUserTranscript) == null ? void 0 : _w.call(_v, TranscriptData);
        break;
      }
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).BOT_TRANSCRIPTION:
        (_y = (_x = this._options.callbacks) == null ? void 0 : _x.onBotTranscript) == null ? void 0 : _y.call(_x, ev.data);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).BOT_LLM_TEXT:
        (_A = (_z = this._options.callbacks) == null ? void 0 : _z.onBotLlmText) == null ? void 0 : _A.call(_z, ev.data);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).BOT_LLM_STARTED:
        (_C = (_B = this._options.callbacks) == null ? void 0 : _B.onBotLlmStarted) == null ? void 0 : _C.call(_B);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).BOT_LLM_STOPPED:
        (_E = (_D = this._options.callbacks) == null ? void 0 : _D.onBotLlmStopped) == null ? void 0 : _E.call(_D);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).BOT_TTS_TEXT:
        (_G = (_F = this._options.callbacks) == null ? void 0 : _F.onBotTtsText) == null ? void 0 : _G.call(_F, ev.data);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).BOT_TTS_STARTED:
        (_I = (_H = this._options.callbacks) == null ? void 0 : _H.onBotTtsStarted) == null ? void 0 : _I.call(_H);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).BOT_TTS_STOPPED:
        (_K = (_J = this._options.callbacks) == null ? void 0 : _J.onBotTtsStopped) == null ? void 0 : _K.call(_J);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).BOT_LLM_SEARCH_RESPONSE:
        (_M = (_L = this._options.callbacks) == null ? void 0 : _L.onBotLlmSearchResponse) == null ? void 0 : _M.call(_L, ev.data);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).BotLlmSearchResponse, ev.data);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).METRICS:
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).Metrics, ev.data);
        (_O = (_N = this._options.callbacks) == null ? void 0 : _N.onMetrics) == null ? void 0 : _O.call(_N, ev.data);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).STORAGE_ITEM_STORED:
        (_Q = (_P = this._options.callbacks) == null ? void 0 : _P.onStorageItemStored) == null ? void 0 : _Q.call(_P, ev.data);
        break;
      case (0, $b48f893ed1354c1e$export$38b3db05cbf0e240).SERVER_MESSAGE:
        (_S = (_R = this._options.callbacks) == null ? void 0 : _R.onServerMessage) == null ? void 0 : _S.call(_R, ev.data);
        this.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).ServerMessage, ev.data);
        break;
      default: {
        let match = false;
        for (const helper of Object.values(this._helpers)) if (helper.getMessageTypes().includes(ev.type)) {
          match = true;
          helper.handleMessage(ev);
        }
        if (!match) (_U = (_T = this._options.callbacks) == null ? void 0 : _T.onGenericMessage) == null ? void 0 : _U.call(_T, ev.data);
      }
    }
  }
  // ------ Helpers
  /**
   * Register a new helper to the client
   * This (optionally) provides a way to reference helpers directly
   * from the client and use the event dispatcher
   * @param service - Target service for this helper
   * @param helper - Helper instance
   * @returns RTVIClientHelper - Registered helper instance
   */
  registerHelper(service, helper) {
    if (this._helpers[service]) throw new Error(`Helper with name '${service}' already registered`);
    if (!(helper instanceof (0, $7614fb2168c523cc$export$23bc637255b2a471))) throw new Error(`Helper must be an instance of RTVIClientHelper`);
    helper.service = service;
    helper.client = this;
    this._helpers[service] = helper;
    return this._helpers[service];
  }
  getHelper(service) {
    const helper = this._helpers[service];
    if (!helper) {
      (0, $7afbbd59ebaa42bf$export$af88d00dbe7f521).debug(`Helper targeting service '${service}' not found`);
      return void 0;
    }
    return helper;
  }
  unregisterHelper(service) {
    if (!this._helpers[service]) return;
    delete this._helpers[service];
  }
};
$a7c324a73303ad55$var$__decorate([
  (0, $16f019d4d16917df$export$f1586721024c4dab)
], $a7c324a73303ad55$export$fa42a01c1d60f4a1.prototype, "getConfig", null);
$a7c324a73303ad55$var$__decorate([
  (0, $16f019d4d16917df$export$f1586721024c4dab)
], $a7c324a73303ad55$export$fa42a01c1d60f4a1.prototype, "updateConfig", null);
$a7c324a73303ad55$var$__decorate([
  (0, $16f019d4d16917df$export$f1586721024c4dab)
], $a7c324a73303ad55$export$fa42a01c1d60f4a1.prototype, "describeConfig", null);
$a7c324a73303ad55$var$__decorate([
  (0, $16f019d4d16917df$export$f1586721024c4dab)
], $a7c324a73303ad55$export$fa42a01c1d60f4a1.prototype, "describeActions", null);
$a7c324a73303ad55$var$__decorate([
  (0, $16f019d4d16917df$export$5c35b4fe6fa8c9a6)("connected", "ready")
], $a7c324a73303ad55$export$fa42a01c1d60f4a1.prototype, "transportExpiry", null);
$a7c324a73303ad55$var$__decorate([
  (0, $16f019d4d16917df$export$f1586721024c4dab)
], $a7c324a73303ad55$export$fa42a01c1d60f4a1.prototype, "sendMessage", null);
$a7c324a73303ad55$var$__decorate([
  (0, $16f019d4d16917df$export$f1586721024c4dab)
], $a7c324a73303ad55$export$fa42a01c1d60f4a1.prototype, "disconnectBot", null);
var $0908f693e3e0724c$exports = {};
$parcel$export($0908f693e3e0724c$exports, "LLMMessageType", () => $0908f693e3e0724c$export$441bcd2e10762760);
$parcel$export($0908f693e3e0724c$exports, "LLMActionType", () => $0908f693e3e0724c$export$43cdfb26f790451);
$parcel$export($0908f693e3e0724c$exports, "LLMHelper", () => $0908f693e3e0724c$export$3cf39a62d076dd5c);
var $0908f693e3e0724c$export$441bcd2e10762760;
(function(LLMMessageType) {
  LLMMessageType["LLM_FUNCTION_CALL"] = "llm-function-call";
  LLMMessageType["LLM_FUNCTION_CALL_START"] = "llm-function-call-start";
  LLMMessageType["LLM_FUNCTION_CALL_RESULT"] = "llm-function-call-result";
  LLMMessageType["LLM_JSON_COMPLETION"] = "llm-json-completion";
})($0908f693e3e0724c$export$441bcd2e10762760 || ($0908f693e3e0724c$export$441bcd2e10762760 = {}));
var $0908f693e3e0724c$export$43cdfb26f790451;
(function(LLMActionType) {
  LLMActionType["APPEND_TO_MESSAGES"] = "append_to_messages";
  LLMActionType["GET_CONTEXT"] = "get_context";
  LLMActionType["SET_CONTEXT"] = "set_context";
  LLMActionType["RUN"] = "run";
})($0908f693e3e0724c$export$43cdfb26f790451 || ($0908f693e3e0724c$export$43cdfb26f790451 = {}));
var $0908f693e3e0724c$export$3cf39a62d076dd5c = class extends (0, $7614fb2168c523cc$export$23bc637255b2a471) {
  constructor(options) {
    super(options);
    this._functionCallCallback = null;
  }
  getMessageTypes() {
    return Object.values($0908f693e3e0724c$export$441bcd2e10762760);
  }
  // --- Actions
  /**
   * Retrieve the bot's current LLM context.
   * @returns Promise<LLMContext>
   */
  async getContext() {
    if (this._client.state !== "ready") throw new $8ead7b33b8402751$export$885fb96b850e8fbb("getContext called while transport not in ready state");
    const actionResponseMsg = await this._client.action({
      service: this._service,
      action: $0908f693e3e0724c$export$43cdfb26f790451.GET_CONTEXT
    });
    return actionResponseMsg.data.result;
  }
  /**
   * Update the bot's LLM context.
   * If this is called while the transport is not in the ready state, the local context will be updated
   * @param context LLMContext - The new context
   * @param interrupt boolean - Whether to interrupt the bot, or wait until it has finished speaking
   * @returns Promise<boolean>
   */
  async setContext(context, interrupt = false) {
    if (this._client.state !== "ready") throw new $8ead7b33b8402751$export$885fb96b850e8fbb("setContext called while transport not in ready state");
    const actionResponse = await this._client.action({
      service: this._service,
      action: $0908f693e3e0724c$export$43cdfb26f790451.SET_CONTEXT,
      arguments: [
        {
          name: "messages",
          value: context.messages
        },
        {
          name: "interrupt",
          value: interrupt
        }
      ]
    });
    return !!actionResponse.data.result;
  }
  /**
   * Append a new message to the LLM context.
   * If this is called while the transport is not in the ready state, the local context will be updated
   * @param context LLMContextMessage
   * @param runImmediately boolean - wait until pipeline is idle before running
   * @returns boolean
   */
  async appendToMessages(message, runImmediately = false) {
    if (this._client.state !== "ready") throw new $8ead7b33b8402751$export$885fb96b850e8fbb("setContext called while transport not in ready state");
    const actionResponse = await this._client.action({
      service: this._service,
      action: $0908f693e3e0724c$export$43cdfb26f790451.APPEND_TO_MESSAGES,
      arguments: [
        {
          name: "messages",
          value: [
            message
          ]
        },
        {
          name: "run_immediately",
          value: runImmediately
        }
      ]
    });
    return !!actionResponse.data.result;
  }
  /**
   * Run the bot's current LLM context.
   * Useful when appending messages to the context without runImmediately set to true.
   * Will do nothing if the bot is not in the ready state.
   * @param interrupt boolean - Whether to interrupt the bot, or wait until it has finished speaking
   * @returns Promise<unknown>
   */
  async run(interrupt = false) {
    if (this._client.state !== "ready") return;
    return this._client.action({
      service: this._service,
      action: $0908f693e3e0724c$export$43cdfb26f790451.RUN,
      arguments: [
        {
          name: "interrupt",
          value: interrupt
        }
      ]
    });
  }
  // --- Handlers
  /**
   * If the LLM wants to call a function, RTVI will invoke the callback defined
   * here. Whatever the callback returns will be sent to the LLM as the function result.
   * @param callback
   * @returns void
   */
  handleFunctionCall(callback) {
    this._functionCallCallback = callback;
  }
  handleMessage(ev) {
    var _a, _b, _c, _d, _e, _f;
    switch (ev.type) {
      case $0908f693e3e0724c$export$441bcd2e10762760.LLM_JSON_COMPLETION:
        (_b = (_a = this._options.callbacks) == null ? void 0 : _a.onLLMJsonCompletion) == null ? void 0 : _b.call(_a, ev.data);
        this._client.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).LLMJsonCompletion, ev.data);
        break;
      case $0908f693e3e0724c$export$441bcd2e10762760.LLM_FUNCTION_CALL: {
        const d = ev.data;
        (_d = (_c = this._options.callbacks) == null ? void 0 : _c.onLLMFunctionCall) == null ? void 0 : _d.call(_c, ev.data);
        this._client.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).LLMFunctionCall, ev.data);
        if (this._functionCallCallback) {
          const fn = {
            functionName: d.function_name,
            arguments: d.args
          };
          if (this._client.state === "ready") this._functionCallCallback(fn).then((result) => {
            this._client.sendMessage(new (0, $b48f893ed1354c1e$export$69aa9ab0334b212)($0908f693e3e0724c$export$441bcd2e10762760.LLM_FUNCTION_CALL_RESULT, {
              function_name: d.function_name,
              tool_call_id: d.tool_call_id,
              arguments: d.args,
              result
            }));
          });
          else throw new $8ead7b33b8402751$export$885fb96b850e8fbb("Attempted to send a function call result from bot while transport not in ready state");
        }
        break;
      }
      case $0908f693e3e0724c$export$441bcd2e10762760.LLM_FUNCTION_CALL_START: {
        const e = ev.data;
        (_f = (_e = this._options.callbacks) == null ? void 0 : _e.onLLMFunctionCallStart) == null ? void 0 : _f.call(_e, e.function_name);
        this._client.emit((0, $f9fc0c57b9aaed9c$export$6b4624d233c61fcb).LLMFunctionCallStart, e.function_name);
        break;
      }
    }
  }
};
var $4086f06442fcb7d7$exports = {};
$parcel$export($4086f06442fcb7d7$exports, "Transport", () => $4086f06442fcb7d7$export$86495b081fef8e52);
var $4086f06442fcb7d7$export$86495b081fef8e52 = class {
  constructor() {
    this._state = "disconnected";
    this._expiry = void 0;
  }
  get expiry() {
    return this._expiry;
  }
};

export {
  $4bb349f22aee5185$export$8728b60ea57bf43e,
  $8ead7b33b8402751$export$59b4786f333aac02,
  $8ead7b33b8402751$export$c67992fa684a81a6,
  $8ead7b33b8402751$export$e7544ab812238a61,
  $8ead7b33b8402751$export$e0624a511a2c4e9,
  $8ead7b33b8402751$export$885fb96b850e8fbb,
  $8ead7b33b8402751$export$4eda4fd287fbbca5,
  $8ead7b33b8402751$export$be839f0100cd3132,
  $f9fc0c57b9aaed9c$export$6b4624d233c61fcb,
  $7614fb2168c523cc$export$23bc637255b2a471,
  $7afbbd59ebaa42bf$export$243e62d78d3b544d,
  $7afbbd59ebaa42bf$export$af88d00dbe7f521,
  $b48f893ed1354c1e$export$882b13c7fda338f5,
  $b48f893ed1354c1e$export$38b3db05cbf0e240,
  $b48f893ed1354c1e$export$69aa9ab0334b212,
  $b48f893ed1354c1e$export$378529d7a8bead8b,
  $b48f893ed1354c1e$export$e9a960646cc432aa,
  $a7c324a73303ad55$export$fa42a01c1d60f4a1,
  $0908f693e3e0724c$export$441bcd2e10762760,
  $0908f693e3e0724c$export$43cdfb26f790451,
  $0908f693e3e0724c$export$3cf39a62d076dd5c,
  $4086f06442fcb7d7$export$86495b081fef8e52
};
/*! Bundled license information:

shallow-clone/index.js:
  (*!
   * shallow-clone <https://github.com/jonschlinkert/shallow-clone>
   *
   * Copyright (c) 2015-present, Jon Schlinkert.
   * Released under the MIT License.
   *)

isobject/index.js:
  (*!
   * isobject <https://github.com/jonschlinkert/isobject>
   *
   * Copyright (c) 2014-2017, Jon Schlinkert.
   * Released under the MIT License.
   *)

is-plain-object/index.js:
  (*!
   * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
   *
   * Copyright (c) 2014-2017, Jon Schlinkert.
   * Released under the MIT License.
   *)
*/
//# sourceMappingURL=chunk-JAMCOKTQ.js.map
