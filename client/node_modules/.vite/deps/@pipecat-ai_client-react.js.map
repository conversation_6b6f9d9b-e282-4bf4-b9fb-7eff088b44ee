{"version": 3, "sources": ["../../jotai/esm/vanilla/internals.mjs", "../../jotai/esm/vanilla.mjs", "../../jotai/esm/react.mjs", "../../jotai/esm/vanilla/utils.mjs", "../../jotai/esm/react/utils.mjs", "../../@pipecat-ai/client-react/dist/client-react/src/index.ts", "../../@pipecat-ai/client-react/dist/client-react/src/RTVIClientAudio.tsx", "../../@pipecat-ai/client-react/dist/client-react/src/useRTVIClientEvent.ts", "../../@pipecat-ai/client-react/dist/client-react/src/useRTVIClient.ts", "../../@pipecat-ai/client-react/dist/client-react/src/RTVIClientProvider.tsx", "../../@pipecat-ai/client-react/dist/client-react/src/useRTVIClientMediaTrack.ts", "../../@pipecat-ai/client-react/dist/client-react/src/RTVIClientVideo.tsx", "../../@pipecat-ai/client-react/dist/client-react/src/useMergedRef.ts", "../../@pipecat-ai/client-react/dist/client-react/src/useRTVIClientMediaDevices.ts", "../../@pipecat-ai/client-react/dist/client-react/src/useRTVIClientTransportState.ts", "../../@pipecat-ai/client-react/dist/client-react/src/VoiceVisualizer.tsx"], "sourcesContent": ["const isSelfAtom = (atom, a) => atom.unstable_is ? atom.unstable_is(a) : a === atom;\nconst hasInitialValue = (atom) => \"init\" in atom;\nconst isActuallyWritableAtom = (atom) => !!atom.write;\nconst isAtomStateInitialized = (atomState) => \"v\" in atomState || \"e\" in atomState;\nconst returnAtomValue = (atomState) => {\n  if (\"e\" in atomState) {\n    throw atomState.e;\n  }\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && !(\"v\" in atomState)) {\n    throw new Error(\"[Bug] atom state is not initialized\");\n  }\n  return atomState.v;\n};\nconst promiseStateMap = /* @__PURE__ */ new WeakMap();\nconst isPendingPromise = (value) => {\n  var _a;\n  return isPromiseLike(value) && !!((_a = promiseStateMap.get(value)) == null ? void 0 : _a[0]);\n};\nconst abortPromise = (promise) => {\n  const promiseState = promiseStateMap.get(promise);\n  if (promiseState == null ? void 0 : promiseState[0]) {\n    promiseState[0] = false;\n    promiseState[1].forEach((fn) => fn());\n  }\n};\nconst registerAbortHandler = (promise, abortHandler) => {\n  let promiseState = promiseStateMap.get(promise);\n  if (!promiseState) {\n    promiseState = [true, /* @__PURE__ */ new Set()];\n    promiseStateMap.set(promise, promiseState);\n    const settle = () => {\n      promiseState[0] = false;\n    };\n    promise.then(settle, settle);\n  }\n  promiseState[1].add(abortHandler);\n};\nconst isPromiseLike = (p) => typeof (p == null ? void 0 : p.then) === \"function\";\nconst addPendingPromiseToDependency = (atom, promise, dependencyAtomState) => {\n  if (!dependencyAtomState.p.has(atom)) {\n    dependencyAtomState.p.add(atom);\n    promise.then(\n      () => {\n        dependencyAtomState.p.delete(atom);\n      },\n      () => {\n        dependencyAtomState.p.delete(atom);\n      }\n    );\n  }\n};\nconst setAtomStateValueOrPromise = (atom, valueOrPromise, ensureAtomState) => {\n  const atomState = ensureAtomState(atom);\n  const hasPrevValue = \"v\" in atomState;\n  const prevValue = atomState.v;\n  if (isPromiseLike(valueOrPromise)) {\n    for (const a of atomState.d.keys()) {\n      addPendingPromiseToDependency(atom, valueOrPromise, ensureAtomState(a));\n    }\n  }\n  atomState.v = valueOrPromise;\n  delete atomState.e;\n  if (!hasPrevValue || !Object.is(prevValue, atomState.v)) {\n    ++atomState.n;\n    if (isPromiseLike(prevValue)) {\n      abortPromise(prevValue);\n    }\n  }\n};\nconst getMountedOrPendingDependents = (atom, atomState, mountedMap) => {\n  var _a;\n  const dependents = /* @__PURE__ */ new Set();\n  for (const a of ((_a = mountedMap.get(atom)) == null ? void 0 : _a.t) || []) {\n    if (mountedMap.has(a)) {\n      dependents.add(a);\n    }\n  }\n  for (const atomWithPendingPromise of atomState.p) {\n    dependents.add(atomWithPendingPromise);\n  }\n  return dependents;\n};\nconst createStoreHook = () => {\n  const callbacks = /* @__PURE__ */ new Set();\n  const notify = () => {\n    callbacks.forEach((fn) => fn());\n  };\n  notify.add = (fn) => {\n    callbacks.add(fn);\n    return () => {\n      callbacks.delete(fn);\n    };\n  };\n  return notify;\n};\nconst createStoreHookForAtoms = () => {\n  const all = {};\n  const callbacks = /* @__PURE__ */ new WeakMap();\n  const notify = (atom) => {\n    var _a, _b;\n    (_a = callbacks.get(all)) == null ? void 0 : _a.forEach((fn) => fn(atom));\n    (_b = callbacks.get(atom)) == null ? void 0 : _b.forEach((fn) => fn());\n  };\n  notify.add = (atom, fn) => {\n    const key = atom || all;\n    const fns = (callbacks.has(key) ? callbacks : callbacks.set(key, /* @__PURE__ */ new Set())).get(key);\n    fns.add(fn);\n    return () => {\n      fns == null ? void 0 : fns.delete(fn);\n      if (!fns.size) {\n        callbacks.delete(key);\n      }\n    };\n  };\n  return notify;\n};\nconst initializeStoreHooks = (storeHooks) => {\n  storeHooks.c || (storeHooks.c = createStoreHookForAtoms());\n  storeHooks.m || (storeHooks.m = createStoreHookForAtoms());\n  storeHooks.u || (storeHooks.u = createStoreHookForAtoms());\n  storeHooks.f || (storeHooks.f = createStoreHook());\n  return storeHooks;\n};\nconst BUILDING_BLOCKS = Symbol();\nconst getBuildingBlocks = (store) => store[BUILDING_BLOCKS];\nconst buildStore = (atomStateMap = /* @__PURE__ */ new WeakMap(), mountedMap = /* @__PURE__ */ new WeakMap(), invalidatedAtoms = /* @__PURE__ */ new WeakMap(), changedAtoms = /* @__PURE__ */ new Set(), mountCallbacks = /* @__PURE__ */ new Set(), unmountCallbacks = /* @__PURE__ */ new Set(), storeHooks = {}, atomRead = (atom, ...params) => atom.read(...params), atomWrite = (atom, ...params) => atom.write(...params), atomOnInit = (atom, store) => {\n  var _a;\n  return (_a = atom.unstable_onInit) == null ? void 0 : _a.call(atom, store);\n}, atomOnMount = (atom, setAtom) => {\n  var _a;\n  return (_a = atom.onMount) == null ? void 0 : _a.call(atom, setAtom);\n}, ...buildingBlockFunctions) => {\n  const ensureAtomState = buildingBlockFunctions[0] || ((atom) => {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && !atom) {\n      throw new Error(\"Atom is undefined or null\");\n    }\n    let atomState = atomStateMap.get(atom);\n    if (!atomState) {\n      atomState = { d: /* @__PURE__ */ new Map(), p: /* @__PURE__ */ new Set(), n: 0 };\n      atomStateMap.set(atom, atomState);\n      atomOnInit == null ? void 0 : atomOnInit(atom, store);\n    }\n    return atomState;\n  });\n  const flushCallbacks = buildingBlockFunctions[1] || (() => {\n    const errors = [];\n    const call = (fn) => {\n      try {\n        fn();\n      } catch (e) {\n        errors.push(e);\n      }\n    };\n    do {\n      if (storeHooks.f) {\n        call(storeHooks.f);\n      }\n      const callbacks = /* @__PURE__ */ new Set();\n      const add = callbacks.add.bind(callbacks);\n      changedAtoms.forEach((atom) => {\n        var _a;\n        return (_a = mountedMap.get(atom)) == null ? void 0 : _a.l.forEach(add);\n      });\n      changedAtoms.clear();\n      unmountCallbacks.forEach(add);\n      unmountCallbacks.clear();\n      mountCallbacks.forEach(add);\n      mountCallbacks.clear();\n      callbacks.forEach(call);\n      if (changedAtoms.size) {\n        recomputeInvalidatedAtoms();\n      }\n    } while (changedAtoms.size || unmountCallbacks.size || mountCallbacks.size);\n    if (errors.length) {\n      throw new AggregateError(errors);\n    }\n  });\n  const recomputeInvalidatedAtoms = buildingBlockFunctions[2] || (() => {\n    const topSortedReversed = [];\n    const visiting = /* @__PURE__ */ new WeakSet();\n    const visited = /* @__PURE__ */ new WeakSet();\n    const stack = Array.from(changedAtoms);\n    while (stack.length) {\n      const a = stack[stack.length - 1];\n      const aState = ensureAtomState(a);\n      if (visited.has(a)) {\n        stack.pop();\n        continue;\n      }\n      if (visiting.has(a)) {\n        if (invalidatedAtoms.get(a) === aState.n) {\n          topSortedReversed.push([a, aState]);\n        } else if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && invalidatedAtoms.has(a)) {\n          throw new Error(\"[Bug] invalidated atom exists\");\n        }\n        visited.add(a);\n        stack.pop();\n        continue;\n      }\n      visiting.add(a);\n      for (const d of getMountedOrPendingDependents(a, aState, mountedMap)) {\n        if (!visiting.has(d)) {\n          stack.push(d);\n        }\n      }\n    }\n    for (let i = topSortedReversed.length - 1; i >= 0; --i) {\n      const [a, aState] = topSortedReversed[i];\n      let hasChangedDeps = false;\n      for (const dep of aState.d.keys()) {\n        if (dep !== a && changedAtoms.has(dep)) {\n          hasChangedDeps = true;\n          break;\n        }\n      }\n      if (hasChangedDeps) {\n        readAtomState(a);\n        mountDependencies(a);\n      }\n      invalidatedAtoms.delete(a);\n    }\n  });\n  const readAtomState = buildingBlockFunctions[3] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    if (isAtomStateInitialized(atomState)) {\n      if (mountedMap.has(atom) && invalidatedAtoms.get(atom) !== atomState.n) {\n        return atomState;\n      }\n      if (Array.from(atomState.d).every(\n        ([a, n]) => (\n          // Recursively, read the atom state of the dependency, and\n          // check if the atom epoch number is unchanged\n          readAtomState(a).n === n\n        )\n      )) {\n        return atomState;\n      }\n    }\n    atomState.d.clear();\n    let isSync = true;\n    const mountDependenciesIfAsync = () => {\n      if (mountedMap.has(atom)) {\n        mountDependencies(atom);\n        recomputeInvalidatedAtoms();\n        flushCallbacks();\n      }\n    };\n    const getter = (a) => {\n      var _a2;\n      if (isSelfAtom(atom, a)) {\n        const aState2 = ensureAtomState(a);\n        if (!isAtomStateInitialized(aState2)) {\n          if (hasInitialValue(a)) {\n            setAtomStateValueOrPromise(a, a.init, ensureAtomState);\n          } else {\n            throw new Error(\"no atom init\");\n          }\n        }\n        return returnAtomValue(aState2);\n      }\n      const aState = readAtomState(a);\n      try {\n        return returnAtomValue(aState);\n      } finally {\n        atomState.d.set(a, aState.n);\n        if (isPendingPromise(atomState.v)) {\n          addPendingPromiseToDependency(atom, atomState.v, aState);\n        }\n        (_a2 = mountedMap.get(a)) == null ? void 0 : _a2.t.add(atom);\n        if (!isSync) {\n          mountDependenciesIfAsync();\n        }\n      }\n    };\n    let controller;\n    let setSelf;\n    const options = {\n      get signal() {\n        if (!controller) {\n          controller = new AbortController();\n        }\n        return controller.signal;\n      },\n      get setSelf() {\n        if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && !isActuallyWritableAtom(atom)) {\n          console.warn(\"setSelf function cannot be used with read-only atom\");\n        }\n        if (!setSelf && isActuallyWritableAtom(atom)) {\n          setSelf = (...args) => {\n            if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && isSync) {\n              console.warn(\"setSelf function cannot be called in sync\");\n            }\n            if (!isSync) {\n              try {\n                return writeAtomState(atom, ...args);\n              } finally {\n                recomputeInvalidatedAtoms();\n                flushCallbacks();\n              }\n            }\n          };\n        }\n        return setSelf;\n      }\n    };\n    const prevEpochNumber = atomState.n;\n    try {\n      const valueOrPromise = atomRead(atom, getter, options);\n      setAtomStateValueOrPromise(atom, valueOrPromise, ensureAtomState);\n      if (isPromiseLike(valueOrPromise)) {\n        registerAbortHandler(valueOrPromise, () => controller == null ? void 0 : controller.abort());\n        valueOrPromise.then(\n          mountDependenciesIfAsync,\n          mountDependenciesIfAsync\n        );\n      }\n      return atomState;\n    } catch (error) {\n      delete atomState.v;\n      atomState.e = error;\n      ++atomState.n;\n      return atomState;\n    } finally {\n      isSync = false;\n      if (prevEpochNumber !== atomState.n && invalidatedAtoms.get(atom) === prevEpochNumber) {\n        invalidatedAtoms.set(atom, atomState.n);\n        changedAtoms.add(atom);\n        (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, atom);\n      }\n    }\n  });\n  const invalidateDependents = buildingBlockFunctions[4] || ((atom) => {\n    const stack = [atom];\n    while (stack.length) {\n      const a = stack.pop();\n      const aState = ensureAtomState(a);\n      for (const d of getMountedOrPendingDependents(a, aState, mountedMap)) {\n        const dState = ensureAtomState(d);\n        invalidatedAtoms.set(d, dState.n);\n        stack.push(d);\n      }\n    }\n  });\n  const writeAtomState = buildingBlockFunctions[5] || ((atom, ...args) => {\n    let isSync = true;\n    const getter = (a) => returnAtomValue(readAtomState(a));\n    const setter = (a, ...args2) => {\n      var _a;\n      const aState = ensureAtomState(a);\n      try {\n        if (isSelfAtom(atom, a)) {\n          if (!hasInitialValue(a)) {\n            throw new Error(\"atom not writable\");\n          }\n          const prevEpochNumber = aState.n;\n          const v = args2[0];\n          setAtomStateValueOrPromise(a, v, ensureAtomState);\n          mountDependencies(a);\n          if (prevEpochNumber !== aState.n) {\n            changedAtoms.add(a);\n            (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, a);\n            invalidateDependents(a);\n          }\n          return void 0;\n        } else {\n          return writeAtomState(a, ...args2);\n        }\n      } finally {\n        if (!isSync) {\n          recomputeInvalidatedAtoms();\n          flushCallbacks();\n        }\n      }\n    };\n    try {\n      return atomWrite(atom, getter, setter, ...args);\n    } finally {\n      isSync = false;\n    }\n  });\n  const mountDependencies = buildingBlockFunctions[6] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    const mounted = mountedMap.get(atom);\n    if (mounted && !isPendingPromise(atomState.v)) {\n      for (const [a, n] of atomState.d) {\n        if (!mounted.d.has(a)) {\n          const aState = ensureAtomState(a);\n          const aMounted = mountAtom(a);\n          aMounted.t.add(atom);\n          mounted.d.add(a);\n          if (n !== aState.n) {\n            changedAtoms.add(a);\n            (_a = storeHooks.c) == null ? void 0 : _a.call(storeHooks, a);\n            invalidateDependents(a);\n          }\n        }\n      }\n      for (const a of mounted.d || []) {\n        if (!atomState.d.has(a)) {\n          mounted.d.delete(a);\n          const aMounted = unmountAtom(a);\n          aMounted == null ? void 0 : aMounted.t.delete(atom);\n        }\n      }\n    }\n  });\n  const mountAtom = buildingBlockFunctions[7] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    let mounted = mountedMap.get(atom);\n    if (!mounted) {\n      readAtomState(atom);\n      for (const a of atomState.d.keys()) {\n        const aMounted = mountAtom(a);\n        aMounted.t.add(atom);\n      }\n      mounted = {\n        l: /* @__PURE__ */ new Set(),\n        d: new Set(atomState.d.keys()),\n        t: /* @__PURE__ */ new Set()\n      };\n      mountedMap.set(atom, mounted);\n      (_a = storeHooks.m) == null ? void 0 : _a.call(storeHooks, atom);\n      if (isActuallyWritableAtom(atom)) {\n        const processOnMount = () => {\n          let isSync = true;\n          const setAtom = (...args) => {\n            try {\n              return writeAtomState(atom, ...args);\n            } finally {\n              if (!isSync) {\n                recomputeInvalidatedAtoms();\n                flushCallbacks();\n              }\n            }\n          };\n          try {\n            const onUnmount = atomOnMount(atom, setAtom);\n            if (onUnmount) {\n              mounted.u = () => {\n                isSync = true;\n                try {\n                  onUnmount();\n                } finally {\n                  isSync = false;\n                }\n              };\n            }\n          } finally {\n            isSync = false;\n          }\n        };\n        mountCallbacks.add(processOnMount);\n      }\n    }\n    return mounted;\n  });\n  const unmountAtom = buildingBlockFunctions[8] || ((atom) => {\n    var _a;\n    const atomState = ensureAtomState(atom);\n    let mounted = mountedMap.get(atom);\n    if (mounted && !mounted.l.size && !Array.from(mounted.t).some((a) => {\n      var _a2;\n      return (_a2 = mountedMap.get(a)) == null ? void 0 : _a2.d.has(atom);\n    })) {\n      if (mounted.u) {\n        unmountCallbacks.add(mounted.u);\n      }\n      mounted = void 0;\n      mountedMap.delete(atom);\n      (_a = storeHooks.u) == null ? void 0 : _a.call(storeHooks, atom);\n      for (const a of atomState.d.keys()) {\n        const aMounted = unmountAtom(a);\n        aMounted == null ? void 0 : aMounted.t.delete(atom);\n      }\n      return void 0;\n    }\n    return mounted;\n  });\n  const buildingBlocks = [\n    // store state\n    atomStateMap,\n    mountedMap,\n    invalidatedAtoms,\n    changedAtoms,\n    mountCallbacks,\n    unmountCallbacks,\n    storeHooks,\n    // atom interceptors\n    atomRead,\n    atomWrite,\n    atomOnInit,\n    atomOnMount,\n    // building-block functions\n    ensureAtomState,\n    flushCallbacks,\n    recomputeInvalidatedAtoms,\n    readAtomState,\n    invalidateDependents,\n    writeAtomState,\n    mountDependencies,\n    mountAtom,\n    unmountAtom\n  ];\n  const store = {\n    get: (atom) => returnAtomValue(readAtomState(atom)),\n    set: (atom, ...args) => {\n      try {\n        return writeAtomState(atom, ...args);\n      } finally {\n        recomputeInvalidatedAtoms();\n        flushCallbacks();\n      }\n    },\n    sub: (atom, listener) => {\n      const mounted = mountAtom(atom);\n      const listeners = mounted.l;\n      listeners.add(listener);\n      flushCallbacks();\n      return () => {\n        listeners.delete(listener);\n        unmountAtom(atom);\n        flushCallbacks();\n      };\n    }\n  };\n  Object.defineProperty(store, BUILDING_BLOCKS, { value: buildingBlocks });\n  return store;\n};\nconst INTERNAL_buildStoreRev1 = buildStore;\nconst INTERNAL_getBuildingBlocksRev1 = getBuildingBlocks;\nconst INTERNAL_initializeStoreHooks = initializeStoreHooks;\nconst INTERNAL_isSelfAtom = isSelfAtom;\nconst INTERNAL_hasInitialValue = hasInitialValue;\nconst INTERNAL_isActuallyWritableAtom = isActuallyWritableAtom;\nconst INTERNAL_isAtomStateInitialized = isAtomStateInitialized;\nconst INTERNAL_returnAtomValue = returnAtomValue;\nconst INTERNAL_promiseStateMap = promiseStateMap;\nconst INTERNAL_isPendingPromise = isPendingPromise;\nconst INTERNAL_abortPromise = abortPromise;\nconst INTERNAL_registerAbortHandler = registerAbortHandler;\nconst INTERNAL_isPromiseLike = isPromiseLike;\nconst INTERNAL_addPendingPromiseToDependency = addPendingPromiseToDependency;\nconst INTERNAL_setAtomStateValueOrPromise = setAtomStateValueOrPromise;\nconst INTERNAL_getMountedOrPendingDependents = getMountedOrPendingDependents;\n\nexport { INTERNAL_abortPromise, INTERNAL_addPendingPromiseToDependency, INTERNAL_buildStoreRev1, INTERNAL_getBuildingBlocksRev1, INTERNAL_getMountedOrPendingDependents, INTERNAL_hasInitialValue, INTERNAL_initializeStoreHooks, INTERNAL_isActuallyWritableAtom, INTERNAL_isAtomStateInitialized, INTERNAL_isPendingPromise, INTERNAL_isPromiseLike, INTERNAL_isSelfAtom, INTERNAL_promiseStateMap, INTERNAL_registerAbortHandler, INTERNAL_returnAtomValue, INTERNAL_setAtomStateValueOrPromise };\n", "import { INTERNAL_buildStoreRev1, INTERNAL_initializeStoreHooks } from 'jotai/vanilla/internals';\n\nlet keyCount = 0;\nfunction atom(read, write) {\n  const key = `atom${++keyCount}`;\n  const config = {\n    toString() {\n      return (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && this.debugLabel ? key + \":\" + this.debugLabel : key;\n    }\n  };\n  if (typeof read === \"function\") {\n    config.read = read;\n  } else {\n    config.init = read;\n    config.read = defaultRead;\n    config.write = defaultWrite;\n  }\n  if (write) {\n    config.write = write;\n  }\n  return config;\n}\nfunction defaultRead(get) {\n  return get(this);\n}\nfunction defaultWrite(get, set, arg) {\n  return set(\n    this,\n    typeof arg === \"function\" ? arg(get(this)) : arg\n  );\n}\n\nconst createDevStoreRev4 = () => {\n  let inRestoreAtom = 0;\n  const storeHooks = INTERNAL_initializeStoreHooks({});\n  const atomStateMap = /* @__PURE__ */ new WeakMap();\n  const mountedAtoms = /* @__PURE__ */ new WeakMap();\n  const store = INTERNAL_buildStoreRev1(\n    atomStateMap,\n    mountedAtoms,\n    void 0,\n    void 0,\n    void 0,\n    void 0,\n    storeHooks,\n    void 0,\n    (atom, get, set, ...args) => {\n      if (inRestoreAtom) {\n        return set(atom, ...args);\n      }\n      return atom.write(get, set, ...args);\n    }\n  );\n  const debugMountedAtoms = /* @__PURE__ */ new Set();\n  storeHooks.m.add(void 0, (atom) => {\n    debugMountedAtoms.add(atom);\n    const atomState = atomStateMap.get(atom);\n    atomState.m = mountedAtoms.get(atom);\n  });\n  storeHooks.u.add(void 0, (atom) => {\n    debugMountedAtoms.delete(atom);\n    const atomState = atomStateMap.get(atom);\n    delete atomState.m;\n  });\n  const devStore = {\n    // store dev methods (these are tentative and subject to change without notice)\n    dev4_get_internal_weak_map: () => {\n      console.log(\"Deprecated: Use devstore from the devtools library\");\n      return atomStateMap;\n    },\n    dev4_get_mounted_atoms: () => debugMountedAtoms,\n    dev4_restore_atoms: (values) => {\n      const restoreAtom = {\n        read: () => null,\n        write: (_get, set) => {\n          ++inRestoreAtom;\n          try {\n            for (const [atom, value] of values) {\n              if (\"init\" in atom) {\n                set(atom, value);\n              }\n            }\n          } finally {\n            --inRestoreAtom;\n          }\n        }\n      };\n      store.set(restoreAtom);\n    }\n  };\n  return Object.assign(store, devStore);\n};\nlet overiddenCreateStore;\nfunction INTERNAL_overrideCreateStore(fn) {\n  overiddenCreateStore = fn(overiddenCreateStore);\n}\nfunction createStore() {\n  if (overiddenCreateStore) {\n    return overiddenCreateStore();\n  }\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    return createDevStoreRev4();\n  }\n  return INTERNAL_buildStoreRev1();\n}\nlet defaultStore;\nfunction getDefaultStore() {\n  if (!defaultStore) {\n    defaultStore = createStore();\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      globalThis.__JOTAI_DEFAULT_STORE__ || (globalThis.__JOTAI_DEFAULT_STORE__ = defaultStore);\n      if (globalThis.__JOTAI_DEFAULT_STORE__ !== defaultStore) {\n        console.warn(\n          \"Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044\"\n        );\n      }\n    }\n  }\n  return defaultStore;\n}\n\nexport { INTERNAL_overrideCreateStore, atom, createStore, getDefaultStore };\n", "'use client';\nimport React, { createContext, useContext, useRef, createElement, useReducer, useEffect, useDebugValue, useCallback } from 'react';\nimport { getDefaultStore, createStore } from 'jotai/vanilla';\nimport { INTERNAL_registerAbortHandler } from 'jotai/vanilla/internals';\n\nconst StoreContext = createContext(\n  void 0\n);\nfunction useStore(options) {\n  const store = useContext(StoreContext);\n  return (options == null ? void 0 : options.store) || store || getDefaultStore();\n}\nfunction Provider({\n  children,\n  store\n}) {\n  const storeRef = useRef(void 0);\n  if (!store && !storeRef.current) {\n    storeRef.current = createStore();\n  }\n  return createElement(\n    StoreContext.Provider,\n    {\n      value: store || storeRef.current\n    },\n    children\n  );\n}\n\nconst isPromiseLike = (x) => typeof (x == null ? void 0 : x.then) === \"function\";\nconst attachPromiseStatus = (promise) => {\n  if (!promise.status) {\n    promise.status = \"pending\";\n    promise.then(\n      (v) => {\n        promise.status = \"fulfilled\";\n        promise.value = v;\n      },\n      (e) => {\n        promise.status = \"rejected\";\n        promise.reason = e;\n      }\n    );\n  }\n};\nconst use = React.use || // A shim for older React versions\n((promise) => {\n  if (promise.status === \"pending\") {\n    throw promise;\n  } else if (promise.status === \"fulfilled\") {\n    return promise.value;\n  } else if (promise.status === \"rejected\") {\n    throw promise.reason;\n  } else {\n    attachPromiseStatus(promise);\n    throw promise;\n  }\n});\nconst continuablePromiseMap = /* @__PURE__ */ new WeakMap();\nconst createContinuablePromise = (promise, getValue) => {\n  let continuablePromise = continuablePromiseMap.get(promise);\n  if (!continuablePromise) {\n    continuablePromise = new Promise((resolve, reject) => {\n      let curr = promise;\n      const onFulfilled = (me) => (v) => {\n        if (curr === me) {\n          resolve(v);\n        }\n      };\n      const onRejected = (me) => (e) => {\n        if (curr === me) {\n          reject(e);\n        }\n      };\n      const onAbort = () => {\n        try {\n          const nextValue = getValue();\n          if (isPromiseLike(nextValue)) {\n            continuablePromiseMap.set(nextValue, continuablePromise);\n            curr = nextValue;\n            nextValue.then(onFulfilled(nextValue), onRejected(nextValue));\n            INTERNAL_registerAbortHandler(nextValue, onAbort);\n          } else {\n            resolve(nextValue);\n          }\n        } catch (e) {\n          reject(e);\n        }\n      };\n      promise.then(onFulfilled(promise), onRejected(promise));\n      INTERNAL_registerAbortHandler(promise, onAbort);\n    });\n    continuablePromiseMap.set(promise, continuablePromise);\n  }\n  return continuablePromise;\n};\nfunction useAtomValue(atom, options) {\n  const { delay, unstable_promiseStatus: promiseStatus = !React.use } = options || {};\n  const store = useStore(options);\n  const [[valueFromReducer, storeFromReducer, atomFromReducer], rerender] = useReducer(\n    (prev) => {\n      const nextValue = store.get(atom);\n      if (Object.is(prev[0], nextValue) && prev[1] === store && prev[2] === atom) {\n        return prev;\n      }\n      return [nextValue, store, atom];\n    },\n    void 0,\n    () => [store.get(atom), store, atom]\n  );\n  let value = valueFromReducer;\n  if (storeFromReducer !== store || atomFromReducer !== atom) {\n    rerender();\n    value = store.get(atom);\n  }\n  useEffect(() => {\n    const unsub = store.sub(atom, () => {\n      if (promiseStatus) {\n        try {\n          const value2 = store.get(atom);\n          if (isPromiseLike(value2)) {\n            attachPromiseStatus(\n              createContinuablePromise(value2, () => store.get(atom))\n            );\n          }\n        } catch (e) {\n        }\n      }\n      if (typeof delay === \"number\") {\n        setTimeout(rerender, delay);\n        return;\n      }\n      rerender();\n    });\n    rerender();\n    return unsub;\n  }, [store, atom, delay, promiseStatus]);\n  useDebugValue(value);\n  if (isPromiseLike(value)) {\n    const promise = createContinuablePromise(value, () => store.get(atom));\n    if (promiseStatus) {\n      attachPromiseStatus(promise);\n    }\n    return use(promise);\n  }\n  return value;\n}\n\nfunction useSetAtom(atom, options) {\n  const store = useStore(options);\n  const setAtom = useCallback(\n    (...args) => {\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && !(\"write\" in atom)) {\n        throw new Error(\"not writable atom\");\n      }\n      return store.set(atom, ...args);\n    },\n    [store, atom]\n  );\n  return setAtom;\n}\n\nfunction useAtom(atom, options) {\n  return [\n    useAtomValue(atom, options),\n    // We do wrong type assertion here, which results in throwing an error.\n    useSetAtom(atom, options)\n  ];\n}\n\nexport { Provider, useAtom, useAtomValue, useSetAtom, useStore };\n", "import { atom } from 'jotai/vanilla';\n\nconst RESET = Symbol(\n  (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" ? \"RESET\" : \"\"\n);\n\nfunction atomWithReset(initialValue) {\n  const anAtom = atom(\n    initialValue,\n    (get, set, update) => {\n      const nextValue = typeof update === \"function\" ? update(get(anAtom)) : update;\n      set(anAtom, nextValue === RESET ? initialValue : nextValue);\n    }\n  );\n  return anAtom;\n}\n\nfunction atomWithReducer(initialValue, reducer) {\n  return atom(initialValue, function(get, set, action) {\n    set(this, reducer(get(this), action));\n  });\n}\n\nfunction atomFamily(initializeAtom, areEqual) {\n  let shouldRemove = null;\n  const atoms = /* @__PURE__ */ new Map();\n  const listeners = /* @__PURE__ */ new Set();\n  const createAtom = (param) => {\n    let item;\n    if (areEqual === void 0) {\n      item = atoms.get(param);\n    } else {\n      for (const [key, value] of atoms) {\n        if (areEqual(key, param)) {\n          item = value;\n          break;\n        }\n      }\n    }\n    if (item !== void 0) {\n      if (shouldRemove == null ? void 0 : shouldRemove(item[1], param)) {\n        createAtom.remove(param);\n      } else {\n        return item[0];\n      }\n    }\n    const newAtom = initializeAtom(param);\n    atoms.set(param, [newAtom, Date.now()]);\n    notifyListeners(\"CREATE\", param, newAtom);\n    return newAtom;\n  };\n  const notifyListeners = (type, param, atom) => {\n    for (const listener of listeners) {\n      listener({ type, param, atom });\n    }\n  };\n  createAtom.unstable_listen = (callback) => {\n    listeners.add(callback);\n    return () => {\n      listeners.delete(callback);\n    };\n  };\n  createAtom.getParams = () => atoms.keys();\n  createAtom.remove = (param) => {\n    if (areEqual === void 0) {\n      if (!atoms.has(param)) return;\n      const [atom] = atoms.get(param);\n      atoms.delete(param);\n      notifyListeners(\"REMOVE\", param, atom);\n    } else {\n      for (const [key, [atom]] of atoms) {\n        if (areEqual(key, param)) {\n          atoms.delete(key);\n          notifyListeners(\"REMOVE\", key, atom);\n          break;\n        }\n      }\n    }\n  };\n  createAtom.setShouldRemove = (fn) => {\n    shouldRemove = fn;\n    if (!shouldRemove) return;\n    for (const [key, [atom, createdAt]] of atoms) {\n      if (shouldRemove(createdAt, key)) {\n        atoms.delete(key);\n        notifyListeners(\"REMOVE\", key, atom);\n      }\n    }\n  };\n  return createAtom;\n}\n\nconst getCached$2 = (c, m, k) => (m.has(k) ? m : m.set(k, c())).get(k);\nconst cache1$3 = /* @__PURE__ */ new WeakMap();\nconst memo3 = (create, dep1, dep2, dep3) => {\n  const cache2 = getCached$2(() => /* @__PURE__ */ new WeakMap(), cache1$3, dep1);\n  const cache3 = getCached$2(() => /* @__PURE__ */ new WeakMap(), cache2, dep2);\n  return getCached$2(create, cache3, dep3);\n};\nfunction selectAtom(anAtom, selector, equalityFn = Object.is) {\n  return memo3(\n    () => {\n      const EMPTY = Symbol();\n      const selectValue = ([value, prevSlice]) => {\n        if (prevSlice === EMPTY) {\n          return selector(value);\n        }\n        const slice = selector(value, prevSlice);\n        return equalityFn(prevSlice, slice) ? prevSlice : slice;\n      };\n      const derivedAtom = atom((get) => {\n        const prev = get(derivedAtom);\n        const value = get(anAtom);\n        return selectValue([value, prev]);\n      });\n      derivedAtom.init = EMPTY;\n      return derivedAtom;\n    },\n    anAtom,\n    selector,\n    equalityFn\n  );\n}\n\nconst frozenAtoms = /* @__PURE__ */ new WeakSet();\nconst deepFreeze = (value) => {\n  if (typeof value !== \"object\" || value === null) {\n    return value;\n  }\n  Object.freeze(value);\n  const propNames = Object.getOwnPropertyNames(value);\n  for (const name of propNames) {\n    deepFreeze(value[name]);\n  }\n  return value;\n};\nfunction freezeAtom(anAtom) {\n  if (frozenAtoms.has(anAtom)) {\n    return anAtom;\n  }\n  frozenAtoms.add(anAtom);\n  const origRead = anAtom.read;\n  anAtom.read = function(get, options) {\n    return deepFreeze(origRead.call(this, get, options));\n  };\n  if (\"write\" in anAtom) {\n    const origWrite = anAtom.write;\n    anAtom.write = function(get, set, ...args) {\n      return origWrite.call(\n        this,\n        get,\n        (...setArgs) => {\n          if (setArgs[0] === anAtom) {\n            setArgs[1] = deepFreeze(setArgs[1]);\n          }\n          return set(...setArgs);\n        },\n        ...args\n      );\n    };\n  }\n  return anAtom;\n}\nfunction freezeAtomCreator(createAtom) {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] freezeAtomCreator is deprecated, define it on users end\"\n    );\n  }\n  return (...args) => freezeAtom(createAtom(...args));\n}\n\nconst getCached$1 = (c, m, k) => (m.has(k) ? m : m.set(k, c())).get(k);\nconst cache1$2 = /* @__PURE__ */ new WeakMap();\nconst memo2$1 = (create, dep1, dep2) => {\n  const cache2 = getCached$1(() => /* @__PURE__ */ new WeakMap(), cache1$2, dep1);\n  return getCached$1(create, cache2, dep2);\n};\nconst cacheKeyForEmptyKeyExtractor = {};\nconst isWritable = (atom2) => !!atom2.write;\nconst isFunction = (x) => typeof x === \"function\";\nfunction splitAtom(arrAtom, keyExtractor) {\n  return memo2$1(\n    () => {\n      const mappingCache = /* @__PURE__ */ new WeakMap();\n      const getMapping = (arr, prev) => {\n        let mapping = mappingCache.get(arr);\n        if (mapping) {\n          return mapping;\n        }\n        const prevMapping = prev && mappingCache.get(prev);\n        const atomList = [];\n        const keyList = [];\n        arr.forEach((item, index) => {\n          const key = keyExtractor ? keyExtractor(item) : index;\n          keyList[index] = key;\n          const cachedAtom = prevMapping && prevMapping.atomList[prevMapping.keyList.indexOf(key)];\n          if (cachedAtom) {\n            atomList[index] = cachedAtom;\n            return;\n          }\n          const read = (get) => {\n            const prev2 = get(mappingAtom);\n            const currArr = get(arrAtom);\n            const mapping2 = getMapping(currArr, prev2 == null ? void 0 : prev2.arr);\n            const index2 = mapping2.keyList.indexOf(key);\n            if (index2 < 0 || index2 >= currArr.length) {\n              const prevItem = arr[getMapping(arr).keyList.indexOf(key)];\n              if (prevItem) {\n                return prevItem;\n              }\n              throw new Error(\"splitAtom: index out of bounds for read\");\n            }\n            return currArr[index2];\n          };\n          const write = (get, set, update) => {\n            const prev2 = get(mappingAtom);\n            const arr2 = get(arrAtom);\n            const mapping2 = getMapping(arr2, prev2 == null ? void 0 : prev2.arr);\n            const index2 = mapping2.keyList.indexOf(key);\n            if (index2 < 0 || index2 >= arr2.length) {\n              throw new Error(\"splitAtom: index out of bounds for write\");\n            }\n            const nextItem = isFunction(update) ? update(arr2[index2]) : update;\n            if (!Object.is(arr2[index2], nextItem)) {\n              set(arrAtom, [\n                ...arr2.slice(0, index2),\n                nextItem,\n                ...arr2.slice(index2 + 1)\n              ]);\n            }\n          };\n          atomList[index] = isWritable(arrAtom) ? atom(read, write) : atom(read);\n        });\n        if (prevMapping && prevMapping.keyList.length === keyList.length && prevMapping.keyList.every((x, i) => x === keyList[i])) {\n          mapping = prevMapping;\n        } else {\n          mapping = { arr, atomList, keyList };\n        }\n        mappingCache.set(arr, mapping);\n        return mapping;\n      };\n      const mappingAtom = atom((get) => {\n        const prev = get(mappingAtom);\n        const arr = get(arrAtom);\n        const mapping = getMapping(arr, prev == null ? void 0 : prev.arr);\n        return mapping;\n      });\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n        mappingAtom.debugPrivate = true;\n      }\n      mappingAtom.init = void 0;\n      const splittedAtom = isWritable(arrAtom) ? atom(\n        (get) => get(mappingAtom).atomList,\n        (get, set, action) => {\n          switch (action.type) {\n            case \"remove\": {\n              const index = get(splittedAtom).indexOf(action.atom);\n              if (index >= 0) {\n                const arr = get(arrAtom);\n                set(arrAtom, [\n                  ...arr.slice(0, index),\n                  ...arr.slice(index + 1)\n                ]);\n              }\n              break;\n            }\n            case \"insert\": {\n              const index = action.before ? get(splittedAtom).indexOf(action.before) : get(splittedAtom).length;\n              if (index >= 0) {\n                const arr = get(arrAtom);\n                set(arrAtom, [\n                  ...arr.slice(0, index),\n                  action.value,\n                  ...arr.slice(index)\n                ]);\n              }\n              break;\n            }\n            case \"move\": {\n              const index1 = get(splittedAtom).indexOf(action.atom);\n              const index2 = action.before ? get(splittedAtom).indexOf(action.before) : get(splittedAtom).length;\n              if (index1 >= 0 && index2 >= 0) {\n                const arr = get(arrAtom);\n                if (index1 < index2) {\n                  set(arrAtom, [\n                    ...arr.slice(0, index1),\n                    ...arr.slice(index1 + 1, index2),\n                    arr[index1],\n                    ...arr.slice(index2)\n                  ]);\n                } else {\n                  set(arrAtom, [\n                    ...arr.slice(0, index2),\n                    arr[index1],\n                    ...arr.slice(index2, index1),\n                    ...arr.slice(index1 + 1)\n                  ]);\n                }\n              }\n              break;\n            }\n          }\n        }\n      ) : atom((get) => get(mappingAtom).atomList);\n      return splittedAtom;\n    },\n    arrAtom,\n    keyExtractor || cacheKeyForEmptyKeyExtractor\n  );\n}\n\nfunction atomWithDefault(getDefault) {\n  const EMPTY = Symbol();\n  const overwrittenAtom = atom(EMPTY);\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    overwrittenAtom.debugPrivate = true;\n  }\n  const anAtom = atom(\n    (get, options) => {\n      const overwritten = get(overwrittenAtom);\n      if (overwritten !== EMPTY) {\n        return overwritten;\n      }\n      return getDefault(get, options);\n    },\n    (get, set, update) => {\n      if (update === RESET) {\n        set(overwrittenAtom, EMPTY);\n      } else if (typeof update === \"function\") {\n        const prevValue = get(anAtom);\n        set(overwrittenAtom, update(prevValue));\n      } else {\n        set(overwrittenAtom, update);\n      }\n    }\n  );\n  return anAtom;\n}\n\nconst isPromiseLike$3 = (x) => typeof (x == null ? void 0 : x.then) === \"function\";\nfunction withStorageValidator(validator) {\n  return (unknownStorage) => {\n    const storage = {\n      ...unknownStorage,\n      getItem: (key, initialValue) => {\n        const validate = (value2) => {\n          if (!validator(value2)) {\n            return initialValue;\n          }\n          return value2;\n        };\n        const value = unknownStorage.getItem(key, initialValue);\n        if (isPromiseLike$3(value)) {\n          return value.then(validate);\n        }\n        return validate(value);\n      }\n    };\n    return storage;\n  };\n}\nfunction createJSONStorage(getStringStorage = () => {\n  try {\n    return window.localStorage;\n  } catch (e) {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      if (typeof window !== \"undefined\") {\n        console.warn(e);\n      }\n    }\n    return void 0;\n  }\n}, options) {\n  var _a;\n  let lastStr;\n  let lastValue;\n  const storage = {\n    getItem: (key, initialValue) => {\n      var _a2, _b;\n      const parse = (str2) => {\n        str2 = str2 || \"\";\n        if (lastStr !== str2) {\n          try {\n            lastValue = JSON.parse(str2, options == null ? void 0 : options.reviver);\n          } catch (e) {\n            return initialValue;\n          }\n          lastStr = str2;\n        }\n        return lastValue;\n      };\n      const str = (_b = (_a2 = getStringStorage()) == null ? void 0 : _a2.getItem(key)) != null ? _b : null;\n      if (isPromiseLike$3(str)) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (key, newValue) => {\n      var _a2;\n      return (_a2 = getStringStorage()) == null ? void 0 : _a2.setItem(\n        key,\n        JSON.stringify(newValue, options == null ? void 0 : options.replacer)\n      );\n    },\n    removeItem: (key) => {\n      var _a2;\n      return (_a2 = getStringStorage()) == null ? void 0 : _a2.removeItem(key);\n    }\n  };\n  const createHandleSubscribe = (subscriber2) => (key, callback, initialValue) => subscriber2(key, (v) => {\n    let newValue;\n    try {\n      newValue = JSON.parse(v || \"\");\n    } catch (e) {\n      newValue = initialValue;\n    }\n    callback(newValue);\n  });\n  let subscriber;\n  try {\n    subscriber = (_a = getStringStorage()) == null ? void 0 : _a.subscribe;\n  } catch (e) {\n  }\n  if (!subscriber && typeof window !== \"undefined\" && typeof window.addEventListener === \"function\" && window.Storage) {\n    subscriber = (key, callback) => {\n      if (!(getStringStorage() instanceof window.Storage)) {\n        return () => {\n        };\n      }\n      const storageEventCallback = (e) => {\n        if (e.storageArea === getStringStorage() && e.key === key) {\n          callback(e.newValue);\n        }\n      };\n      window.addEventListener(\"storage\", storageEventCallback);\n      return () => {\n        window.removeEventListener(\"storage\", storageEventCallback);\n      };\n    };\n  }\n  if (subscriber) {\n    storage.subscribe = createHandleSubscribe(subscriber);\n  }\n  return storage;\n}\nconst defaultStorage = createJSONStorage();\nfunction atomWithStorage(key, initialValue, storage = defaultStorage, options) {\n  const getOnInit = options == null ? void 0 : options.getOnInit;\n  const baseAtom = atom(\n    getOnInit ? storage.getItem(key, initialValue) : initialValue\n  );\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    baseAtom.debugPrivate = true;\n  }\n  baseAtom.onMount = (setAtom) => {\n    setAtom(storage.getItem(key, initialValue));\n    let unsub;\n    if (storage.subscribe) {\n      unsub = storage.subscribe(key, setAtom, initialValue);\n    }\n    return unsub;\n  };\n  const anAtom = atom(\n    (get) => get(baseAtom),\n    (get, set, update) => {\n      const nextValue = typeof update === \"function\" ? update(get(baseAtom)) : update;\n      if (nextValue === RESET) {\n        set(baseAtom, initialValue);\n        return storage.removeItem(key);\n      }\n      if (isPromiseLike$3(nextValue)) {\n        return nextValue.then((resolvedValue) => {\n          set(baseAtom, resolvedValue);\n          return storage.setItem(key, resolvedValue);\n        });\n      }\n      set(baseAtom, nextValue);\n      return storage.setItem(key, nextValue);\n    }\n  );\n  return anAtom;\n}\n\nconst isPromiseLike$2 = (x) => typeof (x == null ? void 0 : x.then) === \"function\";\nfunction atomWithObservable(getObservable, options) {\n  const returnResultData = (result) => {\n    if (\"e\" in result) {\n      throw result.e;\n    }\n    return result.d;\n  };\n  const observableResultAtom = atom((get) => {\n    var _a;\n    let observable = getObservable(get);\n    const itself = (_a = observable[Symbol.observable]) == null ? void 0 : _a.call(observable);\n    if (itself) {\n      observable = itself;\n    }\n    let resolve;\n    const makePending = () => new Promise((r) => {\n      resolve = r;\n    });\n    const initialResult = options && \"initialValue\" in options ? {\n      d: typeof options.initialValue === \"function\" ? options.initialValue() : options.initialValue\n    } : makePending();\n    let setResult;\n    let lastResult;\n    const listener = (result) => {\n      lastResult = result;\n      resolve == null ? void 0 : resolve(result);\n      setResult == null ? void 0 : setResult(result);\n    };\n    let subscription;\n    let timer;\n    const isNotMounted = () => !setResult;\n    const unsubscribe = () => {\n      if (subscription) {\n        subscription.unsubscribe();\n        subscription = void 0;\n      }\n    };\n    const start = () => {\n      if (subscription) {\n        clearTimeout(timer);\n        subscription.unsubscribe();\n      }\n      subscription = observable.subscribe({\n        next: (d) => listener({ d }),\n        error: (e) => listener({ e }),\n        complete: () => {\n        }\n      });\n      if (isNotMounted() && (options == null ? void 0 : options.unstable_timeout)) {\n        timer = setTimeout(unsubscribe, options.unstable_timeout);\n      }\n    };\n    start();\n    const resultAtom = atom(lastResult || initialResult);\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      resultAtom.debugPrivate = true;\n    }\n    resultAtom.onMount = (update) => {\n      setResult = update;\n      if (lastResult) {\n        update(lastResult);\n      }\n      if (subscription) {\n        clearTimeout(timer);\n      } else {\n        start();\n      }\n      return () => {\n        setResult = void 0;\n        if (options == null ? void 0 : options.unstable_timeout) {\n          timer = setTimeout(unsubscribe, options.unstable_timeout);\n        } else {\n          unsubscribe();\n        }\n      };\n    };\n    return [resultAtom, observable, makePending, start, isNotMounted];\n  });\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    observableResultAtom.debugPrivate = true;\n  }\n  const observableAtom = atom(\n    (get) => {\n      const [resultAtom] = get(observableResultAtom);\n      const result = get(resultAtom);\n      if (isPromiseLike$2(result)) {\n        return result.then(returnResultData);\n      }\n      return returnResultData(result);\n    },\n    (get, set, data) => {\n      const [resultAtom, observable, makePending, start, isNotMounted] = get(observableResultAtom);\n      if (\"next\" in observable) {\n        if (isNotMounted()) {\n          set(resultAtom, makePending());\n          start();\n        }\n        observable.next(data);\n      } else {\n        throw new Error(\"observable is not subject\");\n      }\n    }\n  );\n  return observableAtom;\n}\n\nconst cache1$1 = /* @__PURE__ */ new WeakMap();\nconst memo1 = (create, dep1) => (cache1$1.has(dep1) ? cache1$1 : cache1$1.set(dep1, create())).get(dep1);\nconst isPromiseLike$1 = (p) => typeof (p == null ? void 0 : p.then) === \"function\";\nconst LOADING = { state: \"loading\" };\nfunction loadable(anAtom) {\n  return memo1(() => {\n    const loadableCache = /* @__PURE__ */ new WeakMap();\n    const refreshAtom = atom(0);\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      refreshAtom.debugPrivate = true;\n    }\n    const derivedAtom = atom(\n      (get, { setSelf }) => {\n        get(refreshAtom);\n        let value;\n        try {\n          value = get(anAtom);\n        } catch (error) {\n          return { state: \"hasError\", error };\n        }\n        if (!isPromiseLike$1(value)) {\n          return { state: \"hasData\", data: value };\n        }\n        const promise = value;\n        const cached1 = loadableCache.get(promise);\n        if (cached1) {\n          return cached1;\n        }\n        promise.then(\n          (data) => {\n            loadableCache.set(promise, { state: \"hasData\", data });\n            setSelf();\n          },\n          (error) => {\n            loadableCache.set(promise, { state: \"hasError\", error });\n            setSelf();\n          }\n        );\n        const cached2 = loadableCache.get(promise);\n        if (cached2) {\n          return cached2;\n        }\n        loadableCache.set(promise, LOADING);\n        return LOADING;\n      },\n      (_get, set) => {\n        set(refreshAtom, (c) => c + 1);\n      }\n    );\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      derivedAtom.debugPrivate = true;\n    }\n    return atom((get) => get(derivedAtom));\n  }, anAtom);\n}\n\nconst getCached = (c, m, k) => (m.has(k) ? m : m.set(k, c())).get(k);\nconst cache1 = /* @__PURE__ */ new WeakMap();\nconst memo2 = (create, dep1, dep2) => {\n  const cache2 = getCached(() => /* @__PURE__ */ new WeakMap(), cache1, dep1);\n  return getCached(create, cache2, dep2);\n};\nconst isPromiseLike = (p) => typeof (p == null ? void 0 : p.then) === \"function\";\nconst defaultFallback = () => void 0;\nfunction unwrap(anAtom, fallback = defaultFallback) {\n  return memo2(\n    () => {\n      const promiseErrorCache = /* @__PURE__ */ new WeakMap();\n      const promiseResultCache = /* @__PURE__ */ new WeakMap();\n      const refreshAtom = atom(0);\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n        refreshAtom.debugPrivate = true;\n      }\n      const promiseAndValueAtom = atom(\n        (get, { setSelf }) => {\n          get(refreshAtom);\n          const prev = get(promiseAndValueAtom);\n          const promise = get(anAtom);\n          if (!isPromiseLike(promise)) {\n            return { v: promise };\n          }\n          if (promise !== (prev == null ? void 0 : prev.p)) {\n            promise.then(\n              (v) => {\n                promiseResultCache.set(promise, v);\n                setSelf();\n              },\n              (e) => {\n                promiseErrorCache.set(promise, e);\n                setSelf();\n              }\n            );\n          }\n          if (promiseErrorCache.has(promise)) {\n            throw promiseErrorCache.get(promise);\n          }\n          if (promiseResultCache.has(promise)) {\n            return {\n              p: promise,\n              v: promiseResultCache.get(promise)\n            };\n          }\n          if (prev && \"v\" in prev) {\n            return { p: promise, f: fallback(prev.v), v: prev.v };\n          }\n          return { p: promise, f: fallback() };\n        },\n        (_get, set) => {\n          set(refreshAtom, (c) => c + 1);\n        }\n      );\n      promiseAndValueAtom.init = void 0;\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n        promiseAndValueAtom.debugPrivate = true;\n      }\n      return atom(\n        (get) => {\n          const state = get(promiseAndValueAtom);\n          if (\"f\" in state) {\n            return state.f;\n          }\n          return state.v;\n        },\n        (_get, set, ...args) => set(anAtom, ...args)\n      );\n    },\n    anAtom,\n    fallback\n  );\n}\n\nfunction atomWithRefresh(read, write) {\n  const refreshAtom = atom(0);\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    refreshAtom.debugPrivate = true;\n  }\n  return atom(\n    (get, options) => {\n      get(refreshAtom);\n      return read(get, options);\n    },\n    (get, set, ...args) => {\n      if (args.length === 0) {\n        set(refreshAtom, (c) => c + 1);\n      } else if (write) {\n        return write(get, set, ...args);\n      } else if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n        throw new Error(\"refresh must be called without arguments\");\n      }\n    }\n  );\n}\n\nfunction atomWithLazy(makeInitial) {\n  const a = atom(void 0);\n  delete a.init;\n  Object.defineProperty(a, \"init\", {\n    get() {\n      return makeInitial();\n    }\n  });\n  return a;\n}\n\nexport { RESET, atomFamily, atomWithDefault, atomWithLazy, atomWithObservable, atomWithReducer, atomWithRefresh, atomWithReset, atomWithStorage, createJSONStorage, freezeAtom, freezeAtomCreator, loadable, selectAtom, splitAtom, withStorageValidator as unstable_withStorageValidator, unwrap };\n", "'use client';\nimport { useCallback, useMemo } from 'react';\nimport { useSet<PERSON>tom, useAtom, useStore } from 'jotai/react';\nimport { RESET } from 'jotai/vanilla/utils';\nimport { atom } from 'jotai/vanilla';\n\nfunction useResetAtom(anAtom, options) {\n  const setAtom = useSetAtom(anAtom, options);\n  const resetAtom = useCallback(() => setAtom(RESET), [setAtom]);\n  return resetAtom;\n}\n\nfunction useReducerAtom(anAtom, reducer, options) {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] useReducerAtom is deprecated and will be removed in the future. Please create your own version using the recipe. https://github.com/pmndrs/jotai/pull/2467\"\n    );\n  }\n  const [state, setState] = useAtom(anAtom, options);\n  const dispatch = useCallback(\n    (action) => {\n      setState((prev) => reducer(prev, action));\n    },\n    [setState, reducer]\n  );\n  return [state, dispatch];\n}\n\nfunction useAtomCallback(callback, options) {\n  const anAtom = useMemo(\n    () => atom(null, (get, set, ...args) => callback(get, set, ...args)),\n    [callback]\n  );\n  return useSetAtom(anAtom, options);\n}\n\nconst hydratedMap = /* @__PURE__ */ new WeakMap();\nfunction useHydrateAtoms(values, options) {\n  const store = useStore(options);\n  const hydratedSet = getHydratedSet(store);\n  for (const [atom, value] of values) {\n    if (!hydratedSet.has(atom) || (options == null ? void 0 : options.dangerouslyForceHydrate)) {\n      hydratedSet.add(atom);\n      store.set(atom, value);\n    }\n  }\n}\nconst getHydratedSet = (store) => {\n  let hydratedSet = hydratedMap.get(store);\n  if (!hydratedSet) {\n    hydratedSet = /* @__PURE__ */ new WeakSet();\n    hydratedMap.set(store, hydratedSet);\n  }\n  return hydratedSet;\n};\n\nexport { useAtomCallback, useHydrateAtoms, useReducerAtom, useResetAtom };\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport { RTVIClientAudio } from \"./RTVIClientAudio\";\nimport { RTVIClientProvider } from \"./RTVIClientProvider\";\nimport { RTVIClientVideo } from \"./RTVIClientVideo\";\nimport { useRTVIClient } from \"./useRTVIClient\";\nimport { useRTVIClientEvent } from \"./useRTVIClientEvent\";\nimport { useRTVIClientMediaDevices } from \"./useRTVIClientMediaDevices\";\nimport { useRTVIClientMediaTrack } from \"./useRTVIClientMediaTrack\";\nimport { useRTVIClientTransportState } from \"./useRTVIClientTransportState\";\nimport { VoiceVisualizer } from \"./VoiceVisualizer\";\n\nexport {\n  RTVIClientAudio,\n  RTVIClientProvider,\n  RTV<PERSON><PERSON>Video,\n  useRTVIClient,\n  useRTVIClientEvent,\n  useRTVIClientMediaDevices,\n  useRTVIClientMediaTrack,\n  useRTVI<PERSON>lientTransportState,\n  VoiceVisualizer,\n};\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport { RTVIEvent } from \"@pipecat-ai/client-js\";\nimport { useCallback, useEffect, useRef } from \"react\";\nimport { useRTVIClientEvent } from \"./useRTVIClientEvent\";\nimport { useRTVIClientMediaTrack } from \"./useRTVIClientMediaTrack\";\n\nexport const RTVIClientAudio = () => {\n  const botAudioRef = useRef<HTMLAudioElement>(null);\n  const botAudioTrack = useRTVIClientMediaTrack(\"audio\", \"bot\");\n\n  useEffect(() => {\n    if (!botAudioRef.current || !botAudioTrack) return;\n    if (botAudioRef.current.srcObject) {\n      const oldTrack = (\n        botAudioRef.current.srcObject as MediaStream\n      ).getAudioTracks()[0];\n      if (oldTrack.id === botAudioTrack.id) return;\n    }\n    botAudioRef.current.srcObject = new MediaStream([botAudioTrack]);\n  }, [botAudioTrack]);\n\n  useRTVIClientEvent(\n    RTVIEvent.SpeakerUpdated,\n    useCallback((speaker: MediaDeviceInfo) => {\n      if (!botAudioRef.current) return;\n      if (typeof botAudioRef.current.setSinkId !== \"function\") return;\n      botAudioRef.current.setSinkId(speaker.deviceId);\n    }, [])\n  );\n\n  return (\n    <>\n      <audio ref={botAudioRef} autoPlay />\n    </>\n  );\n};\nRTVIClientAudio.displayName = \"RTVIClientAudio\";\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport { RTVIEvent, RTVIEventHandler } from \"@pipecat-ai/client-js\";\nimport { useEffect } from \"react\";\nimport { useRTVIClient } from \"./useRTVIClient\";\n\nexport const useRTVIClientEvent = <E extends RTVIEvent>(\n  event: E,\n  handler: RTVIEventHandler<E>\n) => {\n  const client = useRTVIClient();\n\n  useEffect(() => {\n    if (!client) return;\n    client.on(event, handler);\n    return () => {\n      client.off(event, handler);\n    };\n  }, [event, handler, client]);\n};\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport { useContext } from \"react\";\nimport { RTVIClientContext } from \"./RTVIClientProvider\";\n\nexport const useRTVIClient = () => {\n  const { client } = useContext(RTVIClientContext);\n  return client;\n};\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport { createContext } from \"react\";\n\nimport { RTVIClient } from \"@pipecat-ai/client-js\";\nimport { createStore } from \"jotai\";\nimport { Provider as <PERSON><PERSON><PERSON><PERSON><PERSON> } from \"jotai/react\";\n\nexport interface Props {\n  client: RTVIClient;\n  jotaiStore?: React.ComponentProps<typeof JotaiProvider>[\"store\"];\n}\n\nconst defaultStore = createStore();\n\nexport const RTVIClientContext = createContext<{ client?: RTVIClient }>({});\n\nexport const RTVIClientProvider: React.FC<React.PropsWithChildren<Props>> = ({\n  children,\n  client,\n  jotaiStore = defaultStore,\n}) => {\n  return (\n    <JotaiProvider store={jotaiStore}>\n      <RTVIClientContext.Provider value={{ client }}>\n        {children}\n      </RTVIClientContext.Provider>\n    </JotaiProvider>\n  );\n};\nRTVIClientProvider.displayName = \"RTVIClientProvider\";\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport { Participant, RTVIEvent, Tracks } from \"@pipecat-ai/client-js\";\nimport { atom, useAtomValue } from \"jotai\";\nimport { atomFamily, useAtomCallback } from \"jotai/utils\";\nimport { PrimitiveAtom } from \"jotai/vanilla\";\nimport { useCallback, useEffect } from \"react\";\nimport { useRTVIClient } from \"./useRTVIClient\";\nimport { useRTVIClientEvent } from \"./useRTVIClientEvent\";\n\ntype ParticipantType = keyof Tracks;\ntype TrackType = keyof Tracks[\"local\"];\n\nconst localAudioTrackAtom = atom<MediaStreamTrack | null>(null);\nconst localVideoTrackAtom = atom<MediaStreamTrack | null>(null);\nconst localScreenAudioTrackAtom = atom<MediaStreamTrack | null>(null);\nconst localScreenVideoTrackAtom = atom<MediaStreamTrack | null>(null);\nconst botAudioTrackAtom = atom<MediaStreamTrack | null>(null);\nconst botVideoTrackAtom = atom<MediaStreamTrack | null>(null);\n\nconst trackAtom = atomFamily<\n  { local: boolean; trackType: TrackType },\n  PrimitiveAtom<MediaStreamTrack | null>\n>(({ local, trackType }) => {\n  if (local) {\n    switch (trackType) {\n      case \"audio\":\n        return localAudioTrackAtom;\n      case \"screenAudio\":\n        return localScreenAudioTrackAtom;\n      case \"screenVideo\":\n        return localScreenVideoTrackAtom;\n      case \"video\":\n        return localVideoTrackAtom;\n    }\n  }\n  return trackType === \"audio\" ? botAudioTrackAtom : botVideoTrackAtom;\n});\n\nexport const useRTVIClientMediaTrack = (\n  trackType: TrackType,\n  participantType: ParticipantType\n) => {\n  const client = useRTVIClient();\n  const track = useAtomValue(\n    trackAtom({ local: participantType === \"local\", trackType })\n  );\n\n  const updateTrack = useAtomCallback(\n    useCallback(\n      (\n        get,\n        set,\n        track: MediaStreamTrack,\n        trackType: TrackType,\n        local: boolean\n      ) => {\n        const atom = trackAtom({\n          local,\n          trackType,\n        });\n        const oldTrack = get(atom);\n        if (oldTrack?.id === track.id) return;\n        set(atom, track);\n      },\n      [participantType, track, trackType]\n    )\n  );\n\n  useRTVIClientEvent(\n    RTVIEvent.TrackStarted,\n    useCallback((track: MediaStreamTrack, participant?: Participant) => {\n      updateTrack(track, track.kind as TrackType, Boolean(participant?.local));\n    }, [])\n  );\n\n  useRTVIClientEvent(\n    RTVIEvent.ScreenTrackStarted,\n    useCallback((track: MediaStreamTrack, participant?: Participant) => {\n      const trackType = track.kind === \"audio\" ? \"screenAudio\" : \"screenVideo\";\n      updateTrack(track, trackType, Boolean(participant?.local));\n    }, [])\n  );\n\n  useEffect(() => {\n    if (!client) return;\n    const tracks = client.tracks();\n    const track = tracks?.[participantType]?.[trackType];\n    if (!track) return;\n    updateTrack(track, trackType, participantType === \"local\");\n  }, [participantType, trackType, updateTrack, client]);\n\n  return track;\n};\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport React, { forwardRef, useEffect, useRef } from \"react\";\nimport useMergedRef from \"./useMergedRef\";\nimport { useRTVIClientMediaTrack } from \"./useRTVIClientMediaTrack\";\n\ninterface RTVIClientVideoInterface {\n  aspectRatio: number;\n  height: number;\n  width: number;\n}\n\nexport interface Props\n  extends Omit<React.VideoHTMLAttributes<HTMLVideoElement>, \"onResize\"> {\n  participant: \"local\" | \"bot\";\n\n  /**\n   * Defines the video track type to display. Default: 'video'.\n   */\n  trackType?: \"screenVideo\" | \"video\";\n\n  /**\n   * Defines whether the video should be fully contained or cover the box. Default: 'contain'.\n   */\n  fit?: \"contain\" | \"cover\";\n  /**\n   * Forces the video to be mirrored, if set.\n   */\n  mirror?: boolean;\n\n  /**\n   * Optional callback, which is triggered whenever the video's rendered width or height changes.\n   * Returns the video's native width, height and aspectRatio.\n   */\n  onResize?(dimensions: RTVIClientVideoInterface): void;\n}\n\nexport const RTVIClientVideo = forwardRef<HTMLVideoElement, Props>(\n  function VoiceClientVideo(\n    {\n      participant = \"local\",\n      fit = \"contain\",\n      mirror,\n      onResize,\n      style = {},\n      trackType = \"video\",\n      ...props\n    },\n    ref\n  ) {\n    const videoTrack: MediaStreamTrack | null = useRTVIClientMediaTrack(\n      trackType,\n      participant\n    );\n\n    const videoEl = useRef<HTMLVideoElement>(null);\n    const videoRef = useMergedRef<HTMLVideoElement>(videoEl, ref);\n\n    /**\n     * Handle canplay & picture-in-picture events.\n     */\n    useEffect(function setupVideoEvents() {\n      const video = videoEl.current;\n      if (!video) return;\n\n      const playVideo = () => {\n        const promise = video.play();\n        if (promise !== undefined) {\n          promise\n            .then(() => {\n              // All good, playback started.\n              video.controls = false;\n            })\n            .catch((error) => {\n              // Auto-play was prevented. Show video controls, so user can play video manually.\n              video.controls = true;\n              console.warn(\"Failed to play video\", error);\n            });\n        }\n      };\n\n      const handleCanPlay = () => {\n        if (!video.paused) return;\n        playVideo();\n      };\n      const handleEnterPIP = () => {\n        video.style.transform = \"scale(1)\";\n      };\n      const handleLeavePIP = () => {\n        video.style.transform = \"\";\n        setTimeout(() => {\n          if (video.paused) playVideo();\n        }, 100);\n      };\n      const handleVisibilityChange = () => {\n        if (document.visibilityState === \"hidden\") return;\n        if (!video.paused) return;\n        playVideo();\n      };\n      video.addEventListener(\"canplay\", handleCanPlay);\n      video.addEventListener(\"enterpictureinpicture\", handleEnterPIP);\n      video.addEventListener(\"leavepictureinpicture\", handleLeavePIP);\n\n      // Videos can be paused if media was played in another app on iOS.\n      document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n      return () => {\n        video.removeEventListener(\"canplay\", handleCanPlay);\n        video.removeEventListener(\"enterpictureinpicture\", handleEnterPIP);\n        video.removeEventListener(\"leavepictureinpicture\", handleLeavePIP);\n        document.removeEventListener(\n          \"visibilitychange\",\n          handleVisibilityChange\n        );\n      };\n    }, []);\n\n    /**\n     * Update srcObject.\n     */\n    useEffect(\n      function updateSrcObject() {\n        const video = videoEl.current;\n        if (!video || !videoTrack) return;\n        video.srcObject = new MediaStream([videoTrack]);\n        video.load();\n        return () => {\n          // clean up when unmounted\n          video.srcObject = null;\n          video.load();\n        };\n      },\n      [videoTrack, videoTrack?.id]\n    );\n\n    /**\n     * Add optional event listener for resize event so the parent component\n     * can know the video's native aspect ratio.\n     */\n    useEffect(\n      function reportVideoDimensions() {\n        const video = videoEl.current;\n        if (!onResize || !video) return;\n\n        let frame: ReturnType<typeof requestAnimationFrame>;\n        function handleResize() {\n          if (frame) cancelAnimationFrame(frame);\n          frame = requestAnimationFrame(() => {\n            const video = videoEl.current;\n            if (!video || document.hidden) return;\n            const videoWidth = video.videoWidth;\n            const videoHeight = video.videoHeight;\n            if (videoWidth && videoHeight) {\n              onResize?.({\n                aspectRatio: videoWidth / videoHeight,\n                height: videoHeight,\n                width: videoWidth,\n              });\n            }\n          });\n        }\n\n        handleResize();\n        video.addEventListener(\"loadedmetadata\", handleResize);\n        video.addEventListener(\"resize\", handleResize);\n\n        return () => {\n          if (frame) cancelAnimationFrame(frame);\n          video.removeEventListener(\"loadedmetadata\", handleResize);\n          video.removeEventListener(\"resize\", handleResize);\n        };\n      },\n      [onResize]\n    );\n\n    return (\n      <video\n        autoPlay\n        muted\n        playsInline\n        ref={videoRef}\n        style={{\n          objectFit: fit,\n          transform: mirror ? \"scale(-1, 1)\" : \"\",\n          ...style,\n        }}\n        {...props}\n      />\n    );\n  }\n);\nRTVIClientVideo.displayName = \"RTVIClientVideo\";\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n *\n * This file contains code derived from:\n * https://github.com/jaredLunde/react-hook/blob/master/packages/merged-ref/src/index.tsx\n * Original author: <PERSON> (https://github.com/jaredLunde)\n * Original license: MIT (https://github.com/jaredLunde/react-hook/blob/master/LICENSE)\n */\n\nimport React, { useCallback } from \"react\";\n\nfunction useMergedRef<T>(...refs: React.Ref<T>[]): React.RefCallback<T> {\n  return useCallback(\n    (element: T) => {\n      for (let i = 0; i < refs.length; i++) {\n        const ref = refs[i];\n        if (typeof ref === \"function\") ref(element);\n        else if (ref && typeof ref === \"object\")\n          (ref as React.MutableRefObject<T>).current = element;\n      }\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    refs\n  );\n}\n\nexport default useMergedRef;\n", "import { RTVIEvent } from \"@pipecat-ai/client-js\";\nimport { atom, useAtomValue } from \"jotai\";\nimport { useAtomCallback } from \"jotai/utils\";\nimport { useCallback } from \"react\";\nimport { useRTVIClient } from \"./useRTVIClient\";\nimport { useRTVIClientEvent } from \"./useRTVIClientEvent\";\n\ntype OptionalMediaDeviceInfo = MediaDeviceInfo | Record<string, never>;\n\nconst availableMicsAtom = atom<MediaDeviceInfo[]>([]);\nconst availableCamsAtom = atom<MediaDeviceInfo[]>([]);\nconst availableSpeakersAtom = atom<MediaDeviceInfo[]>([]);\nconst selectedMicAtom = atom<OptionalMediaDeviceInfo>({});\nconst selectedCamAtom = atom<OptionalMediaDeviceInfo>({});\nconst selectedSpeakerAtom = atom<OptionalMediaDeviceInfo>({});\n\nexport const useRTVIClientMediaDevices = () => {\n  const client = useRTVIClient();\n\n  const availableCams = useAtomValue(availableCamsAtom);\n  const availableMics = useAtomValue(availableMicsAtom);\n  const availableSpeakers = useAtomValue(availableSpeakersAtom);\n  const selectedCam = useAtomValue(selectedCamAtom);\n  const selectedMic = useAtomValue(selectedMicAtom);\n  const selectedSpeaker = useAtomValue(selectedSpeakerAtom);\n\n  useRTVIClientEvent(\n    RTVIEvent.AvailableCamsUpdated,\n    useAtomCallback(\n      useCallback((_get, set, cams) => {\n        set(availableCamsAtom, cams);\n      }, [])\n    )\n  );\n  useRTVIClientEvent(\n    RTVIEvent.AvailableMicsUpdated,\n    useAtomCallback(\n      useCallback((_get, set, mics) => {\n        set(availableMicsAtom, mics);\n      }, [])\n    )\n  );\n  useRTVIClientEvent(\n    RTVIEvent.AvailableSpeakersUpdated,\n    useAtomCallback(\n      useCallback((_get, set, speakers) => {\n        set(availableSpeakersAtom, speakers);\n      }, [])\n    )\n  );\n  useRTVIClientEvent(\n    RTVIEvent.CamUpdated,\n    useAtomCallback(\n      useCallback((_get, set, cam) => {\n        set(selectedCamAtom, cam);\n      }, [])\n    )\n  );\n  useRTVIClientEvent(\n    RTVIEvent.MicUpdated,\n    useAtomCallback(\n      useCallback((_get, set, mic) => {\n        set(selectedMicAtom, mic);\n      }, [])\n    )\n  );\n  useRTVIClientEvent(\n    RTVIEvent.SpeakerUpdated,\n    useAtomCallback(\n      useCallback((_get, set, speaker) => {\n        set(selectedSpeakerAtom, speaker);\n      }, [])\n    )\n  );\n\n  const updateCam = useCallback(\n    (id: string) => {\n      client?.updateCam(id);\n    },\n    [client]\n  );\n  const updateMic = useCallback(\n    (id: string) => {\n      client?.updateMic(id);\n    },\n    [client]\n  );\n  const updateSpeaker = useCallback(\n    (id: string) => {\n      client?.updateSpeaker(id);\n    },\n    [client]\n  );\n\n  return {\n    availableCams,\n    availableMics,\n    availableSpeakers,\n    selectedCam,\n    selectedMic,\n    selectedSpeaker,\n    updateCam,\n    updateMic,\n    updateSpeaker,\n  };\n};\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport { RTVIEvent, TransportState } from \"@pipecat-ai/client-js\";\nimport { atom, useAtom } from \"jotai\";\nimport { useRTVIClientEvent } from \"./useRTVIClientEvent\";\n\nconst transportStateAtom = atom<TransportState>(\"disconnected\");\n\nexport const useRTVIClientTransportState = () => {\n  const [transportState, setTransportState] = useAtom(transportStateAtom);\n\n  useRTVIClientEvent(RTVIEvent.TransportStateChanged, setTransportState);\n\n  return transportState;\n};\n", "/**\n * Copyright (c) 2024, Daily.\n *\n * SPDX-License-Identifier: BSD-2-Clause\n */\n\nimport React, { useEffect, useRef } from \"react\";\nimport { useRTVIClientMediaTrack } from \"./useRTVIClientMediaTrack\";\n\ntype ParticipantType = Parameters<typeof useRTVIClientMediaTrack>[1];\n\ninterface Props {\n  backgroundColor?: string;\n  barColor?: string;\n  barGap?: number;\n  barWidth?: number;\n  barMaxHeight?: number;\n  participantType: ParticipantType;\n}\n\nexport const VoiceVisualizer: React.FC<Props> = React.memo(\n  ({\n    backgroundColor = \"transparent\",\n    barColor = \"black\",\n    barWidth = 30,\n    barGap = 12,\n    barMaxHeight = 120,\n    participantType,\n  }) => {\n    const canvasRef = useRef<HTMLCanvasElement>(null);\n\n    const track: MediaStreamTrack | null = useRTVIClientMediaTrack(\n      \"audio\",\n      participantType\n    );\n\n    useEffect(() => {\n      if (!canvasRef.current) return;\n\n      const canvasWidth = 5 * barWidth + 4 * barGap;\n      const canvasHeight = barMaxHeight;\n\n      const canvas = canvasRef.current;\n\n      const scaleFactor = 2;\n\n      // Make canvas fill the width and height of its container\n      const resizeCanvas = () => {\n        canvas.width = canvasWidth * scaleFactor;\n        canvas.height = canvasHeight * scaleFactor;\n\n        canvas.style.width = `${canvasWidth}px`;\n        canvas.style.height = `${canvasHeight}px`;\n\n        canvasCtx.lineCap = \"round\";\n        canvasCtx.scale(scaleFactor, scaleFactor);\n      };\n\n      const canvasCtx = canvas.getContext(\"2d\")!;\n      resizeCanvas();\n\n      if (!track) return;\n\n      const audioContext = new AudioContext();\n      const source = audioContext.createMediaStreamSource(\n        new MediaStream([track])\n      );\n      const analyser = audioContext.createAnalyser();\n\n      analyser.fftSize = 1024;\n\n      source.connect(analyser);\n\n      const frequencyData = new Uint8Array(analyser.frequencyBinCount);\n\n      canvasCtx.lineCap = \"round\";\n\n      const bands = [\n        { startFreq: 85, endFreq: 255, smoothValue: 0 }, // Covers fundamental frequencies for male and female voices\n        { startFreq: 255, endFreq: 500, smoothValue: 0 }, // Lower formants and some harmonics\n        { startFreq: 500, endFreq: 2000, smoothValue: 0 }, // Vowel formants and key consonant frequencies\n        { startFreq: 2000, endFreq: 4000, smoothValue: 0 }, // Higher formants, \"clarity\" of speech\n        { startFreq: 4000, endFreq: 8000, smoothValue: 0 }, // Sibilance and high-frequency consonants\n      ];\n\n      const getFrequencyBinIndex = (frequency: number) => {\n        const nyquist = audioContext.sampleRate / 2;\n        return Math.round(\n          (frequency / nyquist) * (analyser.frequencyBinCount - 1)\n        );\n      };\n\n      function drawSpectrum() {\n        analyser.getByteFrequencyData(frequencyData);\n        canvasCtx.clearRect(\n          0,\n          0,\n          canvas.width / scaleFactor,\n          canvas.height / scaleFactor\n        );\n        canvasCtx.fillStyle = backgroundColor;\n        canvasCtx.fillRect(\n          0,\n          0,\n          canvas.width / scaleFactor,\n          canvas.height / scaleFactor\n        );\n\n        let isActive = false;\n\n        const totalBarsWidth =\n          bands.length * barWidth + (bands.length - 1) * barGap;\n        const startX = (canvas.width / scaleFactor - totalBarsWidth) / 2; // Center bars\n\n        const adjustedCircleRadius = barWidth / 2; // Fixed radius for reset circles\n\n        bands.forEach((band, i) => {\n          const startIndex = getFrequencyBinIndex(band.startFreq);\n          const endIndex = getFrequencyBinIndex(band.endFreq);\n          const bandData = frequencyData.slice(startIndex, endIndex);\n          const bandValue =\n            bandData.reduce((acc, val) => acc + val, 0) / bandData.length;\n\n          const smoothingFactor = 0.2;\n\n          if (bandValue < 1) {\n            band.smoothValue = Math.max(\n              band.smoothValue - smoothingFactor * 5,\n              0\n            );\n          } else {\n            band.smoothValue =\n              band.smoothValue +\n              (bandValue - band.smoothValue) * smoothingFactor;\n            isActive = true;\n          }\n\n          const x = startX + i * (barWidth + barGap);\n          // Calculate bar height with a maximum cap\n          const barHeight = Math.min(\n            (band.smoothValue / 255) * barMaxHeight,\n            barMaxHeight\n          );\n\n          const yTop = Math.max(\n            canvas.height / scaleFactor / 2 - barHeight / 2,\n            adjustedCircleRadius\n          );\n          const yBottom = Math.min(\n            canvas.height / scaleFactor / 2 + barHeight / 2,\n            canvas.height / scaleFactor - adjustedCircleRadius\n          );\n\n          if (band.smoothValue > 0) {\n            canvasCtx.beginPath();\n            canvasCtx.moveTo(x + barWidth / 2, yTop);\n            canvasCtx.lineTo(x + barWidth / 2, yBottom);\n            canvasCtx.lineWidth = barWidth;\n            canvasCtx.strokeStyle = barColor;\n            canvasCtx.stroke();\n          } else {\n            canvasCtx.beginPath();\n            canvasCtx.arc(\n              x + barWidth / 2,\n              canvas.height / scaleFactor / 2,\n              adjustedCircleRadius,\n              0,\n              2 * Math.PI\n            );\n            canvasCtx.fillStyle = barColor;\n            canvasCtx.fill();\n            canvasCtx.closePath();\n          }\n        });\n\n        if (!isActive) {\n          drawInactiveCircles(adjustedCircleRadius, barColor);\n        }\n\n        requestAnimationFrame(drawSpectrum);\n      }\n\n      function drawInactiveCircles(circleRadius: number, color: string) {\n        const totalBarsWidth =\n          bands.length * barWidth + (bands.length - 1) * barGap;\n        const startX = (canvas.width / scaleFactor - totalBarsWidth) / 2;\n        const y = canvas.height / scaleFactor / 2;\n\n        bands.forEach((_, i) => {\n          const x = startX + i * (barWidth + barGap);\n\n          canvasCtx.beginPath();\n          canvasCtx.arc(x + barWidth / 2, y, circleRadius, 0, 2 * Math.PI);\n          canvasCtx.fillStyle = color;\n          canvasCtx.fill();\n          canvasCtx.closePath();\n        });\n      }\n\n      drawSpectrum();\n\n      // Handle resizing\n      window.addEventListener(\"resize\", resizeCanvas);\n\n      return () => {\n        audioContext.close();\n        window.removeEventListener(\"resize\", resizeCanvas);\n      };\n    }, [backgroundColor, barColor, barGap, barMaxHeight, barWidth, track]);\n\n    return (\n      <canvas\n        ref={canvasRef}\n        style={{\n          display: \"block\",\n          width: \"100%\",\n          height: \"100%\",\n        }}\n      />\n    );\n  }\n);\n\nVoiceVisualizer.displayName = \"VoiceVisualizer\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,IAAM,aAAa,CAACA,OAAM,MAAMA,MAAK,cAAcA,MAAK,YAAY,CAAC,IAAI,MAAMA;AAC/E,IAAM,kBAAkB,CAACA,UAAS,UAAUA;AAC5C,IAAM,yBAAyB,CAACA,UAAS,CAAC,CAACA,MAAK;AAChD,IAAM,yBAAyB,CAAC,cAAc,OAAO,aAAa,OAAO;AACzE,IAAM,kBAAkB,CAAC,cAAc;AACrC,MAAI,OAAO,WAAW;AACpB,UAAM,UAAU;AAAA,EAClB;AACA,OAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,EAAE,OAAO,YAAY;AAC7F,UAAM,IAAI,MAAM,qCAAqC;AAAA,EACvD;AACA,SAAO,UAAU;AACnB;AACA,IAAM,kBAAkC,oBAAI,QAAQ;AACpD,IAAM,mBAAmB,CAAC,UAAU;AAClC,MAAI;AACJ,SAAO,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,gBAAgB,IAAI,KAAK,MAAM,OAAO,SAAS,GAAG,CAAC;AAC7F;AACA,IAAM,eAAe,CAAC,YAAY;AAChC,QAAM,eAAe,gBAAgB,IAAI,OAAO;AAChD,MAAI,gBAAgB,OAAO,SAAS,aAAa,CAAC,GAAG;AACnD,iBAAa,CAAC,IAAI;AAClB,iBAAa,CAAC,EAAE,QAAQ,CAAC,OAAO,GAAG,CAAC;AAAA,EACtC;AACF;AACA,IAAM,uBAAuB,CAAC,SAAS,iBAAiB;AACtD,MAAI,eAAe,gBAAgB,IAAI,OAAO;AAC9C,MAAI,CAAC,cAAc;AACjB,mBAAe,CAAC,MAAsB,oBAAI,IAAI,CAAC;AAC/C,oBAAgB,IAAI,SAAS,YAAY;AACzC,UAAM,SAAS,MAAM;AACnB,mBAAa,CAAC,IAAI;AAAA,IACpB;AACA,YAAQ,KAAK,QAAQ,MAAM;AAAA,EAC7B;AACA,eAAa,CAAC,EAAE,IAAI,YAAY;AAClC;AACA,IAAM,gBAAgB,CAAC,MAAM,QAAQ,KAAK,OAAO,SAAS,EAAE,UAAU;AACtE,IAAM,gCAAgC,CAACA,OAAM,SAAS,wBAAwB;AAC5E,MAAI,CAAC,oBAAoB,EAAE,IAAIA,KAAI,GAAG;AACpC,wBAAoB,EAAE,IAAIA,KAAI;AAC9B,YAAQ;AAAA,MACN,MAAM;AACJ,4BAAoB,EAAE,OAAOA,KAAI;AAAA,MACnC;AAAA,MACA,MAAM;AACJ,4BAAoB,EAAE,OAAOA,KAAI;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,6BAA6B,CAACA,OAAM,gBAAgB,oBAAoB;AAC5E,QAAM,YAAY,gBAAgBA,KAAI;AACtC,QAAM,eAAe,OAAO;AAC5B,QAAM,YAAY,UAAU;AAC5B,MAAI,cAAc,cAAc,GAAG;AACjC,eAAW,KAAK,UAAU,EAAE,KAAK,GAAG;AAClC,oCAA8BA,OAAM,gBAAgB,gBAAgB,CAAC,CAAC;AAAA,IACxE;AAAA,EACF;AACA,YAAU,IAAI;AACd,SAAO,UAAU;AACjB,MAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,WAAW,UAAU,CAAC,GAAG;AACvD,MAAE,UAAU;AACZ,QAAI,cAAc,SAAS,GAAG;AAC5B,mBAAa,SAAS;AAAA,IACxB;AAAA,EACF;AACF;AACA,IAAM,gCAAgC,CAACA,OAAM,WAAW,eAAe;AACrE,MAAI;AACJ,QAAM,aAA6B,oBAAI,IAAI;AAC3C,aAAW,OAAO,KAAK,WAAW,IAAIA,KAAI,MAAM,OAAO,SAAS,GAAG,MAAM,CAAC,GAAG;AAC3E,QAAI,WAAW,IAAI,CAAC,GAAG;AACrB,iBAAW,IAAI,CAAC;AAAA,IAClB;AAAA,EACF;AACA,aAAW,0BAA0B,UAAU,GAAG;AAChD,eAAW,IAAI,sBAAsB;AAAA,EACvC;AACA,SAAO;AACT;AACA,IAAM,kBAAkB,MAAM;AAC5B,QAAM,YAA4B,oBAAI,IAAI;AAC1C,QAAM,SAAS,MAAM;AACnB,cAAU,QAAQ,CAAC,OAAO,GAAG,CAAC;AAAA,EAChC;AACA,SAAO,MAAM,CAAC,OAAO;AACnB,cAAU,IAAI,EAAE;AAChB,WAAO,MAAM;AACX,gBAAU,OAAO,EAAE;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,0BAA0B,MAAM;AACpC,QAAM,MAAM,CAAC;AACb,QAAM,YAA4B,oBAAI,QAAQ;AAC9C,QAAM,SAAS,CAACA,UAAS;AACvB,QAAI,IAAI;AACR,KAAC,KAAK,UAAU,IAAI,GAAG,MAAM,OAAO,SAAS,GAAG,QAAQ,CAAC,OAAO,GAAGA,KAAI,CAAC;AACxE,KAAC,KAAK,UAAU,IAAIA,KAAI,MAAM,OAAO,SAAS,GAAG,QAAQ,CAAC,OAAO,GAAG,CAAC;AAAA,EACvE;AACA,SAAO,MAAM,CAACA,OAAM,OAAO;AACzB,UAAM,MAAMA,SAAQ;AACpB,UAAM,OAAO,UAAU,IAAI,GAAG,IAAI,YAAY,UAAU,IAAI,KAAqB,oBAAI,IAAI,CAAC,GAAG,IAAI,GAAG;AACpG,QAAI,IAAI,EAAE;AACV,WAAO,MAAM;AACX,aAAO,OAAO,SAAS,IAAI,OAAO,EAAE;AACpC,UAAI,CAAC,IAAI,MAAM;AACb,kBAAU,OAAO,GAAG;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,uBAAuB,CAAC,eAAe;AAC3C,aAAW,MAAM,WAAW,IAAI,wBAAwB;AACxD,aAAW,MAAM,WAAW,IAAI,wBAAwB;AACxD,aAAW,MAAM,WAAW,IAAI,wBAAwB;AACxD,aAAW,MAAM,WAAW,IAAI,gBAAgB;AAChD,SAAO;AACT;AACA,IAAM,kBAAkB,OAAO;AAE/B,IAAM,aAAa,CAAC,eAA+B,oBAAI,QAAQ,GAAG,aAA6B,oBAAI,QAAQ,GAAG,mBAAmC,oBAAI,QAAQ,GAAG,eAA+B,oBAAI,IAAI,GAAG,iBAAiC,oBAAI,IAAI,GAAG,mBAAmC,oBAAI,IAAI,GAAG,aAAa,CAAC,GAAG,WAAW,CAACC,UAAS,WAAWA,MAAK,KAAK,GAAG,MAAM,GAAG,YAAY,CAACA,UAAS,WAAWA,MAAK,MAAM,GAAG,MAAM,GAAG,aAAa,CAACA,OAAM,UAAU;AAC/b,MAAI;AACJ,UAAQ,KAAKA,MAAK,oBAAoB,OAAO,SAAS,GAAG,KAAKA,OAAM,KAAK;AAC3E,GAAG,cAAc,CAACA,OAAM,YAAY;AAClC,MAAI;AACJ,UAAQ,KAAKA,MAAK,YAAY,OAAO,SAAS,GAAG,KAAKA,OAAM,OAAO;AACrE,MAAM,2BAA2B;AAC/B,QAAM,kBAAkB,uBAAuB,CAAC,MAAM,CAACA,UAAS;AAC9D,SAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,CAACA,OAAM;AAC/E,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AACA,QAAI,YAAY,aAAa,IAAIA,KAAI;AACrC,QAAI,CAAC,WAAW;AACd,kBAAY,EAAE,GAAmB,oBAAI,IAAI,GAAG,GAAmB,oBAAI,IAAI,GAAG,GAAG,EAAE;AAC/E,mBAAa,IAAIA,OAAM,SAAS;AAChC,oBAAc,OAAO,SAAS,WAAWA,OAAM,KAAK;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AACA,QAAM,iBAAiB,uBAAuB,CAAC,MAAM,MAAM;AACzD,UAAM,SAAS,CAAC;AAChB,UAAM,OAAO,CAAC,OAAO;AACnB,UAAI;AACF,WAAG;AAAA,MACL,SAAS,GAAG;AACV,eAAO,KAAK,CAAC;AAAA,MACf;AAAA,IACF;AACA,OAAG;AACD,UAAI,WAAW,GAAG;AAChB,aAAK,WAAW,CAAC;AAAA,MACnB;AACA,YAAM,YAA4B,oBAAI,IAAI;AAC1C,YAAM,MAAM,UAAU,IAAI,KAAK,SAAS;AACxC,mBAAa,QAAQ,CAACA,UAAS;AAC7B,YAAI;AACJ,gBAAQ,KAAK,WAAW,IAAIA,KAAI,MAAM,OAAO,SAAS,GAAG,EAAE,QAAQ,GAAG;AAAA,MACxE,CAAC;AACD,mBAAa,MAAM;AACnB,uBAAiB,QAAQ,GAAG;AAC5B,uBAAiB,MAAM;AACvB,qBAAe,QAAQ,GAAG;AAC1B,qBAAe,MAAM;AACrB,gBAAU,QAAQ,IAAI;AACtB,UAAI,aAAa,MAAM;AACrB,kCAA0B;AAAA,MAC5B;AAAA,IACF,SAAS,aAAa,QAAQ,iBAAiB,QAAQ,eAAe;AACtE,QAAI,OAAO,QAAQ;AACjB,YAAM,IAAI,eAAe,MAAM;AAAA,IACjC;AAAA,EACF;AACA,QAAM,4BAA4B,uBAAuB,CAAC,MAAM,MAAM;AACpE,UAAM,oBAAoB,CAAC;AAC3B,UAAM,WAA2B,oBAAI,QAAQ;AAC7C,UAAM,UAA0B,oBAAI,QAAQ;AAC5C,UAAM,QAAQ,MAAM,KAAK,YAAY;AACrC,WAAO,MAAM,QAAQ;AACnB,YAAM,IAAI,MAAM,MAAM,SAAS,CAAC;AAChC,YAAM,SAAS,gBAAgB,CAAC;AAChC,UAAI,QAAQ,IAAI,CAAC,GAAG;AAClB,cAAM,IAAI;AACV;AAAA,MACF;AACA,UAAI,SAAS,IAAI,CAAC,GAAG;AACnB,YAAI,iBAAiB,IAAI,CAAC,MAAM,OAAO,GAAG;AACxC,4BAAkB,KAAK,CAAC,GAAG,MAAM,CAAC;AAAA,QACpC,YAAY,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,iBAAiB,IAAI,CAAC,GAAG;AACxG,gBAAM,IAAI,MAAM,+BAA+B;AAAA,QACjD;AACA,gBAAQ,IAAI,CAAC;AACb,cAAM,IAAI;AACV;AAAA,MACF;AACA,eAAS,IAAI,CAAC;AACd,iBAAW,KAAK,8BAA8B,GAAG,QAAQ,UAAU,GAAG;AACpE,YAAI,CAAC,SAAS,IAAI,CAAC,GAAG;AACpB,gBAAM,KAAK,CAAC;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,aAAS,IAAI,kBAAkB,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACtD,YAAM,CAAC,GAAG,MAAM,IAAI,kBAAkB,CAAC;AACvC,UAAI,iBAAiB;AACrB,iBAAW,OAAO,OAAO,EAAE,KAAK,GAAG;AACjC,YAAI,QAAQ,KAAK,aAAa,IAAI,GAAG,GAAG;AACtC,2BAAiB;AACjB;AAAA,QACF;AAAA,MACF;AACA,UAAI,gBAAgB;AAClB,sBAAc,CAAC;AACf,0BAAkB,CAAC;AAAA,MACrB;AACA,uBAAiB,OAAO,CAAC;AAAA,IAC3B;AAAA,EACF;AACA,QAAM,gBAAgB,uBAAuB,CAAC,MAAM,CAACA,UAAS;AAC5D,QAAI;AACJ,UAAM,YAAY,gBAAgBA,KAAI;AACtC,QAAI,uBAAuB,SAAS,GAAG;AACrC,UAAI,WAAW,IAAIA,KAAI,KAAK,iBAAiB,IAAIA,KAAI,MAAM,UAAU,GAAG;AACtE,eAAO;AAAA,MACT;AACA,UAAI,MAAM,KAAK,UAAU,CAAC,EAAE;AAAA,QAC1B,CAAC,CAAC,GAAG,CAAC;AAAA;AAAA;AAAA,UAGJ,cAAc,CAAC,EAAE,MAAM;AAAA;AAAA,MAE3B,GAAG;AACD,eAAO;AAAA,MACT;AAAA,IACF;AACA,cAAU,EAAE,MAAM;AAClB,QAAI,SAAS;AACb,UAAM,2BAA2B,MAAM;AACrC,UAAI,WAAW,IAAIA,KAAI,GAAG;AACxB,0BAAkBA,KAAI;AACtB,kCAA0B;AAC1B,uBAAe;AAAA,MACjB;AAAA,IACF;AACA,UAAM,SAAS,CAAC,MAAM;AACpB,UAAI;AACJ,UAAI,WAAWA,OAAM,CAAC,GAAG;AACvB,cAAM,UAAU,gBAAgB,CAAC;AACjC,YAAI,CAAC,uBAAuB,OAAO,GAAG;AACpC,cAAI,gBAAgB,CAAC,GAAG;AACtB,uCAA2B,GAAG,EAAE,MAAM,eAAe;AAAA,UACvD,OAAO;AACL,kBAAM,IAAI,MAAM,cAAc;AAAA,UAChC;AAAA,QACF;AACA,eAAO,gBAAgB,OAAO;AAAA,MAChC;AACA,YAAM,SAAS,cAAc,CAAC;AAC9B,UAAI;AACF,eAAO,gBAAgB,MAAM;AAAA,MAC/B,UAAE;AACA,kBAAU,EAAE,IAAI,GAAG,OAAO,CAAC;AAC3B,YAAI,iBAAiB,UAAU,CAAC,GAAG;AACjC,wCAA8BA,OAAM,UAAU,GAAG,MAAM;AAAA,QACzD;AACA,SAAC,MAAM,WAAW,IAAI,CAAC,MAAM,OAAO,SAAS,IAAI,EAAE,IAAIA,KAAI;AAC3D,YAAI,CAAC,QAAQ;AACX,mCAAyB;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACJ,UAAM,UAAU;AAAA,MACd,IAAI,SAAS;AACX,YAAI,CAAC,YAAY;AACf,uBAAa,IAAI,gBAAgB;AAAA,QACnC;AACA,eAAO,WAAW;AAAA,MACpB;AAAA,MACA,IAAI,UAAU;AACZ,aAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,CAAC,uBAAuBA,KAAI,GAAG;AACvG,kBAAQ,KAAK,qDAAqD;AAAA,QACpE;AACA,YAAI,CAAC,WAAW,uBAAuBA,KAAI,GAAG;AAC5C,oBAAU,IAAI,SAAS;AACrB,iBAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,QAAQ;AAChF,sBAAQ,KAAK,2CAA2C;AAAA,YAC1D;AACA,gBAAI,CAAC,QAAQ;AACX,kBAAI;AACF,uBAAO,eAAeA,OAAM,GAAG,IAAI;AAAA,cACrC,UAAE;AACA,0CAA0B;AAC1B,+BAAe;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,kBAAkB,UAAU;AAClC,QAAI;AACF,YAAM,iBAAiB,SAASA,OAAM,QAAQ,OAAO;AACrD,iCAA2BA,OAAM,gBAAgB,eAAe;AAChE,UAAI,cAAc,cAAc,GAAG;AACjC,6BAAqB,gBAAgB,MAAM,cAAc,OAAO,SAAS,WAAW,MAAM,CAAC;AAC3F,uBAAe;AAAA,UACb;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT,SAAS,OAAO;AACd,aAAO,UAAU;AACjB,gBAAU,IAAI;AACd,QAAE,UAAU;AACZ,aAAO;AAAA,IACT,UAAE;AACA,eAAS;AACT,UAAI,oBAAoB,UAAU,KAAK,iBAAiB,IAAIA,KAAI,MAAM,iBAAiB;AACrF,yBAAiB,IAAIA,OAAM,UAAU,CAAC;AACtC,qBAAa,IAAIA,KAAI;AACrB,SAAC,KAAK,WAAW,MAAM,OAAO,SAAS,GAAG,KAAK,YAAYA,KAAI;AAAA,MACjE;AAAA,IACF;AAAA,EACF;AACA,QAAM,uBAAuB,uBAAuB,CAAC,MAAM,CAACA,UAAS;AACnE,UAAM,QAAQ,CAACA,KAAI;AACnB,WAAO,MAAM,QAAQ;AACnB,YAAM,IAAI,MAAM,IAAI;AACpB,YAAM,SAAS,gBAAgB,CAAC;AAChC,iBAAW,KAAK,8BAA8B,GAAG,QAAQ,UAAU,GAAG;AACpE,cAAM,SAAS,gBAAgB,CAAC;AAChC,yBAAiB,IAAI,GAAG,OAAO,CAAC;AAChC,cAAM,KAAK,CAAC;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,uBAAuB,CAAC,MAAM,CAACA,UAAS,SAAS;AACtE,QAAI,SAAS;AACb,UAAM,SAAS,CAAC,MAAM,gBAAgB,cAAc,CAAC,CAAC;AACtD,UAAM,SAAS,CAAC,MAAM,UAAU;AAC9B,UAAI;AACJ,YAAM,SAAS,gBAAgB,CAAC;AAChC,UAAI;AACF,YAAI,WAAWA,OAAM,CAAC,GAAG;AACvB,cAAI,CAAC,gBAAgB,CAAC,GAAG;AACvB,kBAAM,IAAI,MAAM,mBAAmB;AAAA,UACrC;AACA,gBAAM,kBAAkB,OAAO;AAC/B,gBAAM,IAAI,MAAM,CAAC;AACjB,qCAA2B,GAAG,GAAG,eAAe;AAChD,4BAAkB,CAAC;AACnB,cAAI,oBAAoB,OAAO,GAAG;AAChC,yBAAa,IAAI,CAAC;AAClB,aAAC,KAAK,WAAW,MAAM,OAAO,SAAS,GAAG,KAAK,YAAY,CAAC;AAC5D,iCAAqB,CAAC;AAAA,UACxB;AACA,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,eAAe,GAAG,GAAG,KAAK;AAAA,QACnC;AAAA,MACF,UAAE;AACA,YAAI,CAAC,QAAQ;AACX,oCAA0B;AAC1B,yBAAe;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACF,aAAO,UAAUA,OAAM,QAAQ,QAAQ,GAAG,IAAI;AAAA,IAChD,UAAE;AACA,eAAS;AAAA,IACX;AAAA,EACF;AACA,QAAM,oBAAoB,uBAAuB,CAAC,MAAM,CAACA,UAAS;AAChE,QAAI;AACJ,UAAM,YAAY,gBAAgBA,KAAI;AACtC,UAAM,UAAU,WAAW,IAAIA,KAAI;AACnC,QAAI,WAAW,CAAC,iBAAiB,UAAU,CAAC,GAAG;AAC7C,iBAAW,CAAC,GAAG,CAAC,KAAK,UAAU,GAAG;AAChC,YAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG;AACrB,gBAAM,SAAS,gBAAgB,CAAC;AAChC,gBAAM,WAAW,UAAU,CAAC;AAC5B,mBAAS,EAAE,IAAIA,KAAI;AACnB,kBAAQ,EAAE,IAAI,CAAC;AACf,cAAI,MAAM,OAAO,GAAG;AAClB,yBAAa,IAAI,CAAC;AAClB,aAAC,KAAK,WAAW,MAAM,OAAO,SAAS,GAAG,KAAK,YAAY,CAAC;AAC5D,iCAAqB,CAAC;AAAA,UACxB;AAAA,QACF;AAAA,MACF;AACA,iBAAW,KAAK,QAAQ,KAAK,CAAC,GAAG;AAC/B,YAAI,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG;AACvB,kBAAQ,EAAE,OAAO,CAAC;AAClB,gBAAM,WAAW,YAAY,CAAC;AAC9B,sBAAY,OAAO,SAAS,SAAS,EAAE,OAAOA,KAAI;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,YAAY,uBAAuB,CAAC,MAAM,CAACA,UAAS;AACxD,QAAI;AACJ,UAAM,YAAY,gBAAgBA,KAAI;AACtC,QAAI,UAAU,WAAW,IAAIA,KAAI;AACjC,QAAI,CAAC,SAAS;AACZ,oBAAcA,KAAI;AAClB,iBAAW,KAAK,UAAU,EAAE,KAAK,GAAG;AAClC,cAAM,WAAW,UAAU,CAAC;AAC5B,iBAAS,EAAE,IAAIA,KAAI;AAAA,MACrB;AACA,gBAAU;AAAA,QACR,GAAmB,oBAAI,IAAI;AAAA,QAC3B,GAAG,IAAI,IAAI,UAAU,EAAE,KAAK,CAAC;AAAA,QAC7B,GAAmB,oBAAI,IAAI;AAAA,MAC7B;AACA,iBAAW,IAAIA,OAAM,OAAO;AAC5B,OAAC,KAAK,WAAW,MAAM,OAAO,SAAS,GAAG,KAAK,YAAYA,KAAI;AAC/D,UAAI,uBAAuBA,KAAI,GAAG;AAChC,cAAM,iBAAiB,MAAM;AAC3B,cAAI,SAAS;AACb,gBAAM,UAAU,IAAI,SAAS;AAC3B,gBAAI;AACF,qBAAO,eAAeA,OAAM,GAAG,IAAI;AAAA,YACrC,UAAE;AACA,kBAAI,CAAC,QAAQ;AACX,0CAA0B;AAC1B,+BAAe;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AACA,cAAI;AACF,kBAAM,YAAY,YAAYA,OAAM,OAAO;AAC3C,gBAAI,WAAW;AACb,sBAAQ,IAAI,MAAM;AAChB,yBAAS;AACT,oBAAI;AACF,4BAAU;AAAA,gBACZ,UAAE;AACA,2BAAS;AAAA,gBACX;AAAA,cACF;AAAA,YACF;AAAA,UACF,UAAE;AACA,qBAAS;AAAA,UACX;AAAA,QACF;AACA,uBAAe,IAAI,cAAc;AAAA,MACnC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,cAAc,uBAAuB,CAAC,MAAM,CAACA,UAAS;AAC1D,QAAI;AACJ,UAAM,YAAY,gBAAgBA,KAAI;AACtC,QAAI,UAAU,WAAW,IAAIA,KAAI;AACjC,QAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,EAAE,KAAK,CAAC,MAAM;AACnE,UAAI;AACJ,cAAQ,MAAM,WAAW,IAAI,CAAC,MAAM,OAAO,SAAS,IAAI,EAAE,IAAIA,KAAI;AAAA,IACpE,CAAC,GAAG;AACF,UAAI,QAAQ,GAAG;AACb,yBAAiB,IAAI,QAAQ,CAAC;AAAA,MAChC;AACA,gBAAU;AACV,iBAAW,OAAOA,KAAI;AACtB,OAAC,KAAK,WAAW,MAAM,OAAO,SAAS,GAAG,KAAK,YAAYA,KAAI;AAC/D,iBAAW,KAAK,UAAU,EAAE,KAAK,GAAG;AAClC,cAAM,WAAW,YAAY,CAAC;AAC9B,oBAAY,OAAO,SAAS,SAAS,EAAE,OAAOA,KAAI;AAAA,MACpD;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,iBAAiB;AAAA;AAAA,IAErB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,QAAQ;AAAA,IACZ,KAAK,CAACA,UAAS,gBAAgB,cAAcA,KAAI,CAAC;AAAA,IAClD,KAAK,CAACA,UAAS,SAAS;AACtB,UAAI;AACF,eAAO,eAAeA,OAAM,GAAG,IAAI;AAAA,MACrC,UAAE;AACA,kCAA0B;AAC1B,uBAAe;AAAA,MACjB;AAAA,IACF;AAAA,IACA,KAAK,CAACA,OAAM,aAAa;AACvB,YAAM,UAAU,UAAUA,KAAI;AAC9B,YAAM,YAAY,QAAQ;AAC1B,gBAAU,IAAI,QAAQ;AACtB,qBAAe;AACf,aAAO,MAAM;AACX,kBAAU,OAAO,QAAQ;AACzB,oBAAYA,KAAI;AAChB,uBAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO,eAAe,OAAO,iBAAiB,EAAE,OAAO,eAAe,CAAC;AACvE,SAAO;AACT;AACA,IAAM,0BAA0B;AAEhC,IAAM,gCAAgC;AAStC,IAAM,gCAAgC;;;AC5hBtC,IAAI,WAAW;AACf,SAAS,KAAK,MAAM,OAAO;AACzB,QAAM,MAAM,OAAO,EAAE,QAAQ;AAC7B,QAAM,SAAS;AAAA,IACb,WAAW;AACT,cAAQ,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,KAAK,aAAa,MAAM,MAAM,KAAK,aAAa;AAAA,IAC/H;AAAA,EACF;AACA,MAAI,OAAO,SAAS,YAAY;AAC9B,WAAO,OAAO;AAAA,EAChB,OAAO;AACL,WAAO,OAAO;AACd,WAAO,OAAO;AACd,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,OAAO;AACT,WAAO,QAAQ;AAAA,EACjB;AACA,SAAO;AACT;AACA,SAAS,YAAY,KAAK;AACxB,SAAO,IAAI,IAAI;AACjB;AACA,SAAS,aAAa,KAAK,KAAK,KAAK;AACnC,SAAO;AAAA,IACL;AAAA,IACA,OAAO,QAAQ,aAAa,IAAI,IAAI,IAAI,CAAC,IAAI;AAAA,EAC/C;AACF;AAEA,IAAM,qBAAqB,MAAM;AAC/B,MAAI,gBAAgB;AACpB,QAAM,aAAa,8BAA8B,CAAC,CAAC;AACnD,QAAM,eAA+B,oBAAI,QAAQ;AACjD,QAAM,eAA+B,oBAAI,QAAQ;AACjD,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAACC,OAAM,KAAK,QAAQ,SAAS;AAC3B,UAAI,eAAe;AACjB,eAAO,IAAIA,OAAM,GAAG,IAAI;AAAA,MAC1B;AACA,aAAOA,MAAK,MAAM,KAAK,KAAK,GAAG,IAAI;AAAA,IACrC;AAAA,EACF;AACA,QAAM,oBAAoC,oBAAI,IAAI;AAClD,aAAW,EAAE,IAAI,QAAQ,CAACA,UAAS;AACjC,sBAAkB,IAAIA,KAAI;AAC1B,UAAM,YAAY,aAAa,IAAIA,KAAI;AACvC,cAAU,IAAI,aAAa,IAAIA,KAAI;AAAA,EACrC,CAAC;AACD,aAAW,EAAE,IAAI,QAAQ,CAACA,UAAS;AACjC,sBAAkB,OAAOA,KAAI;AAC7B,UAAM,YAAY,aAAa,IAAIA,KAAI;AACvC,WAAO,UAAU;AAAA,EACnB,CAAC;AACD,QAAM,WAAW;AAAA;AAAA,IAEf,4BAA4B,MAAM;AAChC,cAAQ,IAAI,oDAAoD;AAChE,aAAO;AAAA,IACT;AAAA,IACA,wBAAwB,MAAM;AAAA,IAC9B,oBAAoB,CAAC,WAAW;AAC9B,YAAM,cAAc;AAAA,QAClB,MAAM,MAAM;AAAA,QACZ,OAAO,CAAC,MAAM,QAAQ;AACpB,YAAE;AACF,cAAI;AACF,uBAAW,CAACA,OAAM,KAAK,KAAK,QAAQ;AAClC,kBAAI,UAAUA,OAAM;AAClB,oBAAIA,OAAM,KAAK;AAAA,cACjB;AAAA,YACF;AAAA,UACF,UAAE;AACA,cAAE;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AACA,YAAM,IAAI,WAAW;AAAA,IACvB;AAAA,EACF;AACA,SAAO,OAAO,OAAO,OAAO,QAAQ;AACtC;AACA,IAAI;AAIJ,SAAS,cAAc;AACrB,MAAI,sBAAsB;AACxB,WAAO,qBAAqB;AAAA,EAC9B;AACA,OAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,cAAc;AACtE,WAAO,mBAAmB;AAAA,EAC5B;AACA,SAAO,wBAAwB;AACjC;AACA,IAAI;AACJ,SAAS,kBAAkB;AACzB,MAAI,CAAC,cAAc;AACjB,mBAAe,YAAY;AAC3B,SAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,cAAc;AACtE,iBAAW,4BAA4B,WAAW,0BAA0B;AAC5E,UAAI,WAAW,4BAA4B,cAAc;AACvD,gBAAQ;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACtHA,mBAA2H;AAI3H,IAAM,mBAAe;AAAA,EACnB;AACF;AACA,SAAS,SAAS,SAAS;AACzB,QAAM,YAAQ,yBAAW,YAAY;AACrC,UAAQ,WAAW,OAAO,SAAS,QAAQ,UAAU,SAAS,gBAAgB;AAChF;AACA,SAAS,SAAS;AAAA,EAChB;AAAA,EACA;AACF,GAAG;AACD,QAAM,eAAW,qBAAO,MAAM;AAC9B,MAAI,CAAC,SAAS,CAAC,SAAS,SAAS;AAC/B,aAAS,UAAU,YAAY;AAAA,EACjC;AACA,aAAO;AAAA,IACL,aAAa;AAAA,IACb;AAAA,MACE,OAAO,SAAS,SAAS;AAAA,IAC3B;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAMC,iBAAgB,CAAC,MAAM,QAAQ,KAAK,OAAO,SAAS,EAAE,UAAU;AACtE,IAAM,sBAAsB,CAAC,YAAY;AACvC,MAAI,CAAC,QAAQ,QAAQ;AACnB,YAAQ,SAAS;AACjB,YAAQ;AAAA,MACN,CAAC,MAAM;AACL,gBAAQ,SAAS;AACjB,gBAAQ,QAAQ;AAAA,MAClB;AAAA,MACA,CAAC,MAAM;AACL,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,MAAM,aAAAC,QAAM;AAAA,CACjB,CAAC,YAAY;AACZ,MAAI,QAAQ,WAAW,WAAW;AAChC,UAAM;AAAA,EACR,WAAW,QAAQ,WAAW,aAAa;AACzC,WAAO,QAAQ;AAAA,EACjB,WAAW,QAAQ,WAAW,YAAY;AACxC,UAAM,QAAQ;AAAA,EAChB,OAAO;AACL,wBAAoB,OAAO;AAC3B,UAAM;AAAA,EACR;AACF;AACA,IAAM,wBAAwC,oBAAI,QAAQ;AAC1D,IAAM,2BAA2B,CAAC,SAAS,aAAa;AACtD,MAAI,qBAAqB,sBAAsB,IAAI,OAAO;AAC1D,MAAI,CAAC,oBAAoB;AACvB,yBAAqB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpD,UAAI,OAAO;AACX,YAAM,cAAc,CAAC,OAAO,CAAC,MAAM;AACjC,YAAI,SAAS,IAAI;AACf,kBAAQ,CAAC;AAAA,QACX;AAAA,MACF;AACA,YAAM,aAAa,CAAC,OAAO,CAAC,MAAM;AAChC,YAAI,SAAS,IAAI;AACf,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AACA,YAAM,UAAU,MAAM;AACpB,YAAI;AACF,gBAAM,YAAY,SAAS;AAC3B,cAAID,eAAc,SAAS,GAAG;AAC5B,kCAAsB,IAAI,WAAW,kBAAkB;AACvD,mBAAO;AACP,sBAAU,KAAK,YAAY,SAAS,GAAG,WAAW,SAAS,CAAC;AAC5D,0CAA8B,WAAW,OAAO;AAAA,UAClD,OAAO;AACL,oBAAQ,SAAS;AAAA,UACnB;AAAA,QACF,SAAS,GAAG;AACV,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AACA,cAAQ,KAAK,YAAY,OAAO,GAAG,WAAW,OAAO,CAAC;AACtD,oCAA8B,SAAS,OAAO;AAAA,IAChD,CAAC;AACD,0BAAsB,IAAI,SAAS,kBAAkB;AAAA,EACvD;AACA,SAAO;AACT;AACA,SAAS,aAAaE,OAAM,SAAS;AACnC,QAAM,EAAE,OAAO,wBAAwB,gBAAgB,CAAC,aAAAD,QAAM,IAAI,IAAI,WAAW,CAAC;AAClF,QAAM,QAAQ,SAAS,OAAO;AAC9B,QAAM,CAAC,CAAC,kBAAkB,kBAAkB,eAAe,GAAG,QAAQ,QAAI;AAAA,IACxE,CAAC,SAAS;AACR,YAAM,YAAY,MAAM,IAAIC,KAAI;AAChC,UAAI,OAAO,GAAG,KAAK,CAAC,GAAG,SAAS,KAAK,KAAK,CAAC,MAAM,SAAS,KAAK,CAAC,MAAMA,OAAM;AAC1E,eAAO;AAAA,MACT;AACA,aAAO,CAAC,WAAW,OAAOA,KAAI;AAAA,IAChC;AAAA,IACA;AAAA,IACA,MAAM,CAAC,MAAM,IAAIA,KAAI,GAAG,OAAOA,KAAI;AAAA,EACrC;AACA,MAAI,QAAQ;AACZ,MAAI,qBAAqB,SAAS,oBAAoBA,OAAM;AAC1D,aAAS;AACT,YAAQ,MAAM,IAAIA,KAAI;AAAA,EACxB;AACA,8BAAU,MAAM;AACd,UAAM,QAAQ,MAAM,IAAIA,OAAM,MAAM;AAClC,UAAI,eAAe;AACjB,YAAI;AACF,gBAAM,SAAS,MAAM,IAAIA,KAAI;AAC7B,cAAIF,eAAc,MAAM,GAAG;AACzB;AAAA,cACE,yBAAyB,QAAQ,MAAM,MAAM,IAAIE,KAAI,CAAC;AAAA,YACxD;AAAA,UACF;AAAA,QACF,SAAS,GAAG;AAAA,QACZ;AAAA,MACF;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,mBAAW,UAAU,KAAK;AAC1B;AAAA,MACF;AACA,eAAS;AAAA,IACX,CAAC;AACD,aAAS;AACT,WAAO;AAAA,EACT,GAAG,CAAC,OAAOA,OAAM,OAAO,aAAa,CAAC;AACtC,kCAAc,KAAK;AACnB,MAAIF,eAAc,KAAK,GAAG;AACxB,UAAM,UAAU,yBAAyB,OAAO,MAAM,MAAM,IAAIE,KAAI,CAAC;AACrE,QAAI,eAAe;AACjB,0BAAoB,OAAO;AAAA,IAC7B;AACA,WAAO,IAAI,OAAO;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,WAAWA,OAAM,SAAS;AACjC,QAAM,QAAQ,SAAS,OAAO;AAC9B,QAAM,cAAU;AAAA,IACd,IAAI,SAAS;AACX,WAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,gBAAgB,EAAE,WAAWA,QAAO;AAC5F,cAAM,IAAI,MAAM,mBAAmB;AAAA,MACrC;AACA,aAAO,MAAM,IAAIA,OAAM,GAAG,IAAI;AAAA,IAChC;AAAA,IACA,CAAC,OAAOA,KAAI;AAAA,EACd;AACA,SAAO;AACT;AAEA,SAAS,QAAQA,OAAM,SAAS;AAC9B,SAAO;AAAA,IACL,aAAaA,OAAM,OAAO;AAAA;AAAA,IAE1B,WAAWA,OAAM,OAAO;AAAA,EAC1B;AACF;;;ACtKA,IAAM,QAAQ;AAAA,GACX,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,eAAe,UAAU;AACjF;AAmBA,SAAS,WAAW,gBAAgB,UAAU;AAC5C,MAAI,eAAe;AACnB,QAAM,QAAwB,oBAAI,IAAI;AACtC,QAAM,YAA4B,oBAAI,IAAI;AAC1C,QAAM,aAAa,CAAC,UAAU;AAC5B,QAAI;AACJ,QAAI,aAAa,QAAQ;AACvB,aAAO,MAAM,IAAI,KAAK;AAAA,IACxB,OAAO;AACL,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO;AAChC,YAAI,SAAS,KAAK,KAAK,GAAG;AACxB,iBAAO;AACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS,QAAQ;AACnB,UAAI,gBAAgB,OAAO,SAAS,aAAa,KAAK,CAAC,GAAG,KAAK,GAAG;AAChE,mBAAW,OAAO,KAAK;AAAA,MACzB,OAAO;AACL,eAAO,KAAK,CAAC;AAAA,MACf;AAAA,IACF;AACA,UAAM,UAAU,eAAe,KAAK;AACpC,UAAM,IAAI,OAAO,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC;AACtC,oBAAgB,UAAU,OAAO,OAAO;AACxC,WAAO;AAAA,EACT;AACA,QAAM,kBAAkB,CAAC,MAAM,OAAOC,UAAS;AAC7C,eAAW,YAAY,WAAW;AAChC,eAAS,EAAE,MAAM,OAAO,MAAAA,MAAK,CAAC;AAAA,IAChC;AAAA,EACF;AACA,aAAW,kBAAkB,CAAC,aAAa;AACzC,cAAU,IAAI,QAAQ;AACtB,WAAO,MAAM;AACX,gBAAU,OAAO,QAAQ;AAAA,IAC3B;AAAA,EACF;AACA,aAAW,YAAY,MAAM,MAAM,KAAK;AACxC,aAAW,SAAS,CAAC,UAAU;AAC7B,QAAI,aAAa,QAAQ;AACvB,UAAI,CAAC,MAAM,IAAI,KAAK,EAAG;AACvB,YAAM,CAACA,KAAI,IAAI,MAAM,IAAI,KAAK;AAC9B,YAAM,OAAO,KAAK;AAClB,sBAAgB,UAAU,OAAOA,KAAI;AAAA,IACvC,OAAO;AACL,iBAAW,CAAC,KAAK,CAACA,KAAI,CAAC,KAAK,OAAO;AACjC,YAAI,SAAS,KAAK,KAAK,GAAG;AACxB,gBAAM,OAAO,GAAG;AAChB,0BAAgB,UAAU,KAAKA,KAAI;AACnC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,aAAW,kBAAkB,CAAC,OAAO;AACnC,mBAAe;AACf,QAAI,CAAC,aAAc;AACnB,eAAW,CAAC,KAAK,CAACA,OAAM,SAAS,CAAC,KAAK,OAAO;AAC5C,UAAI,aAAa,WAAW,GAAG,GAAG;AAChC,cAAM,OAAO,GAAG;AAChB,wBAAgB,UAAU,KAAKA,KAAI;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AA0PA,IAAM,kBAAkB,CAAC,MAAM,QAAQ,KAAK,OAAO,SAAS,EAAE,UAAU;AAsBxE,SAAS,kBAAkB,mBAAmB,MAAM;AAClD,MAAI;AACF,WAAO,OAAO;AAAA,EAChB,SAAS,GAAG;AACV,SAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,cAAc;AACtE,UAAI,OAAO,WAAW,aAAa;AACjC,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF,GAAG,SAAS;AACV,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU;AAAA,IACd,SAAS,CAAC,KAAK,iBAAiB;AAC9B,UAAI,KAAK;AACT,YAAM,QAAQ,CAAC,SAAS;AACtB,eAAO,QAAQ;AACf,YAAI,YAAY,MAAM;AACpB,cAAI;AACF,wBAAY,KAAK,MAAM,MAAM,WAAW,OAAO,SAAS,QAAQ,OAAO;AAAA,UACzE,SAAS,GAAG;AACV,mBAAO;AAAA,UACT;AACA,oBAAU;AAAA,QACZ;AACA,eAAO;AAAA,MACT;AACA,YAAM,OAAO,MAAM,MAAM,iBAAiB,MAAM,OAAO,SAAS,IAAI,QAAQ,GAAG,MAAM,OAAO,KAAK;AACjG,UAAI,gBAAgB,GAAG,GAAG;AACxB,eAAO,IAAI,KAAK,KAAK;AAAA,MACvB;AACA,aAAO,MAAM,GAAG;AAAA,IAClB;AAAA,IACA,SAAS,CAAC,KAAK,aAAa;AAC1B,UAAI;AACJ,cAAQ,MAAM,iBAAiB,MAAM,OAAO,SAAS,IAAI;AAAA,QACvD;AAAA,QACA,KAAK,UAAU,UAAU,WAAW,OAAO,SAAS,QAAQ,QAAQ;AAAA,MACtE;AAAA,IACF;AAAA,IACA,YAAY,CAAC,QAAQ;AACnB,UAAI;AACJ,cAAQ,MAAM,iBAAiB,MAAM,OAAO,SAAS,IAAI,WAAW,GAAG;AAAA,IACzE;AAAA,EACF;AACA,QAAM,wBAAwB,CAAC,gBAAgB,CAAC,KAAK,UAAU,iBAAiB,YAAY,KAAK,CAAC,MAAM;AACtG,QAAI;AACJ,QAAI;AACF,iBAAW,KAAK,MAAM,KAAK,EAAE;AAAA,IAC/B,SAAS,GAAG;AACV,iBAAW;AAAA,IACb;AACA,aAAS,QAAQ;AAAA,EACnB,CAAC;AACD,MAAI;AACJ,MAAI;AACF,kBAAc,KAAK,iBAAiB,MAAM,OAAO,SAAS,GAAG;AAAA,EAC/D,SAAS,GAAG;AAAA,EACZ;AACA,MAAI,CAAC,cAAc,OAAO,WAAW,eAAe,OAAO,OAAO,qBAAqB,cAAc,OAAO,SAAS;AACnH,iBAAa,CAAC,KAAK,aAAa;AAC9B,UAAI,EAAE,iBAAiB,aAAa,OAAO,UAAU;AACnD,eAAO,MAAM;AAAA,QACb;AAAA,MACF;AACA,YAAM,uBAAuB,CAAC,MAAM;AAClC,YAAI,EAAE,gBAAgB,iBAAiB,KAAK,EAAE,QAAQ,KAAK;AACzD,mBAAS,EAAE,QAAQ;AAAA,QACrB;AAAA,MACF;AACA,aAAO,iBAAiB,WAAW,oBAAoB;AACvD,aAAO,MAAM;AACX,eAAO,oBAAoB,WAAW,oBAAoB;AAAA,MAC5D;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY;AACd,YAAQ,YAAY,sBAAsB,UAAU;AAAA,EACtD;AACA,SAAO;AACT;AACA,IAAM,iBAAiB,kBAAkB;;;AC7bzC,IAAAC,gBAAqC;AA2BrC,SAAS,gBAAgB,UAAU,SAAS;AAC1C,QAAM,aAAS;AAAA,IACb,MAAM,KAAK,MAAM,CAAC,KAAK,QAAQ,SAAS,SAAS,KAAK,KAAK,GAAG,IAAI,CAAC;AAAA,IACnE,CAAC,QAAQ;AAAA,EACX;AACA,SAAO,WAAW,QAAQ,OAAO;AACnC;;;AKjBA,IAAM,sCAAe,GAAA,aAAA;AAEd,IAAM,6CAAoB,GAAA,cAAAC,eAAuC,CAAA,CAAA;AAEjE,IAAM,2CAA+D,CAAC,EAAA,UACnE,QACF,aACO,mCAAA,MACd;AACC,UACE,GAAA,mBAAAC,MAAC,GAAA,WAAa;IAAC,OAAO;IAAU,WAC9B,GAAA,mBAAAA,KAAC,0CAAkB,UAAQ;MAAC,OAAO;;MAAQ;MAAE;IAClC,CAAA;EACkB,CAAA;AAGnC;AACA,yCAAmB,cAAc;ADzB1B,IAAM,4CAAgB,MAAA;AAC3B,QAAM,EAAA,OAAQ,KAAK,GAAA,cAAAC,aAAW,GAAA,0CAAA;AAC9B,SAAO;AACT;ADFO,IAAM,4CAAqB,CAChC,OACA,YAAA;AAEA,QAAM,UAAS,GAAA,2CAAA;AAEf,GAAA,GAAA,cAAAC,WAAU,MAAA;AACR,QAAI,CAAC,OAAQ;AACb,WAAO,GAAG,OAAO,OAAA;AACjB,WAAO,MAAA;AACL,aAAO,IAAI,OAAO,OAAA;IACpB;EACF,GAAG;IAAC;IAAO;IAAS;GAAO;AAC7B;AGNA,IAAM,6CAAsB,GAAA,MAA8B,IAAA;AAC1D,IAAM,6CAAsB,GAAA,MAA8B,IAAA;AAC1D,IAAM,mDAA4B,GAAA,MAA8B,IAAA;AAChE,IAAM,mDAA4B,GAAA,MAA8B,IAAA;AAChE,IAAM,2CAAoB,GAAA,MAA8B,IAAA;AACxD,IAAM,2CAAoB,GAAA,MAA8B,IAAA;AAExD,IAAM,mCAAY,GAAA,YAGhB,CAAC,EAAA,OAAO,UAAW,MAAE;AACrB,MAAI,MACF,SAAQ,WAAA;IACN,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;EACX;AAEF,SAAO,cAAc,UAAU,0CAAoB;AACrD,CAAA;AAEO,IAAM,4CAA0B,CACrC,WACA,oBAAA;AAEA,QAAM,UAAS,GAAA,2CAAA;AACf,QAAM,SAAQ,GAAA,cACZ,gCAAU;IAAE,OAAO,oBAAoB;;EAAkB,CAAA,CAAA;AAG3D,QAAM,eAAc,GAAA,kBAClB,GAAA,cAAAC,aACE,CACE,KACA,KACAC,QACAC,YACA,UAAA;AAEA,UAAMC,QAAO,gCAAU;;iBAErBD;IACD,CAAA;AACD,UAAM,WAAW,IAAIC,KAAA;AACrB,SAAI,qCAAU,QAAOF,OAAM,GAAI;AAC/B,QAAIE,OAAMF,MAAA;EACZ,GACA;IAAC;IAAiB;IAAO;GAAU,CAAA;AAIvC,GAAA,GAAA,4CACE,GAAA,2CAAU,eACV,GAAA,cAAAD,aAAY,CAACC,QAAyB,gBAAA;AACpC,gBAAYA,QAAOA,OAAM,MAAmB,QAAQ,2CAAa,KAAA,CAAA;EACnE,GAAG,CAAA,CAAE,CAAA;AAGP,GAAA,GAAA,4CACE,GAAA,2CAAU,qBACV,GAAA,cAAAD,aAAY,CAACC,QAAyB,gBAAA;AACpC,UAAMC,aAAYD,OAAM,SAAS,UAAU,gBAAgB;AAC3D,gBAAYA,QAAOC,YAAW,QAAQ,2CAAa,KAAA,CAAA;EACrD,GAAG,CAAA,CAAE,CAAA;AAGP,GAAA,GAAA,cAAAH,WAAU,MAAA;;AACR,QAAI,CAAC,OAAQ;AACb,UAAM,SAAS,OAAO,OAAM;AAC5B,UAAME,UAAQ,sCAAS,qBAAT,mBAA4B;AAC1C,QAAI,CAACA,OAAO;AACZ,gBAAYA,QAAO,WAAW,oBAAoB,OAAA;EACpD,GAAG;IAAC;IAAiB;IAAW;IAAa;GAAO;AAEpD,SAAO;AACT;AJtFO,IAAM,4CAAkB,MAAA;AAC7B,QAAM,eAAc,GAAA,cAAAG,QAAyB,IAAA;AAC7C,QAAM,iBAAgB,GAAA,2CAAwB,SAAS,KAAA;AAEvD,GAAA,GAAA,cAAAL,WAAU,MAAA;AACR,QAAI,CAAC,YAAY,WAAW,CAAC,cAAe;AAC5C,QAAI,YAAY,QAAQ,WAAW;AACjC,YAAM,WACJ,YAAY,QAAQ,UACpB,eAAc,EAAG,CAAA;AACnB,UAAI,SAAS,OAAO,cAAc,GAAI;IACxC;AACA,gBAAY,QAAQ,YAAY,IAAI,YAAY;MAAC;KAAc;EACjE,GAAG;IAAC;GAAc;AAElB,GAAA,GAAA,4CACE,GAAA,2CAAU,iBACV,GAAA,cAAAC,aAAY,CAAC,YAAA;AACX,QAAI,CAAC,YAAY,QAAS;AAC1B,QAAI,OAAO,YAAY,QAAQ,cAAc,WAAY;AACzD,gBAAY,QAAQ,UAAU,QAAQ,QAAQ;EAChD,GAAG,CAAA,CAAE,CAAA;AAGP,UACE,GAAA,mBAAAH,MAAA,GAAA,mBAAAQ,WAAA;IAAA,WACE,GAAA,mBAAAR,KAAA,SAAA;MAAO,KAAK;MAAa,UAAQ;IAAA,CAAA;EAAG,CAAA;AAG1C;AACA,0CAAgB,cAAc;AM5B9B,SAAS,sCAAmB,MAAoB;AAC9C,UAAO,GAAA,cAAAG;IACL,CAAC,YAAA;AACC,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAM,MAAM,KAAK,CAAA;AACjB,YAAI,OAAO,QAAQ,WAAY,KAAI,OAAA;iBAC1B,OAAO,OAAO,QAAQ,SAC5B,KAAkC,UAAU;MACjD;IACF;;IAEA;EAAA;AAEJ;IAEA,2CAAe;ADaR,IAAM,6CAAkB,GAAA,cAAAM,YAC7B,SAAS,iBACP,EAAA,cACgB,SAAA,MACR,WAAA,QACA,UACE,QACA,CAAA,GAAA,YACI,SACZ,GAAG,MAAA,GAEL,KAAG;AAEH,QAAM,cAAsC,GAAA,2CAC1C,WACA,WAAA;AAGF,QAAM,WAAU,GAAA,cAAAF,QAAyB,IAAA;AACzC,QAAM,YAAW,GAAA,0CAA+B,SAAS,GAAA;AAKzD,GAAA,GAAA,cAAAL,WAAU,SAAS,mBAAA;AACjB,UAAM,QAAQ,QAAQ;AACtB,QAAI,CAAC,MAAO;AAEZ,UAAM,YAAY,MAAA;AAChB,YAAM,UAAU,MAAM,KAAI;AAC1B,UAAI,YAAY,OACd,SACG,KAAK,MAAA;AAEJ,cAAM,WAAW;MACnB,CAAA,EACC,MAAM,CAAC,UAAA;AAEN,cAAM,WAAW;AACjB,gBAAQ,KAAK,wBAAwB,KAAA;MACvC,CAAA;IAEN;AAEA,UAAM,gBAAgB,MAAA;AACpB,UAAI,CAAC,MAAM,OAAQ;AACnB,gBAAA;IACF;AACA,UAAM,iBAAiB,MAAA;AACrB,YAAM,MAAM,YAAY;IAC1B;AACA,UAAM,iBAAiB,MAAA;AACrB,YAAM,MAAM,YAAY;AACxB,iBAAW,MAAA;AACT,YAAI,MAAM,OAAQ,WAAA;MACpB,GAAG,GAAA;IACL;AACA,UAAM,yBAAyB,MAAA;AAC7B,UAAI,SAAS,oBAAoB,SAAU;AAC3C,UAAI,CAAC,MAAM,OAAQ;AACnB,gBAAA;IACF;AACA,UAAM,iBAAiB,WAAW,aAAA;AAClC,UAAM,iBAAiB,yBAAyB,cAAA;AAChD,UAAM,iBAAiB,yBAAyB,cAAA;AAGhD,aAAS,iBAAiB,oBAAoB,sBAAA;AAC9C,WAAO,MAAA;AACL,YAAM,oBAAoB,WAAW,aAAA;AACrC,YAAM,oBAAoB,yBAAyB,cAAA;AACnD,YAAM,oBAAoB,yBAAyB,cAAA;AACnD,eAAS,oBACP,oBACA,sBAAA;IAEJ;EACF,GAAG,CAAA,CAAE;AAKL,GAAA,GAAA,cAAAA,WACE,SAAS,kBAAA;AACP,UAAM,QAAQ,QAAQ;AACtB,QAAI,CAAC,SAAS,CAAC,WAAY;AAC3B,UAAM,YAAY,IAAI,YAAY;MAAC;KAAW;AAC9C,UAAM,KAAI;AACV,WAAO,MAAA;AAEL,YAAM,YAAY;AAClB,YAAM,KAAI;IACZ;EACF,GACA;IAAC;IAAY,yCAAY;GAAG;AAO9B,GAAA,GAAA,cAAAA,WACE,SAAS,wBAAA;AACP,UAAM,QAAQ,QAAQ;AACtB,QAAI,CAAC,YAAY,CAAC,MAAO;AAEzB,QAAI;AACJ,aAAS,eAAA;AACP,UAAI,MAAO,sBAAqB,KAAA;AAChC,cAAQ,sBAAsB,MAAA;AAC5B,cAAMQ,SAAQ,QAAQ;AACtB,YAAI,CAACA,UAAS,SAAS,OAAQ;AAC/B,cAAM,aAAaA,OAAM;AACzB,cAAM,cAAcA,OAAM;AAC1B,YAAI,cAAc,YAChB,sCAAW;UACT,aAAa,aAAa;UAC1B,QAAQ;UACR,OAAO;QACR;MAEL,CAAA;IACF;AAEA,iBAAA;AACA,UAAM,iBAAiB,kBAAkB,YAAA;AACzC,UAAM,iBAAiB,UAAU,YAAA;AAEjC,WAAO,MAAA;AACL,UAAI,MAAO,sBAAqB,KAAA;AAChC,YAAM,oBAAoB,kBAAkB,YAAA;AAC5C,YAAM,oBAAoB,UAAU,YAAA;IACtC;EACF,GACA;IAAC;GAAS;AAGZ,UACE,GAAA,mBAAAV,KAAA,SAAA;IACE,UAAQ;IACR,OAAK;IACL,aAAW;IACX,KAAK;IACL,OAAO;MACL,WAAW;MACX,WAAW,SAAS,iBAAiB;MACrC,GAAG;IACJ;IAAA,GACG;EAAK,CAAA;AAGf,CAAA;AAEF,0CAAgB,cAAc;AEzL9B,IAAM,2CAAoB,GAAA,MAAwB,CAAA,CAAE;AACpD,IAAM,2CAAoB,GAAA,MAAwB,CAAA,CAAE;AACpD,IAAM,+CAAwB,GAAA,MAAwB,CAAA,CAAE;AACxD,IAAM,yCAAkB,GAAA,MAA8B,CAAA,CAAA;AACtD,IAAM,yCAAkB,GAAA,MAA8B,CAAA,CAAA;AACtD,IAAM,6CAAsB,GAAA,MAA8B,CAAA,CAAA;AAEnD,IAAM,4CAA4B,MAAA;AACvC,QAAM,UAAS,GAAA,2CAAA;AAEf,QAAM,iBAAgB,GAAA,cAAa,uCAAA;AACnC,QAAM,iBAAgB,GAAA,cAAa,uCAAA;AACnC,QAAM,qBAAoB,GAAA,cAAa,2CAAA;AACvC,QAAM,eAAc,GAAA,cAAa,qCAAA;AACjC,QAAM,eAAc,GAAA,cAAa,qCAAA;AACjC,QAAM,mBAAkB,GAAA,cAAa,yCAAA;AAErC,GAAA,GAAA,4CACE,GAAA,2CAAU,uBACV,GAAA,kBACE,GAAA,cAAAG,aAAY,CAAC,MAAM,KAAK,SAAA;AACtB,QAAI,yCAAmB,IAAA;EACzB,GAAG,CAAA,CAAE,CAAA,CAAA;AAGT,GAAA,GAAA,4CACE,GAAA,2CAAU,uBACV,GAAA,kBACE,GAAA,cAAAA,aAAY,CAAC,MAAM,KAAK,SAAA;AACtB,QAAI,yCAAmB,IAAA;EACzB,GAAG,CAAA,CAAE,CAAA,CAAA;AAGT,GAAA,GAAA,4CACE,GAAA,2CAAU,2BACV,GAAA,kBACE,GAAA,cAAAA,aAAY,CAAC,MAAM,KAAK,aAAA;AACtB,QAAI,6CAAuB,QAAA;EAC7B,GAAG,CAAA,CAAE,CAAA,CAAA;AAGT,GAAA,GAAA,4CACE,GAAA,2CAAU,aACV,GAAA,kBACE,GAAA,cAAAA,aAAY,CAAC,MAAM,KAAK,QAAA;AACtB,QAAI,uCAAiB,GAAA;EACvB,GAAG,CAAA,CAAE,CAAA,CAAA;AAGT,GAAA,GAAA,4CACE,GAAA,2CAAU,aACV,GAAA,kBACE,GAAA,cAAAA,aAAY,CAAC,MAAM,KAAK,QAAA;AACtB,QAAI,uCAAiB,GAAA;EACvB,GAAG,CAAA,CAAE,CAAA,CAAA;AAGT,GAAA,GAAA,4CACE,GAAA,2CAAU,iBACV,GAAA,kBACE,GAAA,cAAAA,aAAY,CAAC,MAAM,KAAK,YAAA;AACtB,QAAI,2CAAqB,OAAA;EAC3B,GAAG,CAAA,CAAE,CAAA,CAAA;AAIT,QAAM,aAAY,GAAA,cAAAA,aAChB,CAAC,OAAA;AACC,qCAAQ,UAAU;EACpB,GACA;IAAC;GAAO;AAEV,QAAM,aAAY,GAAA,cAAAA,aAChB,CAAC,OAAA;AACC,qCAAQ,UAAU;EACpB,GACA;IAAC;GAAO;AAEV,QAAM,iBAAgB,GAAA,cAAAA,aACpB,CAAC,OAAA;AACC,qCAAQ,cAAc;EACxB,GACA;IAAC;GAAO;AAGV,SAAO;;;;;;;;;;EAUN;AACH;AC/FA,IAAM,4CAAqB,GAAA,MAAqB,cAAA;AAEzC,IAAM,4CAA8B,MAAA;AACzC,QAAM,CAAC,gBAAgB,iBAAA,KAAqB,GAAA,SAAQ,wCAAA;AAEpD,GAAA,GAAA,4CAAmB,GAAA,2CAAU,uBAAuB,iBAAA;AAEpD,SAAO;AACT;ACEO,IAAM,6CAAmC,GAAA,cAAAQ,SAAM,KACpD,CAAC,EAAA,kBACmB,eAAA,WACP,SAAA,WACA,IAAA,SACF,IAAA,eACM,KAAA,gBACA,MAChB;AACC,QAAM,aAAY,GAAA,cAAAJ,QAA0B,IAAA;AAE5C,QAAM,SAAiC,GAAA,2CACrC,SACA,eAAA;AAGF,GAAA,GAAA,cAAAL,WAAU,MAAA;AACR,QAAI,CAAC,UAAU,QAAS;AAExB,UAAM,cAAc,IAAI,WAAW,IAAI;AACvC,UAAM,eAAe;AAErB,UAAM,SAAS,UAAU;AAEzB,UAAM,cAAc;AAGpB,UAAM,eAAe,MAAA;AACnB,aAAO,QAAQ,cAAc;AAC7B,aAAO,SAAS,eAAe;AAE/B,aAAO,MAAM,QAAQ,GAAG,WAAA;AACxB,aAAO,MAAM,SAAS,GAAG,YAAA;AAEzB,gBAAU,UAAU;AACpB,gBAAU,MAAM,aAAa,WAAA;IAC/B;AAEA,UAAM,YAAY,OAAO,WAAW,IAAA;AACpC,iBAAA;AAEA,QAAI,CAAC,MAAO;AAEZ,UAAM,eAAe,IAAI,aAAA;AACzB,UAAM,SAAS,aAAa,wBAC1B,IAAI,YAAY;MAAC;KAAM,CAAA;AAEzB,UAAM,WAAW,aAAa,eAAc;AAE5C,aAAS,UAAU;AAEnB,WAAO,QAAQ,QAAA;AAEf,UAAM,gBAAgB,IAAI,WAAW,SAAS,iBAAiB;AAE/D,cAAU,UAAU;AAEpB,UAAM,QAAQ;MACZ;QAAE,WAAW;QAAI,SAAS;QAAK,aAAa;MAAC;MAC7C;QAAE,WAAW;QAAK,SAAS;QAAK,aAAa;MAAC;MAC9C;QAAE,WAAW;QAAK,SAAS;QAAM,aAAa;MAAC;MAC/C;QAAE,WAAW;QAAM,SAAS;QAAM,aAAa;MAAC;MAChD;QAAE,WAAW;QAAM,SAAS;QAAM,aAAa;MAAC;;AAGlD,UAAM,uBAAuB,CAAC,cAAA;AAC5B,YAAM,UAAU,aAAa,aAAa;AAC1C,aAAO,KAAK,MACT,YAAY,WAAY,SAAS,oBAAoB,EAAA;IAE1D;AAEA,aAAS,eAAA;AACP,eAAS,qBAAqB,aAAA;AAC9B,gBAAU,UACR,GACA,GACA,OAAO,QAAQ,aACf,OAAO,SAAS,WAAA;AAElB,gBAAU,YAAY;AACtB,gBAAU,SACR,GACA,GACA,OAAO,QAAQ,aACf,OAAO,SAAS,WAAA;AAGlB,UAAI,WAAW;AAEf,YAAM,iBACJ,MAAM,SAAS,YAAY,MAAM,SAAS,KAAK;AACjD,YAAM,UAAU,OAAO,QAAQ,cAAc,kBAAkB;AAE/D,YAAM,uBAAuB,WAAW;AAExC,YAAM,QAAQ,CAAC,MAAM,MAAA;AACnB,cAAM,aAAa,qBAAqB,KAAK,SAAS;AACtD,cAAM,WAAW,qBAAqB,KAAK,OAAO;AAClD,cAAM,WAAW,cAAc,MAAM,YAAY,QAAA;AACjD,cAAM,YACJ,SAAS,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,CAAA,IAAK,SAAS;AAEzD,cAAM,kBAAkB;AAExB,YAAI,YAAY,EACd,MAAK,cAAc,KAAK,IACtB,KAAK,cAAc,kBAAkB,GACrC,CAAA;aAEG;AACL,eAAK,cACH,KAAK,eACJ,YAAY,KAAK,eAAe;AACnC,qBAAW;QACb;AAEA,cAAM,IAAI,SAAS,KAAK,WAAW;AAEnC,cAAM,YAAY,KAAK,IACpB,KAAK,cAAc,MAAO,cAC3B,YAAA;AAGF,cAAM,OAAO,KAAK,IAChB,OAAO,SAAS,cAAc,IAAI,YAAY,GAC9C,oBAAA;AAEF,cAAM,UAAU,KAAK,IACnB,OAAO,SAAS,cAAc,IAAI,YAAY,GAC9C,OAAO,SAAS,cAAc,oBAAA;AAGhC,YAAI,KAAK,cAAc,GAAG;AACxB,oBAAU,UAAS;AACnB,oBAAU,OAAO,IAAI,WAAW,GAAG,IAAA;AACnC,oBAAU,OAAO,IAAI,WAAW,GAAG,OAAA;AACnC,oBAAU,YAAY;AACtB,oBAAU,cAAc;AACxB,oBAAU,OAAM;QAClB,OAAO;AACL,oBAAU,UAAS;AACnB,oBAAU,IACR,IAAI,WAAW,GACf,OAAO,SAAS,cAAc,GAC9B,sBACA,GACA,IAAI,KAAK,EAAE;AAEb,oBAAU,YAAY;AACtB,oBAAU,KAAI;AACd,oBAAU,UAAS;QACrB;MACF,CAAA;AAEA,UAAI,CAAC,SACH,qBAAoB,sBAAsB,QAAA;AAG5C,4BAAsB,YAAA;IACxB;AAEA,aAAS,oBAAoB,cAAsB,OAAa;AAC9D,YAAM,iBACJ,MAAM,SAAS,YAAY,MAAM,SAAS,KAAK;AACjD,YAAM,UAAU,OAAO,QAAQ,cAAc,kBAAkB;AAC/D,YAAM,IAAI,OAAO,SAAS,cAAc;AAExC,YAAM,QAAQ,CAAC,GAAG,MAAA;AAChB,cAAM,IAAI,SAAS,KAAK,WAAW;AAEnC,kBAAU,UAAS;AACnB,kBAAU,IAAI,IAAI,WAAW,GAAG,GAAG,cAAc,GAAG,IAAI,KAAK,EAAE;AAC/D,kBAAU,YAAY;AACtB,kBAAU,KAAI;AACd,kBAAU,UAAS;MACrB,CAAA;IACF;AAEA,iBAAA;AAGA,WAAO,iBAAiB,UAAU,YAAA;AAElC,WAAO,MAAA;AACL,mBAAa,MAAK;AAClB,aAAO,oBAAoB,UAAU,YAAA;IACvC;EACF,GAAG;IAAC;IAAiB;IAAU;IAAQ;IAAc;IAAU;GAAM;AAErE,UACE,GAAA,mBAAAF,KAAA,UAAA;IACE,KAAK;IACL,OAAO;MACL,SAAS;MACT,OAAO;MACP,QAAQ;IACT;EAAA,CAAA;AAGP,CAAA;AAGF,0CAAgB,cAAc;", "names": ["atom", "atom", "atom", "isPromiseLike", "React", "atom", "atom", "import_react", "$h9lXz$createContext", "$h9lXz$jsx", "$h9lXz$useContext", "$h9lXz$useEffect", "$h9lXz$useCallback", "track", "trackType", "atom", "$h9lXz$useRef", "$h9lXz$Fragment", "$h9lXz$forwardRef", "video", "$h9lXz$react"]}