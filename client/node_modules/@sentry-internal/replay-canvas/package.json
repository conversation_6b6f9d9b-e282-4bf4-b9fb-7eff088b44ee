{"name": "@sentry-internal/replay-canvas", "version": "8.55.0", "description": "Replay canvas integration", "main": "build/npm/cjs/index.js", "module": "build/npm/esm/index.js", "types": "build/npm/types/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./build/npm/types/index.d.ts", "default": "./build/npm/esm/index.js"}, "require": {"types": "./build/npm/types/index.d.ts", "default": "./build/npm/cjs/index.js"}}}, "typesVersions": {"<4.9": {"build/npm/types/index.d.ts": ["build/npm/types-ts3.8/index.d.ts"]}}, "files": ["/build/npm"], "sideEffects": false, "scripts": {"build": "run-p build:transpile build:types build:bundle", "build:transpile": "rollup -c rollup.npm.config.mjs", "build:bundle": "rollup -c rollup.bundle.config.mjs", "build:dev": "run-p build:transpile build:types", "build:types": "run-s build:types:core build:types:downlevel", "build:types:core": "tsc -p tsconfig.types.json", "build:types:downlevel": "yarn downlevel-dts build/npm/types build/npm/types-ts3.8 --to ts3.8", "build:watch": "run-p build:transpile:watch build:bundle:watch build:types:watch", "build:dev:watch": "run-p build:transpile:watch build:types:watch", "build:transpile:watch": "yarn build:transpile --watch", "build:bundle:watch": "yarn build:bundle --watch", "build:types:watch": "tsc -p tsconfig.types.json --watch", "build:tarball": "npm pack", "circularDepCheck": "madge --circular src/index.ts", "clean": "rimraf build sentry-replay-*.tgz", "fix": "eslint . --format stylish --fix", "lint": "eslint . --format stylish", "test": "vitest run", "test:watch": "vitest --watch", "yalc:publish": "yalc publish --push --sig"}, "publishConfig": {"access": "public", "tag": "v8"}, "repository": {"type": "git", "url": "git+https://github.com/getsentry/sentry-javascript.git"}, "author": "Sentry", "license": "MIT", "bugs": {"url": "https://github.com/getsentry/sentry-javascript/issues"}, "homepage": "https://docs.sentry.io/platforms/javascript/session-replay/", "devDependencies": {"@sentry-internal/rrweb": "2.31.0"}, "dependencies": {"@sentry-internal/replay": "8.55.0", "@sentry/core": "8.55.0"}, "engines": {"node": ">=14.18"}, "volta": {"extends": "../../package.json"}}