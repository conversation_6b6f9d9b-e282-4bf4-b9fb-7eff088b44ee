{"name": "@vitest/expect", "type": "module", "version": "1.6.1", "description": "Jest's expect matchers as a Chai plugin", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/expect#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/expect"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": false, "exports": {".": {"types": "./index.d.ts", "default": "./dist/index.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./index.d.ts", "files": ["*.d.ts", "dist"], "dependencies": {"chai": "^4.3.10", "@vitest/utils": "1.6.1", "@vitest/spy": "1.6.1"}, "devDependencies": {"@types/chai": "4.3.6", "picocolors": "^1.0.0", "rollup-plugin-copy": "^3.5.0", "@vitest/runner": "1.6.1"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c --watch"}}