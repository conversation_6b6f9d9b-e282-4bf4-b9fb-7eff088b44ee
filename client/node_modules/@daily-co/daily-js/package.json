{"name": "@daily-co/daily-js", "version": "0.77.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10.0.0"}, "homepage": "https://github.com/daily-co/daily-js/", "main": "dist/daily.js", "module": "dist/daily-esm.js", "types": "index.d.ts", "files": ["dist", "index.d.ts", "README.md", "LICENSE"], "unpkg": "dist/daily.js", "browserslist": ["defaults", "chrome 61", "ie >= 11"], "scripts": {"dev": "NODE_ENV=development npx webpack-dev-server --progress -c webpack.config.js", "analyze": "ANALYZE=true npm run build-main", "build": "npm run build-main && npm run build-module", "build-dev": "NODE_ENV=development npm run build", "build-main": "webpack", "build-main-dev": "NODE_ENV=development npm run build-main", "build-main-dev-rn": "RN=t npm run build-main-dev", "build-module": "rollup -c", "build-module-dev": "NODE_ENV=development npm run build-module", "tag": "scripts/tag", "prepublishOnly": "npm run build && npm run tag", "test": "jest"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-transform-runtime": "^7.12.10", "@babel/preset-env": "^7.12.11", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-node-resolve": "^15.0.2", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-terser": "^0.4.0", "@semantic-release/git": "^10.0.1", "babel-jest": "^27.4.6", "babel-loader": "^8.0.6", "babel-plugin-rewire": "^1.2.0", "conventional-changelog-conventionalcommits": "^5.0.0", "dotenv": "^16.0.3", "jest": "^27.4.7", "prettier": "^2.5.1", "rollup": "^3.29.5", "semantic-release": "^22.0.12", "ts-jest": "^27.1.2", "webpack": "^5.94.0", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^5.0.1", "webpack-dev-server": "5.1"}, "dependencies": {"@babel/runtime": "^7.12.5", "@sentry/browser": "^8.33.1", "bowser": "^2.8.1", "dequal": "^2.0.3", "events": "^3.1.0"}, "jest": {"verbose": true, "testEnvironment": "jsdom"}, "release": {"branches": [{"name": "main", "channel": "internal", "prerelease": "internal"}, {"name": "daily-js-releases"}], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"type": "*", "release": false}, {"type": "fix", "scope": "daily-js", "release": "patch"}, {"type": "fix", "scope": "dj", "release": "patch"}, {"type": "fix", "scope": "call-machine", "release": "patch"}, {"type": "fix", "scope": "cm", "release": "patch"}, {"type": "feat", "scope": "daily-js", "release": "minor"}, {"type": "feat", "scope": "dj", "release": "minor"}, {"type": "feat", "scope": "call-machine", "release": "minor"}, {"type": "feat", "scope": "cm", "release": "minor"}, {"type": "improvement", "scope": "daily-js", "release": "minor"}, {"type": "improvement", "scope": "dj", "release": "minor"}, {"type": "improvement", "scope": "call-machine", "release": "minor"}, {"type": "improvement", "scope": "cm", "release": "minor"}, {"type": "imp", "scope": "daily-js", "release": "minor"}, {"type": "imp", "scope": "dj", "release": "minor"}, {"type": "imp", "scope": "call-machine", "release": "minor"}, {"type": "imp", "scope": "cm", "release": "minor"}, {"breaking": true, "scope": "daily-js", "release": "minor"}, {"breaking": true, "scope": "call-machine", "release": "minor"}, {"breaking": true, "scope": "dj", "release": "minor"}, {"breaking": true, "scope": "cm", "release": "minor"}]}], ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/git"], "tagFormat": "release-daily-js-${version}"}}