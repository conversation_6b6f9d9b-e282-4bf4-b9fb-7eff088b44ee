#!/usr/bin/env node
/**
 * Test script for the React client.
 *
 * This script tests:
 * - Package dependencies
 * - Build process
 * - Server connectivity
 */

import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✓ ${message}`, 'green');
}

function logError(message) {
  log(`✗ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠ ${message}`, 'yellow');
}

async function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const proc = spawn(command, args, {
      stdio: 'pipe',
      ...options
    });

    let stdout = '';
    let stderr = '';

    proc.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    proc.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    proc.on('close', (code) => {
      resolve({
        code,
        stdout,
        stderr
      });
    });

    proc.on('error', (error) => {
      reject(error);
    });
  });
}

async function testPackageJson() {
  logInfo('Testing package.json...');

  const packagePath = path.join(__dirname, 'package.json');

  if (!fs.existsSync(packagePath)) {
    logError('package.json not found');
    return false;
  }

  try {
    const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

    const requiredDeps = [
      '@pipecat-ai/client-js',
      '@pipecat-ai/client-react',
      '@pipecat-ai/daily-transport',
      'react',
      'react-dom'
    ];

    const missingDeps = requiredDeps.filter(dep => !packageContent.dependencies[dep]);

    if (missingDeps.length > 0) {
      logError(`Missing dependencies: ${missingDeps.join(', ')}`);
      return false;
    }

    logSuccess('package.json is valid');
    return true;
  } catch (error) {
    logError(`Failed to parse package.json: ${error.message}`);
    return false;
  }
}

async function testNodeModules() {
  logInfo('Testing node_modules...');

  const nodeModulesPath = path.join(__dirname, 'node_modules');

  if (!fs.existsSync(nodeModulesPath)) {
    logWarning('node_modules not found. Run "npm install" first.');
    return false;
  }

  logSuccess('node_modules exists');
  return true;
}

async function testDependencyInstall() {
  logInfo('Testing dependency installation...');

  try {
    const result = await runCommand('npm', ['list', '--depth=0'], {
      cwd: __dirname
    });

    if (result.code === 0) {
      logSuccess('All dependencies are installed');
      return true;
    } else {
      logError('Some dependencies are missing or have issues');
      logError(result.stderr);
      return false;
    }
  } catch (error) {
    logError(`Failed to check dependencies: ${error.message}`);
    return false;
  }
}

async function testBuild() {
  logInfo('Testing build process...');

  try {
    const result = await runCommand('npm', ['run', 'build'], {
      cwd: __dirname
    });

    if (result.code === 0) {
      logSuccess('Build completed successfully');

      // Check if dist folder was created
      const distPath = path.join(__dirname, 'dist');
      if (fs.existsSync(distPath)) {
        logSuccess('Build output (dist) folder created');
        return true;
      } else {
        logWarning('Build completed but dist folder not found');
        return false;
      }
    } else {
      logError('Build failed');
      logError(result.stderr);
      return false;
    }
  } catch (error) {
    logError(`Build test failed: ${error.message}`);
    return false;
  }
}

async function testServerConnectivity() {
  logInfo('Testing server connectivity...');

  try {
    const response = await fetch('http://localhost:7860/connect', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      logSuccess('Server is reachable');
      return true;
    } else {
      logWarning(`Server returned status ${response.status}. Make sure the server is running.`);
      return false;
    }
  } catch (error) {
    logWarning('Server is not reachable. Make sure the server is running on port 7860.');
    return false;
  }
}

async function testSourceFiles() {
  logInfo('Testing source files...');

  const requiredFiles = [
    'src/App.jsx',
    'src/App.css',
    'src/main.jsx',
    'index.html',
    'vite.config.js'
  ];

  const missingFiles = requiredFiles.filter(file =>
    !fs.existsSync(path.join(__dirname, file))
  );

  if (missingFiles.length > 0) {
    logError(`Missing source files: ${missingFiles.join(', ')}`);
    return false;
  }

  logSuccess('All required source files exist');
  return true;
}

async function main() {
  log('\n🧪 Running React Client Tests\n', 'cyan');

  const tests = [
    { name: 'Package.json', fn: testPackageJson },
    { name: 'Source Files', fn: testSourceFiles },
    { name: 'Node Modules', fn: testNodeModules },
    { name: 'Dependencies', fn: testDependencyInstall },
    { name: 'Build Process', fn: testBuild },
    { name: 'Server Connectivity', fn: testServerConnectivity }
  ];

  const results = [];

  for (const test of tests) {
    log(`\n--- Running ${test.name} Test ---`, 'yellow');
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
    } catch (error) {
      logError(`${test.name} test failed with exception: ${error.message}`);
      results.push({ name: test.name, passed: false });
    }
  }

  // Summary
  log('\n--- Test Results Summary ---', 'cyan');
  let passed = 0;

  for (const result of results) {
    const status = result.passed ? '✓ PASS' : '✗ FAIL';
    const color = result.passed ? 'green' : 'red';
    log(`${status}: ${result.name}`, color);
    if (result.passed) passed++;
  }

  log(`\nPassed: ${passed}/${results.length} tests`, 'cyan');

  if (passed === results.length) {
    logSuccess('\nAll tests passed! Client is ready to run.');
    process.exit(0);
  } else {
    logError('\nSome tests failed. Please fix the issues before running the client.');
    process.exit(1);
  }
}

// Run tests
main().catch(error => {
  logError(`Test runner failed: ${error.message}`);
  process.exit(1);
});
