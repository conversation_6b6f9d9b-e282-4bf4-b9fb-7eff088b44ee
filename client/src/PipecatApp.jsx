import { useState, useEffect, useCallback } from 'react';
import { RTVIClient, LogLevel } from '@pipecat-ai/client-js';
import { DailyTransport } from '@pipecat-ai/daily-transport';
import { RTV<PERSON>lientProvider, RTVIClientAudio, useRTVIClientTransportState } from '@pipecat-ai/client-react';
import './App.css';

// Configure the RTVI client with extensive logging
const transport = new DailyTransport();
const client = new RTVIClient({
  transport,
  params: {
    baseUrl: 'http://localhost:7860',
    endpoints: {
      connect: '/connect',
    },
  },
  enableMic: true,
  enableCam: false,
  logLevel: LogLevel.DEBUG,
});

// Global event logger for debugging
const logEvent = (source, event, data) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${source}] ${event}:`, data);
};

// Log all events from the client
client.on('*', (event, data) => {
  logEvent('RTVI', event, data);
});

function ChatInterface() {
  const transportState = useRTVIClientTransportState();
  const [logs, setLogs] = useState([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');

  // Add log entry helper
  const addLog = useCallback((type, message, data = null) => {
    const logEntry = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      type,
      message,
      data
    };
    setLogs(prev => [...prev.slice(-49), logEntry]); // Keep last 50 logs
    logEvent('UI', type, { message, data });
  }, []);

  useEffect(() => {
    const newIsConnected = transportState === 'connected';
    setIsConnected(newIsConnected);
    setConnectionStatus(transportState);
    
    addLog('transport', `Transport state: ${transportState}`);
  }, [transportState, addLog]);

  // Listen for RTVI events with detailed logging
  useEffect(() => {
    const handleEvent = (event, data) => {
      addLog('rtvi-event', event, data);
    };

    // Listen for specific important events
    const events = [
      'connected', 'disconnected', 'error', 'participantJoined', 
      'participantLeft', 'trackStarted', 'trackStopped', 'botReady',
      'userStartedSpeaking', 'userStoppedSpeaking', 'botStartedSpeaking', 
      'botStoppedSpeaking'
    ];

    events.forEach(event => {
      client.on(event, (data) => handleEvent(event, data));
    });

    // Catch all other events
    client.on('*', handleEvent);
    
    return () => {
      events.forEach(event => {
        client.off(event, handleEvent);
      });
      client.off('*', handleEvent);
    };
  }, [addLog]);

  const handleConnect = async () => {
    try {
      addLog('action', 'Initiating connection to bot...');
      
      await client.connect();
      
      addLog('action', 'Connection request sent successfully');
    } catch (error) {
      addLog('error', `Connection failed: ${error.message}`, error);
      console.error('Connection error:', error);
    }
  };

  const handleDisconnect = async () => {
    try {
      addLog('action', 'Initiating disconnection...');
      
      await client.disconnect();
      
      addLog('action', 'Disconnection completed');
    } catch (error) {
      addLog('error', `Disconnection failed: ${error.message}`, error);
      console.error('Disconnection error:', error);
    }
  };

  const clearLogs = () => {
    setLogs([]);
    addLog('action', 'Logs cleared');
  };

  return (
    <div className="chat-container">
      <div className="status-bar">
        <div className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
          <span className="status-text">
            {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
          </span>
          <span className="status-detail">({connectionStatus})</span>
        </div>
        
        <div className="controls">
          {!isConnected ? (
            <button onClick={handleConnect} className="connect-btn">
              Connect to Bot
            </button>
          ) : (
            <button onClick={handleDisconnect} className="disconnect-btn">
              Disconnect
            </button>
          )}
          <button onClick={clearLogs} className="clear-btn">
            Clear Logs
          </button>
        </div>
      </div>
      
      <div className="audio-container">
        {isConnected && <RTVIClientAudio />}
        {isConnected && (
          <div className="audio-status">
            🎤 Microphone active - Speak to the bot
          </div>
        )}
      </div>
      
      <div className="log-container">
        <div className="log-header">
          <h3>Event Logs ({logs.length})</h3>
          <small>Real-time debugging information</small>
        </div>
        <div className="logs">
          {logs.map((log) => (
            <div key={log.id} className={`log-entry ${log.type}`}>
              <span className="timestamp">
                {log.timestamp.split('T')[1].split('.')[0]}
              </span>
              <span className="log-type">[{log.type.toUpperCase()}]</span>
              <span className="message">
                {log.message}
                {log.data && (
                  <details className="log-data">
                    <summary>Data</summary>
                    <pre>{JSON.stringify(log.data, null, 2)}</pre>
                  </details>
                )}
              </span>
            </div>
          ))}
          {logs.length === 0 && (
            <div className="no-logs">No logs yet. Click "Connect to Bot" to start.</div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function PipecatApp() {
  return (
    <RTVIClientProvider client={client}>
      <div className="app">
        <h1>Pipecat Chatbot</h1>
        <p>A simple voice chatbot using Pipecat, Daily, Deepgram, OpenAI, and Azure TTS</p>
        <ChatInterface />
      </div>
    </RTVIClientProvider>
  );
}
