.app {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 800px;
}

.chat-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  border: 1px solid #444;
  border-radius: 8px;
  overflow: hidden;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #333;
  border-bottom: 1px solid #444;
}

.status-indicator {
  padding: 5px 10px;
  border-radius: 4px;
  font-weight: bold;
}

.status-indicator.connected {
  background-color: #4caf50;
  color: white;
}

.status-indicator.disconnected {
  background-color: #f44336;
  color: white;
}

.controls {
  display: flex;
  gap: 10px;
}

.audio-container {
  padding: 20px;
  background-color: #2a2a2a;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.log-container {
  padding: 10px;
  background-color: #1a1a1a;
  border-top: 1px solid #444;
  max-height: 300px;
  overflow-y: auto;
}

.logs {
  display: flex;
  flex-direction: column;
  gap: 5px;
  text-align: left;
  font-family: monospace;
  font-size: 12px;
}

.log-entry {
  padding: 5px;
  border-radius: 4px;
  background-color: #2a2a2a;
}

.log-entry.transport {
  border-left: 3px solid #2196f3;
}

.log-entry.rtvi {
  border-left: 3px solid #ff9800;
}

.log-entry.action {
  border-left: 3px solid #4caf50;
}

.log-entry.error {
  border-left: 3px solid #f44336;
}

.timestamp {
  color: #888;
  margin-right: 10px;
}