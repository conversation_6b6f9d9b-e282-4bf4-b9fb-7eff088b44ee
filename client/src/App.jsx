import { useState, useEffect } from 'react';
import { RTVIClient, LogLevel } from '@pipecat-ai/client-js';
import { DailyTransport } from '@pipecat-ai/daily-transport';
import { RTVIClientProvider, RTVIClientAudio, useRTVIClientTransportState } from '@pipecat-ai/client-react';
import './App.css';

// Configure the RTVI client
const transport = new DailyTransport();
const client = new RTVIClient({
  transport,
  params: {
    baseUrl: 'http://localhost:7860',
    endpoints: {
      connect: '/connect',
    },
  },
  enableMic: true,
  enableCam: false,
  logLevel: LogLevel.DEBUG,
});

// Log all events from the client
client.on('*', (event, data) => {
  console.log(`[RTVI Event] ${event}:`, data);
});

function ChatInterface() {
  const transportState = useRTVIClientTransportState();
  const [logs, setLogs] = useState([]);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    setIsConnected(transportState === 'connected');
    
    // Add transport state change to logs
    setLogs(prev => [...prev, {
      timestamp: new Date().toISOString(),
      type: 'transport',
      message: `Transport state changed to: ${transportState}`
    }]);
  }, [transportState]);

  // Listen for RTVI events
  useEffect(() => {
    const handleEvent = (event, data) => {
      setLogs(prev => [...prev, {
        timestamp: new Date().toISOString(),
        type: 'rtvi',
        event,
        data
      }]);
    };

    client.on('*', handleEvent);
    
    return () => {
      client.off('*', handleEvent);
    };
  }, []);

  const handleConnect = async () => {
    try {
      setLogs(prev => [...prev, {
        timestamp: new Date().toISOString(),
        type: 'action',
        message: 'Connecting to bot...'
      }]);
      
      await client.connect();
      
      setLogs(prev => [...prev, {
        timestamp: new Date().toISOString(),
        type: 'action',
        message: 'Connected successfully'
      }]);
    } catch (error) {
      setLogs(prev => [...prev, {
        timestamp: new Date().toISOString(),
        type: 'error',
        message: `Connection error: ${error.message}`
      }]);
      console.error('Connection error:', error);
    }
  };

  const handleDisconnect = async () => {
    try {
      setLogs(prev => [...prev, {
        timestamp: new Date().toISOString(),
        type: 'action',
        message: 'Disconnecting from bot...'
      }]);
      
      await client.disconnect();
      
      setLogs(prev => [...prev, {
        timestamp: new Date().toISOString(),
        type: 'action',
        message: 'Disconnected successfully'
      }]);
    } catch (error) {
      setLogs(prev => [...prev, {
        timestamp: new Date().toISOString(),
        type: 'error',
        message: `Disconnection error: ${error.message}`
      }]);
      console.error('Disconnection error:', error);
    }
  };

  return (
    <div className="chat-container">
      <div className="status-bar">
        <div className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
          {isConnected ? 'Connected' : 'Disconnected'}
        </div>
        
        <div className="controls">
          {!isConnected ? (
            <button onClick={handleConnect}>Connect</button>
          ) : (
            <button onClick={handleDisconnect}>Disconnect</button>
          )}
        </div>
      </div>
      
      <div className="audio-container">
        {isConnected && <RTVIClientAudio />}
      </div>
      
      <div className="log-container">
        <h3>Event Logs</h3>
        <div className="logs">
          {logs.map((log, index) => (
            <div key={index} className={`log-entry ${log.type}`}>
              <span className="timestamp">{log.timestamp.split('T')[1].split('.')[0]}</span>
              {log.type === 'rtvi' ? (
                <span className="message">
                  <strong>{log.event}</strong>: {JSON.stringify(log.data)}
                </span>
              ) : (
                <span className="message">{log.message}</span>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default function App() {
  return (
    <RTVIClientProvider client={client}>
      <div className="app">
        <h1>Pipecat Chatbot</h1>
        <p>A simple voice chatbot using Pipecat, Daily, Deepgram, OpenAI, and Azure TTS</p>
        <ChatInterface />
      </div>
    </RTVIClientProvider>
  );
}