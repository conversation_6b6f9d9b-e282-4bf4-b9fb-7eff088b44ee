import { useState, Suspense, lazy } from 'react';
import './App.css';

// Lazy load the Pipecat app to handle potential import errors
const PipecatApp = lazy(() => import('./PipecatApp.jsx'));

// Simple test version to verify React is working
function SimpleTestApp({ onSwitchMode }) {
  const [connected, setConnected] = useState(false);
  const [logs, setLogs] = useState([]);

  const addLog = (message) => {
    const timestamp = new Date().toISOString();
    setLogs(prev => [...prev.slice(-9), { id: Date.now(), timestamp, message }]);
  };

  const testConnection = async () => {
    addLog('Testing server connection...');
    try {
      const response = await fetch('http://localhost:7860/connect', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const data = await response.json();
        addLog(`✅ Server connected! Room: ${data.room_url}`);
        setConnected(true);
      } else {
        addLog(`❌ Server error: ${response.status}`);
      }
    } catch (error) {
      addLog(`❌ Connection failed: ${error.message}`);
    }
  };

  return (
    <div className="app">
      <h1>Pipecat Chatbot - Test Mode</h1>
      <p>Testing basic React app and server connectivity</p>

      <div className="chat-container">
        <div className="status-bar">
          <div className="status-indicator">
            <span className="status-text">
              {connected ? '🟢 Connected' : '🔴 Disconnected'}
            </span>
          </div>

          <div className="controls">
            <button onClick={testConnection} className="connect-btn">
              Test Server Connection
            </button>
            <button onClick={() => setLogs([])} className="clear-btn">
              Clear Logs
            </button>
            {connected && (
              <button onClick={onSwitchMode} className="connect-btn">
                Switch to Full Mode
              </button>
            )}
          </div>
        </div>

        <div className="log-container">
          <div className="log-header">
            <h3>Test Logs ({logs.length})</h3>
            <small>Basic connectivity testing</small>
          </div>
          <div className="logs">
            {logs.map((log) => (
              <div key={log.id} className="log-entry action">
                <span className="timestamp">
                  {log.timestamp.split('T')[1].split('.')[0]}
                </span>
                <span className="message">{log.message}</span>
              </div>
            ))}
            {logs.length === 0 && (
              <div className="no-logs">Click "Test Server Connection" to start</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function App() {
  const [mode, setMode] = useState('test'); // 'test' or 'full'

  if (mode === 'test') {
    return <SimpleTestApp onSwitchMode={() => setMode('full')} />;
  }

  return (
    <Suspense fallback={
      <div className="app">
        <h1>Loading Pipecat Chatbot...</h1>
        <p>Initializing voice chat components...</p>
      </div>
    }>
      <div>
        <button
          onClick={() => setMode('test')}
          style={{
            position: 'fixed',
            top: '10px',
            right: '10px',
            zIndex: 1000,
            padding: '8px 16px',
            background: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Back to Test Mode
        </button>
        <PipecatApp />
      </div>
    </Suspense>
  );
}

export default App;