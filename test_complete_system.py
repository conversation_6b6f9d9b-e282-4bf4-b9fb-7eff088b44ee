#!/usr/bin/env python3
"""
Complete system test for the Pipecat chatbot.

This script tests the entire system end-to-end:
- Server is running and responding
- <PERSON><PERSON> can connect to server
- <PERSON><PERSON> can be created and join a room
"""

import asyncio
import aiohttp
import sys
from loguru import logger

# Configure logging
logger.remove()
logger.add(sys.stderr, level="INFO", format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan> - <level>{message}</level>")

async def test_server_health():
    """Test that the server is running and healthy."""
    logger.info("Testing server health...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:7860/") as response:
                if response.status == 200 or response.status == 307:  # 307 is redirect
                    logger.success("Server is running and responding")
                    return True
                else:
                    logger.error(f"Server returned unexpected status: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"Failed to connect to server: {e}")
        return False

async def test_rtvi_connect_endpoint():
    """Test the RTVI connect endpoint."""
    logger.info("Testing RTVI connect endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post("http://localhost:7860/connect") as response:
                if response.status == 200:
                    data = await response.json()
                    if "room_url" in data and "token" in data:
                        logger.success("RTVI connect endpoint working correctly")
                        logger.info(f"Room URL: {data['room_url']}")
                        return True, data
                    else:
                        logger.error("RTVI connect response missing required fields")
                        return False, None
                else:
                    logger.error(f"RTVI connect endpoint returned status: {response.status}")
                    return False, None
    except Exception as e:
        logger.error(f"Failed to test RTVI connect endpoint: {e}")
        return False, None

async def test_client_server_integration():
    """Test that the client can reach the server."""
    logger.info("Testing client-server integration...")
    
    try:
        # Test the client's expected server endpoint
        async with aiohttp.ClientSession() as session:
            async with session.post("http://localhost:7860/connect", 
                                  headers={"Content-Type": "application/json"}) as response:
                if response.status == 200:
                    logger.success("Client can successfully connect to server")
                    return True
                else:
                    logger.error(f"Client-server integration test failed with status: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"Client-server integration test failed: {e}")
        return False

async def test_bot_creation():
    """Test that a bot can be created and started."""
    logger.info("Testing bot creation...")
    
    try:
        # This will create a room and start a bot
        async with aiohttp.ClientSession() as session:
            async with session.post("http://localhost:7860/connect") as response:
                if response.status == 200:
                    data = await response.json()
                    logger.success("Bot creation successful")
                    logger.info(f"Bot will join room: {data['room_url']}")
                    return True
                else:
                    logger.error(f"Bot creation failed with status: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"Bot creation test failed: {e}")
        return False

async def main():
    """Run all system tests."""
    logger.info("🚀 Starting Complete System Tests")
    logger.info("=" * 50)
    
    tests = [
        ("Server Health", test_server_health),
        ("RTVI Connect Endpoint", test_rtvi_connect_endpoint),
        ("Client-Server Integration", test_client_server_integration),
        ("Bot Creation", test_bot_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            if test_name == "RTVI Connect Endpoint":
                result, data = await test_func()
                results.append((test_name, result))
            else:
                result = await test_func()
                results.append((test_name, result))
        except Exception as e:
            logger.error(f"{test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 Test Results Summary")
    logger.info("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n📈 Passed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        logger.success("\n🎉 All tests passed! The complete system is working correctly!")
        logger.info("\n🌐 You can now:")
        logger.info("   • Open http://localhost:5173/ to access the React client")
        logger.info("   • Click 'Connect to Bot' to start a conversation")
        logger.info("   • Speak to test the voice interaction")
        logger.info("   • Check the logs for detailed debugging information")
        return 0
    else:
        logger.error("\n❌ Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
